import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import {
  FaKey,
  FaLock,
  FaUser,
  FaRegEnvelope,
  FaPhoneAlt,
  FaBuilding,
  FaFileInvoice,
  FaMapMarkerAlt
} from "react-icons/fa";
import { registerUser, clearError, clearSuccessMessage } from "../../redux/slices/authSlice";

import "./style.css";

const Register = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    username: "",
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    phone: "",
    title: "",
    taxNumber: "",
    taxOffice: "",
    address: "",
    acceptTerms: false
  });

  const [formErrors, setFormErrors] = useState({});

  const { loading, error, isAuthenticated, successMessage } = useSelector((state) => state.auth);

  // Kullanıcı zaten giriş yapmışsa ana sayfaya yönlendir
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Başarılı kayıt sonrası login sayfasına yönlendir
  useEffect(() => {
    if (successMessage) {
      // 3 saniye sonra login sayfasına yönlendir
      const timer = setTimeout(() => {
        dispatch(clearSuccessMessage());
        navigate("/login");
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [successMessage, navigate, dispatch]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });

    // Değişiklik yapıldığında ilgili hata mesajını temizle
    if (formErrors[name]) {
      setFormErrors({
        ...formErrors,
        [name]: ""
      });
    }
  };

  const validateForm = () => {
    const errors = {};

    if (!formData.username.trim()) {
      errors.username = "Kullanıcı adı zorunludur";
    }

    if (!formData.firstName.trim()) {
      errors.firstName = "Ad alanı zorunludur";
    }

    if (!formData.lastName.trim()) {
      errors.lastName = "Soyad alanı zorunludur";
    }

    if (!formData.email.trim()) {
      errors.email = "E-posta alanı zorunludur";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Geçerli bir e-posta adresi girin";
    }

    if (!formData.phone.trim()) {
      errors.phone = "Telefon numarası zorunludur";
    }

    if (!formData.password) {
      errors.password = "Şifre alanı zorunludur";
    } else if (formData.password.length < 6) {
      errors.password = "Şifre en az 6 karakter olmalıdır";
    }

    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Şifreler eşleşmiyor";
    }

    // Opsiyonel alanlar için doğrulama yapmıyoruz
    // title, taxNumber, taxOffice, address

    if (!formData.acceptTerms) {
      errors.acceptTerms = "Şartları ve koşulları kabul etmelisiniz";
    }

    return errors;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Form doğrulama
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // Redux action ile kayıt işlemini başlat
    dispatch(registerUser({
      username: formData.username,
      password: formData.password,
      firstName: formData.firstName,
      lastName: formData.lastName,
      email: formData.email,
      phone: formData.phone,
      title: formData.title || "",
      taxNumber: formData.taxNumber || "",
      taxOffice: formData.taxOffice || "",
      address: formData.address || ""
    }));
  };

  // Hata göstergesi kapatıldığında hata state'ini temizle
  const handleErrorClose = () => {
    dispatch(clearError());
  };

  return (
    <section className="gauto-login-area section_70">
      <Container>
        <Row>
          <Col md={12}>
            <div className="login-box">
              <div className="login-page-heading">
                <FaKey />
                <h3>{t("register_page.singup")}</h3>
              </div>

              {error && (
                <Alert variant="danger" dismissible onClose={handleErrorClose}>
                  {error}
                </Alert>
              )}

              {successMessage && (
                <Alert variant="success">
                  {successMessage}
                </Alert>
              )}

              <form onSubmit={handleSubmit}>
                {/* Form Bölümleri */}
                <div className="form-section">
                  <h4 className="section-title">Hesap Bilgileri</h4>
                  <Row>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="text"
                          placeholder="Kullanıcı Adı"
                          name="username"
                          value={formData.username}
                          onChange={handleChange}
                          required
                        />
                        <FaUser />
                      </div>
                      {formErrors.username && <div className="form-error">{formErrors.username}</div>}
                    </Col>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="email"
                          placeholder={t("register_page.email")}
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                        />
                        <FaRegEnvelope />
                      </div>
                      {formErrors.email && <div className="form-error">{formErrors.email}</div>}
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="password"
                          placeholder={t("register_page.password")}
                          name="password"
                          value={formData.password}
                          onChange={handleChange}
                          required
                        />
                        <FaLock />
                      </div>
                      {formErrors.password && <div className="form-error">{formErrors.password}</div>}
                    </Col>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="password"
                          placeholder={t("register_page.c_password")}
                          name="confirmPassword"
                          value={formData.confirmPassword}
                          onChange={handleChange}
                          required
                        />
                        <FaLock />
                      </div>
                      {formErrors.confirmPassword && <div className="form-error">{formErrors.confirmPassword}</div>}
                    </Col>
                  </Row>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Kişisel Bilgiler</h4>
                  <Row>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="text"
                          placeholder="Ad"
                          name="firstName"
                          value={formData.firstName}
                          onChange={handleChange}
                          required
                        />
                        <FaUser />
                      </div>
                      {formErrors.firstName && <div className="form-error">{formErrors.firstName}</div>}
                    </Col>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="text"
                          placeholder="Soyad"
                          name="lastName"
                          value={formData.lastName}
                          onChange={handleChange}
                          required
                        />
                        <FaUser />
                      </div>
                      {formErrors.lastName && <div className="form-error">{formErrors.lastName}</div>}
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="tel"
                          placeholder="Telefon"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          required
                        />
                        <FaPhoneAlt />
                      </div>
                      {formErrors.phone && <div className="form-error">{formErrors.phone}</div>}
                    </Col>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="text"
                          placeholder="Ünvan (Opsiyonel)"
                          name="title"
                          value={formData.title}
                          onChange={handleChange}
                        />
                        <FaBuilding />
                      </div>
                    </Col>
                  </Row>
                </div>

                <div className="form-section">
                  <h4 className="section-title">Vergi ve Adres Bilgileri <small>(Opsiyonel)</small></h4>
                  <Row>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="text"
                          placeholder="Vergi Numarası"
                          name="taxNumber"
                          value={formData.taxNumber}
                          onChange={handleChange}
                        />
                        <FaFileInvoice />
                      </div>
                    </Col>
                    <Col md={6}>
                      <div className="account-form-group">
                        <input
                          type="text"
                          placeholder="Vergi Dairesi"
                          name="taxOffice"
                          value={formData.taxOffice}
                          onChange={handleChange}
                        />
                        <FaBuilding />
                      </div>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={12}>
                      <div className="account-form-group textarea-group">
                        <textarea
                          placeholder="Adres Detayı (Sokak, Mahalle, Bina No, Daire No vb.)"
                          name="address"
                          value={formData.address}
                          onChange={handleChange}
                          rows="3"
                        />
                        <FaMapMarkerAlt />
                      </div>
                    </Col>
                  </Row>
                </div>

                <div className="remember-row">
                  <p className="checkbox remember signup">
                    <input
                      className="checkbox-spin"
                      type="checkbox"
                      id="acceptTerms"
                      name="acceptTerms"
                      checked={formData.acceptTerms}
                      onChange={handleChange}
                    />
                    <label htmlFor="acceptTerms">
                      <span />
                      {t("register_page.terms")}
                    </label>
                  </p>
                </div>
                {formErrors.acceptTerms && <div className="form-error">{formErrors.acceptTerms}</div>}

                <p>
                  <button
                    type="submit"
                    className="gauto-theme-btn"
                    disabled={loading}
                  >
                    {loading ? (
                      <>
                        <Spinner as="span" animation="border" size="sm" className="me-2" />
                        Kaydediliyor...
                      </>
                    ) : t("register_page.register_now")}
                  </button>
                </p>
              </form>
              <div className="login-sign-up">
                <Link to="/login">{t("register_page.have_account")}</Link>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default Register;
