import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import ProductDetails from "../../components/ProductDetails";

const ProductSinglePage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.product_details")}
        pagesub={t("header-navigation.product_details")}
      />
      <ProductDetails />
    </Fragment>
  );
};
export default ProductSinglePage;
