@import url('../../styles/colors.css');

/* Responsive */
@media (max-width: 991px) {
  .responsiveMenu {
    display: block !important;
  }
  .spinner-master * {
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    box-sizing: border-box;
  }
  .spinner-spin {
    text-indent: -99999px;
    width: 22px;
  }
  .spinner-master {
    display: block !important;
    position: relative;
    margin-top: 16px;
    margin-left: 15px;
    float: left;
  }
  .spinner:nth-child(2n) {
    width: 70% !important;
  }
  .spinner-master label {
    cursor: pointer;
    height: auto;
    width: 100%;
    padding: 0;
    -webkit-transform: translateY(0%);
    transform: translateY(0%);
    top: -13px;
    position: relative;
  }
  .spinner-master .spinner {
    height: 2px;
    width: 100%;
    background-color: #ffffff;
  }
  .spinner-master .horizontal {
    margin-top: 6px;
  }
  .spinner-master .diagonal.part-2 {
    margin-top: 6px;
  }
  .spinner-master.active .spinner-spin > .horizontal {
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  }
  .spinner-master.active .spinner-spin > .diagonal.part-1 {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
    margin-top: 3px;
  }
  .spinner-master.active .spinner-spin > .diagonal.part-2 {
    transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg);
    margin-top: -10px;
  }
  nav#mobileMenu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    display: block;
    z-index: 9999;
  }
  nav#mobileMenu {
    overflow: hidden;
    max-height: 0;
    background-color: #000;
    -webkit-transition: all 0.4s ease 0s;
    transition: all 0.4s ease 0s;
  }
  nav#mobileMenu.active,
  nav#mobileMenu > ul ul.show {
    max-height: 55em;
    overflow: auto;
    padding: 10px 20px;
  }
  nav#mobileMenu > ul ul.show {
    padding: 0;
  }
  nav#mobileMenu > ul > li {
    display: block !important;
    margin: 0 !important;
  }
  nav#mobileMenu > ul > li > a,
  nav#mobileMenu > ul > li > p {
    display: block;
    position: relative;
    padding: 10px 0;
    color: #fff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
  }
  nav#mobileMenu > ul > li:last-child > a {
    border-bottom: 0px;
  }
  nav#mobileMenu li.has-submenu > a:before,
  .mainmenu ul > li.active a:before {
    display: none;
  }
  .megamenu.active {
    padding: 15px 0 !important;
  }
  .sub-menu:before {
    height: 90% !important;
    left: 50% !important;
    -webkit-transform: translate(-50%, -50%) !important;
    transform: translate(-50%, -50%) !important;
  }
  .sub-menu:after {
    display: none;
  }
  nav#mobileMenu li.has-submenu > a.active:after {
    content: "-";
  }
  nav#mobileMenu ul ul > li a {
    padding: 7px 20px;
    font-size: 16px;
    color: #fff;
  }
  nav#mobileMenu ul ul > li a,
  nav#mobileMenu ul ul > li p {
    padding: 10px 20px;
    line-height: normal;
    -webkit-text-fill-color: #fff;
    display: block;
  }
  .mainmenu ul li ul li {
    border: none;
  }

  nav#mobileMenu > ul > li > a,
  nav#mobileMenu > ul > li > p {
    padding: 10px;
    background: transparent;
    -webkit-text-fill-color: #fff;
    font-size: 14px;
    letter-spacing: 1px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.13);
    text-transform: uppercase;
    font-weight: 500;
  }
  nav#mobileMenu p {
    margin: 0;
    line-height: normal;
  }
  .responsivemenu li p span {
    float: right;
  }
  
  .mobileMenu {
    display: block;
  }
  
  .header-promo,
  .main-search-right,
  .mainmenu {
    display: none !important;
  }
}

/* ==========================================================================
   Mobile Menu
   ========================================================================== */

/* Responsive Menu Structure */
.responsiveMenu {
  display: none;
}

@media (max-width: 991px) {
  .responsiveMenu {
    display: block;
  }
}

/* Hamburger Menu Button */
.spinner-master {
  position: relative;
  height: 36px;
  width: 36px;
  margin: 10px;
  float: left;
  z-index: 9999;
  cursor: pointer;
  display: none; /* Varsayılan olarak gizli */
}

.spinner-master input[type=checkbox] {
  display: none;
}

.spinner-master label {
  cursor: pointer;
  position: absolute;
  z-index: 9999;
  height: 100%;
  width: 100%;
  top: 8px;
  left: 0;
}

.spinner-master .spinner {
  position: absolute;
  height: 3px;
  width: 100%;
  background-color: #fff;
}

.spinner-master .diagonal.part-1 {
  position: relative;
  float: left;
  top: 0;
}

.spinner-master .horizontal {
  position: relative;
  float: left;
  margin-top: 6px;
}

.spinner-master .diagonal.part-2 {
  position: relative;
  float: left;
  margin-top: 6px;
}

.spinner-master.active .diagonal.part-1 {
  transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
  margin-top: 10px;
}

.spinner-master.active .horizontal {
  transform: scale(0.1, 1);
  -webkit-transform: scale(0.1, 1);
}

.spinner-master.active .diagonal.part-2 {
  transform: rotate(-135deg);
  -webkit-transform: rotate(-135deg);
  margin-top: -16px;
}

/* Mobile Menu Container */
.mobileMenu {
  background-color: #000;
  color: #fff;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  -webkit-transition: all 0.3s ease-in-out;
  -moz-transition: all 0.3s ease-in-out;
  -o-transition: all 0.3s ease-in-out;
  transition: all 0.3s ease-in-out;
}

.mobileMenu.active {
  visibility: visible;
  opacity: 0.97;
}

/* Menu Items */
.responsivemenu {
  margin: 80px 0 0;
  padding: 0 20px;
  list-style-type: none;
}

.responsivemenu li {
  position: relative;
  margin: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.responsivemenu li:last-child {
  border-bottom: 0;
}

.responsivemenu li a {
  display: block;
  padding: 15px 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  transition: all 0.3s ease;
}

.responsivemenu li a:hover {
  color: var(--primary-color);
  padding-left: 5px;
}

/* Submenu Toggle */
.responsivemenu li p {
  color: #fff;
  display: block;
  margin: 0;
  padding: 15px 0;
  font-size: 16px;
  position: relative;
  text-transform: uppercase;
  cursor: pointer;
  border-bottom: 0;
  font-weight: 500;
  transition: all 0.3s ease;
}

.responsivemenu li p:hover {
  color: var(--primary-color);
}

.responsivemenu li p span {
  float: right;
  font-size: 20px;
  line-height: 25px;
}

/* Submenu */
.sub-menu {
  background: rgba(0, 0, 0, 0.1);
  list-style-type: none;
  margin: 0;
  padding-left: 15px;
  border-radius: 0;
}

.sub-menu li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
}

.sub-menu li:last-child {
  border-bottom: 0;
}

.sub-menu li a {
  font-size: 14px;
  padding: 12px 0;
}

/* User Info and Logout */
.mobile-user-info {
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 0;
}

.mobile-user-info a {
  display: flex !important;
  align-items: center;
  color: #fff !important;
  font-weight: 500 !important;
}

.mobile-user-info a .mobile-icon {
  margin-right: 10px;
  font-size: 20px;
  color: var(--primary-color);
}

.mobile-logout {
  padding: 15px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 15px;
}

.mobile-logout a {
  display: flex !important;
  align-items: center;
  color: var(--primary-color) !important;
  font-weight: 500 !important;
}

.mobile-logout a .mobile-icon {
  margin-right: 10px;
  font-size: 18px;
}

/* Responsive Media Queries */
@media (max-width: 991px) {
  .responsiveMenu {
    display: block !important;
  }
  
  .mobileMenu {
    display: block;
  }
  
  .header-promo,
  .main-search-right,
  .mainmenu {
    display: none !important;
  }
}
