@import url('../../styles/colors.css');

.tender-process-detail-section {
  background-color: #f8f9fa;
  padding: 70px 0;
}

/* <PERSON><PERSON> */
.process-timeline {
  position: relative;
}

.timeline-wrapper {
  position: relative;
  padding-left: 40px;
}

.timeline-wrapper:before {
  content: '';
  position: absolute;
  left: 20px;
  top: 30px;
  bottom: 30px;
  width: 2px;
  background-color: #eee;
}

.timeline-item {
  position: relative;
  margin-bottom: 50px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-dot {
  position: absolute;
  left: -40px;
  top: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--white);
  border: 2px solid var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 18px;
  z-index: 2;
}

.timeline-content {
  padding-left: 15px;
}

.timeline-content .card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.timeline-content .card-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: 15px 20px;
  border: none;
}

.timeline-content .card-header h4 {
  color: var(--white);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.timeline-content .card-body {
  padding: 25px;
}

.timeline-content .card-body p {
  color: var(--text-light-gray);
  margin-bottom: 15px;
  line-height: 1.6;
}

/* Yıldız derecelendirme sistemi */
.rating-stars {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 18px;
  width: 100%;
  height: 100%;
}

/* Yıldız ikonunu büyütme oranını düşürüyorum */
.rating-stars svg {
  transform: scale(1);
}

/* İhale aşamaları */
.tender-phases {
  margin: 25px 0;
  padding: 20px;
  background-color: var(--light-gray);
  border-radius: 8px;
}

.tender-phases h5 {
  font-size: 18px;
  margin-bottom: 15px;
  color: var(--secondary-color);
  font-weight: 600;
}

.phase-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed var(--medium-gray);
}

.phase-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.phase-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-weight: bold;
  flex-shrink: 0;
}

.phase-content {
  flex: 1;
}

.phase-content strong {
  display: block;
  margin-bottom: 5px;
  color: var(--secondary-color);
}

.phase-content p {
  margin: 0;
  font-size: 14px;
  color: var(--text-light-gray);
}

/* Tedarikçi bilgileri */
.supplier-info {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--light-gray);
  border-radius: 8px;
}

.supplier-info h5 {
  font-size: 17px;
  margin-bottom: 15px;
  color: var(--secondary-color);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.supplier-info h5 svg {
  margin-right: 8px;
  color: var(--primary-color);
}

/* Süreç notları */
.process-note {
  margin: 15px 0;
  padding: 15px;
  background-color: #fff9ec;
  border-left: 3px solid #ffcc00;
  border-radius: 4px;
}

.process-note h5 {
  font-size: 16px;
  margin-bottom: 10px;
  color: var(--secondary-color);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.process-note h5 svg {
  margin-right: 8px;
  color: #ffcc00;
}

.process-note p {
  font-size: 14px;
  margin: 0;
}

/* Süreç özellikleri listesi */
.process-features {
  list-style: none;
  padding: 0;
  margin: 15px 0 0;
}

.process-features li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  color: var(--text-light-gray);
}

.process-features li svg {
  color: var(--primary-color);
  margin-right: 10px;
  margin-top: 5px;
  flex-shrink: 0;
}

/* Yardım bölümü */
.process-help {
  background-color: var(--light-gray);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-top: 30px;
}

.process-help h4 {
  font-size: 22px;
  color: var(--secondary-color);
  margin-bottom: 15px;
}

.process-help p {
  color: var(--text-light-gray);
  margin-bottom: 20px;
}

.process-help .gauto-theme-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 25px;
}

.process-help .gauto-theme-btn svg {
  margin-left: 8px;
}

/* Responsive Stilller */
@media (max-width: 991px) {
  .timeline-content .card-header h4 {
    font-size: 18px;
  }
  
  .process-help h4 {
    font-size: 20px;
  }
}

@media (max-width: 767px) {
  .tender-process-detail-section {
    padding: 50px 0;
  }
  
  .timeline-content .card-body {
    padding: 20px;
  }
  
  .timeline-content .card-header h4 {
    font-size: 17px;
  }
  
  .tender-phases h5, 
  .supplier-info h5 {
    font-size: 16px;
  }
  
  .process-help {
    padding: 20px;
  }
  
  .process-help h4 {
    font-size: 18px;
  }
}

@media (max-width: 576px) {
  .tender-process-detail-section {
    padding: 40px 0;
  }
  
  .timeline-wrapper {
    padding-left: 30px;
  }
  
  .timeline-wrapper:before {
    left: 15px;
  }
  
  .timeline-dot {
    left: -30px;
    width: 30px;
    height: 30px;
    font-size: 14px;
  }
  
  .timeline-content {
    padding-left: 10px;
  }
  
  .timeline-content .card-body {
    padding: 15px;
  }
  
  .timeline-content .card-header {
    padding: 12px 15px;
  }
  
  .timeline-content .card-header h4 {
    font-size: 16px;
  }
  
  .phase-icon {
    width: 25px;
    height: 25px;
    font-size: 12px;
  }
  
  .process-features li {
    font-size: 14px;
  }
}

.process-container .card-header {
  font-weight: 600;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 8px 8px 0 0;
}

.process-step.active {
  border: 2px solid var(--primary-color);
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.process-step.active .step-number {
  color: var(--primary-color);
  font-weight: 700;
}

.process-detail-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.form-control:focus {
  border-color: #ced4da;
  box-shadow: 0 0 0 0.25rem rgba(var(--primary-color-rgb), 0.25);
}

.btn-process-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: var(--white);
}

.btn-process-primary:hover {
  background-color: var(--primary-color);
  color: var(--white);
} 