import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, Card, Form, Button, Alert, Spinner, Badge, Tabs, Tab, ListGroup, OverlayTrigger, Tooltip } from "react-bootstrap";
import {
  FaUser,
  FaEnvelope,
  FaIdCard,
  FaPhone,
  FaEdit,
  FaSave,
  FaCalendarAlt,
  FaHeart,
  FaKey,
  FaTrashAlt,
  FaCheckCircle,
  FaExclamationTriangle,
  FaBuilding,
  FaMapMarkerAlt,
  FaBirthdayCake,
  FaShieldAlt,
  FaTimes,
  FaChartLine,
  FaFileInvoiceDollar,
  FaUserTie,
  FaClock,
  FaHistory,
  FaListAlt,
  FaMoneyBillWave,
  FaCar,
  FaStar,
  FaCheck,
  FaInfoCircle,
  FaLock
} from "react-icons/fa";
import { Link, useNavigate } from "react-router-dom";
import { fetchUserProfile, updateUserProfile, updateUserPassword, clearError, clearSuccessMessage } from "../../redux/slices/userSlice";
import { USER_ROLES } from "../../constants/userRoles";
import "./style.css";

const UserProfile = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { user, token, isAuthenticated } = useSelector((state) => state.auth);
  const { profile, loading, error, successMessage: reduxSuccessMessage } = useSelector((state) => state.user);

  const isAdmin = user?.roleId === USER_ROLES.ADMIN.id;
  const isUser = user?.roleId === USER_ROLES.CUSTOMER.id;
  const isVerified = user?.isVerified || false;

  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState("personal");
  const [profileData, setProfileData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    title: '',
    taxNumber: '',
    taxOffice: '',
    companyPhone: '',
    companyEmail: '',
  });
  const [passwordData, setPasswordData] = useState({
    password: '',
    newPassword: '',
    confirmNewPassword: '',
  });
  const [passwordErrors, setPasswordErrors] = useState({});
  const [localSuccessMessage, setLocalSuccessMessage] = useState('');
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [passwordChangeSuccess, setPasswordChangeSuccess] = useState(false);

  // Kullanıcı istatistikleri (gerçek uygulamada API'den gelecek)
  const [userStats, setUserStats] = useState({
    completedTenders: 8,
    activeTenders: 3,
    totalVehicles: 5,
    lastLoginDate: new Date().toLocaleDateString(),
    memberSince: user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : new Date().toLocaleDateString()
  });

  // Uyarı ve başarı mesajları için değişkenler
  const successMessage = reduxSuccessMessage || localSuccessMessage;
  const errorMessage = error;

  // Sayfa yüklendiğinde profil verilerini getir
  useEffect(() => {
    // Hata ve başarı mesajlarını temizle
    dispatch(clearError());
    dispatch(clearSuccessMessage());


    // localStorage'dan token'ı doğrudan kontrol et
    const localStorageToken = localStorage.getItem('token');

    // Kullanıcı giriş yapmışsa profil verilerini getir
    if (isAuthenticated && (token || localStorageToken)) {

      // Eğer Redux state'inde token yoksa ama localStorage'da varsa, localStorage'daki token'ı kullan
      if (!token && localStorageToken) {
      }

      dispatch(fetchUserProfile());
    } else {
      // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
      navigate('/login');
    }
  }, [dispatch, isAuthenticated, token, navigate, user]);

  // Redux store'daki profil verisi değiştiğinde state'i güncelle
  useEffect(() => {
    if (profile?.result) {
      const { userInformation, companyInformation } = profile.result;

      setProfileData({
        firstName: userInformation.firstName || '',
        lastName: userInformation.lastName || '',
        email: userInformation.email || '',
        phone: userInformation.phone || '',
        address: companyInformation.address || '',
        title: companyInformation.title || '',
        taxNumber: companyInformation.taxNumber || '',
        taxOffice: companyInformation.taxOffice || '',
        companyPhone: companyInformation.phone || '',
        companyEmail: companyInformation.email || '',
      });
    }
  }, [profile]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    // Eğer düzenleme modundan çıkılıyorsa orijinal verileri geri yükle
    if (isEditing && profile?.result) {
      const { userInformation, companyInformation } = profile.result;

      setProfileData({
        firstName: userInformation.firstName || '',
        lastName: userInformation.lastName || '',
        email: userInformation.email || '',
        phone: userInformation.phone || '',
        address: companyInformation.address || '',
        title: companyInformation.title || '',
        taxNumber: companyInformation.taxNumber || '',
        taxOffice: companyInformation.taxOffice || '',
        companyPhone: companyInformation.phone || '',
        companyEmail: companyInformation.email || '',
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Redux action ile profil bilgilerini güncelle
    dispatch(updateUserProfile(profileData));
    setIsEditing(false);
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));

    // Girdi değiştiğinde ilgili hata mesajını temizle
    if (passwordErrors[name]) {
      setPasswordErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Şifre değiştirme formunu doğrula
  const validatePasswordForm = () => {
    const errors = {};

    // Mevcut şifre kontrolü
    if (!passwordData.password) {
      errors.password = t("validation.password_required");
    }

    // Yeni şifre kontrolü
    if (!passwordData.newPassword) {
      errors.newPassword = t("validation.new_password_required");
    } else {
      // Şifre uzunluğu kontrolü (8-20 karakter)
      if (passwordData.newPassword.length < 8 || passwordData.newPassword.length > 20) {
        errors.newPassword = t("validation.password_length");
      }
      // Şifre karmaşıklığı kontrolü (küçük harf, büyük harf, rakam, özel karakter)
      else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&.])[A-Za-z\d@$!%*?&.]{8,20}$/.test(passwordData.newPassword)) {
        errors.newPassword = t("validation.password_complexity");
      }
    }

    // Şifre tekrar kontrolü
    if (passwordData.newPassword !== passwordData.confirmNewPassword) {
      errors.confirmNewPassword = t("validation.passwords_not_match");
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();

    // Şifre değiştirme formunu doğrula
    if (!validatePasswordForm()) {
      return;
    }

    // Başarı durumunu sıfırla
    setPasswordChangeSuccess(false);

    // Redux action ile şifre değiştirme işlemini başlat
    dispatch(updateUserPassword(passwordData))
      .unwrap()
      .then(() => {
        // Başarılı olursa
        setPasswordChangeSuccess(true);
        setLocalSuccessMessage(t("profile_page.password_change_success"));

        // 3 saniye sonra formu kapat
        setTimeout(() => {
          setPasswordData({
            password: '',
            newPassword: '',
            confirmNewPassword: '',
          });
          setPasswordChangeSuccess(false);
          setShowPasswordForm(false);
        }, 3000);
      })
      .catch((err) => {
        console.error("Şifre değiştirme hatası:", err);
        // Hata durumunda hata mesajını göster
        if (err.includes("mevcut şifre") || err.includes("current password")) {
          setPasswordErrors({
            ...passwordErrors,
            password: err
          });
        }
      });
  };

  const handleVerificationRedirect = () => {
    navigate("/verification");
  };

  // Kullanıcı profil özeti
  const renderProfileSummary = () => {
    const userInfo = profile?.result?.userInformation;
    const fullName = userInfo ? `${userInfo.firstName} ${userInfo.lastName}` : '';

    return (
      <Card className="profile-summary-card">
        <Card.Body>
          <div className="profile-header">
            <div className="profile-avatar-container">
              <div className="profile-avatar">
                {user?.profileImage ? (
                  <img src={user.profileImage} alt={fullName} className="profile-image" />
                ) : (
                  <div className="profile-image-placeholder">
                    {userInfo?.firstName?.charAt(0) || "U"}
                  </div>
                )}
              </div>
              <div className="profile-info">
                <div className="profile-name-container">
                  <span className="user-name">{fullName}</span>
                  {isVerified && (
                    <OverlayTrigger
                      placement="top"
                      delay={{ show: 250, hide: 100 }}
                      overlay={(props) => (
                        <Tooltip id="verified-tooltip" {...props}>
                          {t("profile_page.verified_account")}
                        </Tooltip>
                      )}
                    >
                      <span className="verified-badge">
                        <FaCheck size={9} />
                      </span>
                    </OverlayTrigger>
                  )}
                </div>
                <p>{userInfo?.email}</p>
              </div>
            </div>
          </div>

          {!isVerified && (
            <div className="profile-actions-panel">
              <Button
                onClick={handleVerificationRedirect}
                className="profile-primary-btn"
              >
                <FaIdCard className="me-2" /> {t("profile_page.verify_now")}
              </Button>
            </div>
          )}
        </Card.Body>
      </Card>
    );
  };

  // User summary card
  const renderUserStatsCard = () => {
    return (
      <Card className="mb-4 stats-card">
        <Card.Header>
          <h5 className="mb-0"><FaChartLine className="me-2" /> {t("profile_page.account_status")}</h5>
        </Card.Header>
        <Card.Body>
          <ListGroup variant="flush">
            <ListGroup.Item className="d-flex justify-content-between align-items-center">
              <span><FaCalendarAlt className="me-2" /> {t("profile_page.account_created")}</span>
              <span>{userStats.memberSince}</span>
            </ListGroup.Item>
            <ListGroup.Item className="d-flex justify-content-between align-items-center">
              <span><FaClock className="me-2" /> {t("profile_page.last_login")}</span>
              <span>{userStats.lastLoginDate}</span>
            </ListGroup.Item>
            <ListGroup.Item className="d-flex justify-content-between align-items-center">
              <span><FaListAlt className="me-2" /> {t("dashboard.stats.my_tenders")}</span>
              <Badge bg="primary" pill>{userStats.completedTenders + userStats.activeTenders}</Badge>
            </ListGroup.Item>
            <ListGroup.Item className="d-flex justify-content-between align-items-center">
              <span><FaHistory className="me-2" /> {t("dashboard.stats.active")}</span>
              <Badge bg="success" pill>{userStats.activeTenders}</Badge>
            </ListGroup.Item>
            <ListGroup.Item className="d-flex justify-content-between align-items-center">
              <span><FaCheckCircle className="me-2" /> {t("dashboard.stats.completed")}</span>
              <Badge bg="info" pill>{userStats.completedTenders}</Badge>
            </ListGroup.Item>
          </ListGroup>
        </Card.Body>
      </Card>
    );
  };

  // Personal information tab
  const renderPersonalInfoTab = () => {
    return (
      <Form onSubmit={handleSubmit}>
        <h5 className="section-title mb-3">{t("profile_page.personal_info")}</h5>
        <p className="mb-4">{t("profile_page.personal_info_desc")}</p>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaUser className="me-2" /> {t("profile_page.first_name")}</Form.Label>
              <Form.Control
                type="text"
                name="firstName"
                value={profileData.firstName}
                onChange={handleChange}
                disabled={!isEditing || loading}
                required
              />
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaUser className="me-2" /> {t("profile_page.last_name")}</Form.Label>
              <Form.Control
                type="text"
                name="lastName"
                value={profileData.lastName}
                onChange={handleChange}
                disabled={!isEditing || loading}
                required
              />
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaEnvelope className="me-2" /> {t("profile_page.email")}</Form.Label>
              <Form.Control
                type="email"
                name="email"
                value={profileData.email}
                onChange={handleChange}
                disabled={!isEditing || loading}
                required
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.email_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaPhone className="me-2" /> {t("profile_page.phone")}</Form.Label>
              <Form.Control
                type="tel"
                name="phone"
                value={profileData.phone}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.phone_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>
        </Row>

        <hr />

        <h5 className="section-title mb-3"><FaMapMarkerAlt className="me-2" /> {t("profile_page.address_title")}</h5>
        <Row>
          <Col md={12}>
            <Form.Group className="mb-4">
              <Form.Label><FaMapMarkerAlt className="me-2" /> {t("profile_page.address")}</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="address"
                value={profileData.address}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
            </Form.Group>
          </Col>
        </Row>

        {isEditing && (
          <div className="d-flex justify-content-end mt-4">
            <Button
              variant="primary"
              type="submit"
              className="profile-primary-btn"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    aria-hidden="true"
                    className="me-2"
                  />
                  {t("profile_page.saving")}
                </>
              ) : (
                <>
                  <FaSave className="me-2" /> {t("profile_page.save_changes")}
                </>
              )}
            </Button>
          </div>
        )}
      </Form>
    );
  };

  // Business/Company information tab
  const renderBusinessInfoTab = () => {
    return (
      <Form onSubmit={handleSubmit}>
        <h5 className="section-title mb-3">{t("profile_page.business_info")}</h5>
        <p className="mb-4">{t("profile_page.business_info_desc")}</p>
        <Row>
          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaUserTie className="me-2" /> {t("profile_page.title")}</Form.Label>
              <Form.Control
                type="text"
                name="title"
                value={profileData.title}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.title_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaIdCard className="me-2" /> {t("profile_page.tax_number")}</Form.Label>
              <Form.Control
                type="text"
                name="taxNumber"
                value={profileData.taxNumber}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.tax_number_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaBuilding className="me-2" /> {t("profile_page.tax_office")}</Form.Label>
              <Form.Control
                type="text"
                name="taxOffice"
                value={profileData.taxOffice}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.tax_office_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaPhone className="me-2" /> {t("profile_page.company_phone")}</Form.Label>
              <Form.Control
                type="tel"
                name="companyPhone"
                value={profileData.companyPhone}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.company_phone_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>

          <Col md={6}>
            <Form.Group className="mb-4">
              <Form.Label><FaEnvelope className="me-2" /> {t("profile_page.company_email")}</Form.Label>
              <Form.Control
                type="email"
                name="companyEmail"
                value={profileData.companyEmail}
                onChange={handleChange}
                disabled={!isEditing || loading}
              />
              {isEditing && (
                <Form.Text className="text-muted">
                  <small>
                    <FaInfoCircle className="me-1" />
                    {t("profile_page.company_email_info")}
                  </small>
                </Form.Text>
              )}
            </Form.Group>
          </Col>
        </Row>

        {isEditing && (
          <div className="d-flex justify-content-end mt-4">
            <Button
              variant="primary"
              type="submit"
              className="profile-primary-btn"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    aria-hidden="true"
                    className="me-2"
                  />
                  {t("profile_page.saving")}
                </>
              ) : (
                <>
                  <FaSave className="me-2" /> {t("profile_page.save_changes")}
                </>
              )}
            </Button>
          </div>
        )}
      </Form>
    );
  };

  // Security tab
  const renderSecurityTab = () => {
    return (
      <div className="security-section">
        <h5 className="section-title mb-3">{t("profile_page.security_title")}</h5>
        <p className="mb-4">{t("profile_page.security_info")}</p>

        <Card className="mb-3">
          <Card.Body className="py-2">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <div>
                <h6 className="mb-0"><FaKey className="me-2 text-primary" /> {t("profile_page.change_password")}</h6>
                <p className="text-muted small mb-0">{t("profile_page.password_description")}</p>
              </div>
              <Button
                variant="outline-primary"
                size="sm"
                onClick={() => setShowPasswordForm(!showPasswordForm)}
                className="profile-white-btn btn-sm"
              >
                <FaKey className="me-2" /> {t("profile_page.change_password")}
              </Button>
            </div>

            {showPasswordForm && (
              <Form onSubmit={handlePasswordSubmit} className="mt-4 password-change-form">
                {passwordChangeSuccess && (
                  <Alert variant="success" className="mb-4">
                    <FaCheckCircle className="me-2" /> {t("profile_page.password_change_success")}
                  </Alert>
                )}

                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>{t("profile_page.current_password")}</Form.Label>
                      <Form.Control
                        type="password"
                        name="password"
                        value={passwordData.password}
                        onChange={handlePasswordChange}
                        isInvalid={!!passwordErrors.password}
                        required
                      />
                      <Form.Control.Feedback type="invalid">
                        {passwordErrors.password}
                      </Form.Control.Feedback>
                      <Form.Text className="text-muted">
                        <small>
                          <FaInfoCircle className="me-1" />
                          {t("profile_page.current_password_info")}
                        </small>
                      </Form.Text>
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>{t("profile_page.new_password")}</Form.Label>
                      <Form.Control
                        type="password"
                        name="newPassword"
                        value={passwordData.newPassword}
                        onChange={handlePasswordChange}
                        isInvalid={!!passwordErrors.newPassword}
                        required
                      />
                      <Form.Control.Feedback type="invalid">
                        {passwordErrors.newPassword}
                      </Form.Control.Feedback>
                      <Form.Text className="text-muted">
                        <small>
                          <FaInfoCircle className="me-1" />
                          {t("profile_page.password_requirements")}
                        </small>
                      </Form.Text>
                    </Form.Group>
                  </Col>

                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>{t("profile_page.confirm_password")}</Form.Label>
                      <Form.Control
                        type="password"
                        name="confirmNewPassword"
                        value={passwordData.confirmNewPassword}
                        onChange={handlePasswordChange}
                        isInvalid={!!passwordErrors.confirmNewPassword}
                        required
                      />
                      <Form.Control.Feedback type="invalid">
                        {passwordErrors.confirmNewPassword}
                      </Form.Control.Feedback>
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex justify-content-end mt-3">
                  <Button
                    variant="outline-secondary"
                    className="me-2 profile-white-btn"
                    onClick={() => {
                      setShowPasswordForm(false);
                      setPasswordData({
                        password: '',
                        newPassword: '',
                        confirmNewPassword: '',
                      });
                      setPasswordErrors({});
                    }}
                    disabled={loading}
                  >
                    <FaTimes className="me-2" /> {t("common.cancel")}
                  </Button>
                  <Button
                    variant="primary"
                    type="submit"
                    className="profile-primary-btn"
                    disabled={loading || passwordChangeSuccess}
                  >
                    {loading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        {t("profile_page.saving")}
                      </>
                    ) : passwordChangeSuccess ? (
                      <>
                        <FaCheckCircle className="me-2" /> {t("profile_page.password_changed")}
                      </>
                    ) : (
                      <>
                        <FaSave className="me-2" /> {t("profile_page.save_changes")}
                      </>
                    )}
                  </Button>
                </div>
              </Form>
            )}
          </Card.Body>
        </Card>

        <Card className="mb-3">
          <Card.Body className="py-2">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <div>
                <h6 className="mb-0"><FaShieldAlt className="me-2 text-primary" /> {t("profile_page.verification_title")}</h6>
                <p className="text-muted small mb-0">
                  {isVerified
                    ? t("verification_page.verified_account_description")
                    : t("profile_page.verification_required")}
                </p>
              </div>
              {!isVerified && (
                <Button
                  onClick={handleVerificationRedirect}
                  className="profile-primary-btn"
                >
                  <FaIdCard className="me-2" /> {t("profile_page.verify_now")}
                </Button>
              )}
              {isVerified && (
                <Badge bg="success">
                  <FaCheckCircle className="me-1" /> {t("profile_page.verified")}
                </Badge>
              )}
            </div>
          </Card.Body>
        </Card>

        <Card className="mb-3">
          <Card.Body className="py-2 text-danger">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h6 className="mb-0 text-danger"><FaTrashAlt className="me-2" /> {t("profile_page.delete_account")}</h6>
                <p className="text-muted small mb-0">{t("profile_page.delete_account_description")}</p>
              </div>
              <Button
                variant="outline-danger"
                size="sm"
                as={Link}
                to="/contact"
                className="profile-white-btn btn-sm"
                style={{borderColor: "var(--danger)", color: "var(--danger)"}}
              >
                <FaTrashAlt className="me-2" /> {t("profile_page.contact_support")}
              </Button>
            </div>
          </Card.Body>
        </Card>
      </div>
    );
  };

  return (
    <div className="user-profile-container py-5">
      <Container>
        <Row>
          {/* Sol kolon - Profil özeti ve istatistikler */}
          <Col lg={4} md={12} className="mb-4">
            {/* Profil özeti kartı */}
            {renderProfileSummary()}

            {/* İstatistikler kartı */}
            {renderUserStatsCard()}
          </Col>

          {/* Sağ kolon - Sekmeler */}
          <Col lg={8} md={12}>
            <Card className="profile-card mb-4">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h3 className="mb-0">
                  <FaUser className="me-2" /> {t("profile_page.profile_title")}
                </h3>
                {!isEditing ? (
                  <button
                    onClick={handleEditToggle}
                    className="edit-button"
                    title={t("profile_page.edit")}
                    type="button"
                  >
                    <FaEdit size={18} color="#ffffff" data-icon="edit" />
                  </button>
                ) : (
                  <button
                    onClick={handleEditToggle}
                    className="edit-button"
                    title={t("common.cancel")}
                    type="button"
                  >
                    <FaTimes size={18} color="#ffffff" data-icon="times" />
                  </button>
                )}
              </Card.Header>
              <Card.Body>
                <Tabs defaultActiveKey="personal" id="profile-tabs" className="mb-4 profile-tabs">
                  <Tab eventKey="personal" title={<><FaUser className="me-2" />{t("profile_page.personal_info")}</>}>
                    {renderPersonalInfoTab()}
                  </Tab>
                  <Tab eventKey="business" title={<><FaBuilding className="me-2" />{t("profile_page.business_info")}</>}>
                    {renderBusinessInfoTab()}
                  </Tab>
                  <Tab eventKey="security" title={<><FaLock className="me-2" />{t("profile_page.security")}</>}>
                    {renderSecurityTab()}
                  </Tab>
                </Tabs>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {successMessage && (
          <Row>
            <Col md={12}>
              <Alert variant="success" className="mt-3">
                <FaCheckCircle className="me-2" /> {successMessage}
              </Alert>
            </Col>
          </Row>
        )}

        {errorMessage && (
          <Row>
            <Col md={12}>
              <Alert variant="danger" className="mt-3">
                <FaExclamationTriangle className="me-2" /> {errorMessage}
              </Alert>
            </Col>
          </Row>
        )}
      </Container>
    </div>
  );
};

export default UserProfile;