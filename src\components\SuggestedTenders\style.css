.suggested-tenders-section {
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.suggested-tenders-section .section-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.suggested-tenders-section .section-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.suggested-tenders-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  flex-grow: 1;
  max-height: 300px;
}

.suggested-tender-card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.suggested-tender-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.suggested-tender-card .card-body {
  padding: 15px;
}

.suggested-tender-card .card-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #0066cc;
}

.badge-container {
  position: absolute;
  top: 10px;
  right: 10px;
}

.new-badge {
  background-color: #e8f5e9;
  color: #43a047;
  border-radius: 4px;
  padding: 3px 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tender-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.info-label {
  color: #666;
  font-size: 0.85rem;
  margin-right: 5px;
}

.info-value {
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

.bid-button {
  background-color: #ff6700;
  border-color: #ff6700;
}

.bid-button:hover {
  background-color: #e65c00;
  border-color: #e65c00;
}

.view-all-container {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  flex-shrink: 0;
}

.view-all-container .view-all-button {
  color: #0066cc;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
  text-decoration: none;
}

.view-all-container .view-all-button:hover {
  color: #004999;
  text-decoration: none;
}

.loading-indicator,
.error-message,
.no-items-message {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 0.9rem;
} 