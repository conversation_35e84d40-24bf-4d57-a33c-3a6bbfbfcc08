import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const SEOMeta = ({ 
  title, 
  description, 
  keywords, 
  canonicalUrl, 
  ogTitle, 
  ogDescription, 
  ogImage, 
  ogUrl,
  twitterTitle,
  twitterDescription,
  twitterImage,
  language,
  translateTitle = true,
  translateDescription = true,
  translateKeywords = true,
}) => {
  const { t, i18n } = useTranslation();
  const currentLang = language || i18n.language;
  
  // Çeviriler için ilgili fonksiyonlar
  const getTitle = () => translateTitle ? t(title) : title;
  const getDescription = () => translateDescription ? t(description) : description;
  const getKeywords = () => translateKeywords ? t(keywords) : keywords;
  
  // Twitter etiketleri için varsayılan değerler
  const twitterTitleContent = twitterTitle || ogTitle || title;
  const twitterDescContent = twitterDescription || ogDescription || description;
  const twitterImageContent = twitterImage || ogImage;

  return (
    <Helmet>
      {/* Temel meta etiketleri */}
      <html lang={currentLang} />
      <title>{getTitle()}</title>
      <meta name="description" content={getDescription()} />
      {keywords && <meta name="keywords" content={getKeywords()} />}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Open Graph etiketleri */}
      {ogTitle && <meta property="og:title" content={translateTitle ? t(ogTitle) : ogTitle} />}
      {ogDescription && <meta property="og:description" content={translateDescription ? t(ogDescription) : ogDescription} />}
      {ogImage && <meta property="og:image" content={ogImage} />}
      {ogUrl && <meta property="og:url" content={ogUrl} />}
      <meta property="og:type" content="website" />
      <meta property="og:locale" content={currentLang} />
      
      {/* Twitter Card etiketleri */}
      <meta name="twitter:card" content="summary_large_image" />
      {twitterTitleContent && <meta name="twitter:title" content={translateTitle ? t(twitterTitleContent) : twitterTitleContent} />}
      {twitterDescContent && <meta name="twitter:description" content={translateDescription ? t(twitterDescContent) : twitterDescContent} />}
      {twitterImageContent && <meta name="twitter:image" content={twitterImageContent} />}
      
      {/* Mobil için meta etiketleri */}
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
    </Helmet>
  );
};

SEOMeta.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  keywords: PropTypes.string,
  canonicalUrl: PropTypes.string,
  ogTitle: PropTypes.string,
  ogDescription: PropTypes.string,
  ogImage: PropTypes.string,
  ogUrl: PropTypes.string,
  twitterTitle: PropTypes.string,
  twitterDescription: PropTypes.string,
  twitterImage: PropTypes.string,
  language: PropTypes.string,
  translateTitle: PropTypes.bool,
  translateDescription: PropTypes.bool,
  translateKeywords: PropTypes.bool,
};

export default SEOMeta; 