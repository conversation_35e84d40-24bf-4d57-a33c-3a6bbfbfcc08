@import url('../../styles/colors.css');

/* ===================== FOOTER ======================*/
.footer-top-area {
  padding: 50px 0;
  background: var(--secondary-dark);
  position: relative;
  z-index: 1;
}

.footer-top-area:after {
  display: none;
}

.single-footer {
  clear: both;
  color: var(--white);
}

.quick-links {
  float: left;
  width: 45%;
}

.footer-logo {
  margin-bottom: 15px;
}

/* Logo bileşeni ayrı bir component olarak taşındı */

.single-footer h3,
.footer-address h3 {
  font-size: 17px;
  color: var(--white);
  letter-spacing: 1px;
  margin-bottom: 30px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: block;
  text-transform: uppercase;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  position: relative;
}

.single-footer > h3:after,
.footer-address > h3:after {
  position: absolute;
  content: "";
  bottom: -13px;
  width: 18%;
  height: 2px;
  background: var(--primary-color);
  left: 0;
}

.quick_links {
  overflow: hidden;
}

.quick-links li a {
  color: var(--light-gray);
}

.quick-links li a:hover {
  color: var(--primary-color);
}

.footer-address {
  margin-top: 30px;
}

.newsletter_box {
  margin-top: 30px;
}

.newsletter_box form {
  width: 230px;
  height: 40px;
  background: var(--secondary-dark);
  position: relative;
  border-radius: 5px;
}

.newsletter_box form input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background: var(--secondary-dark);
  height: 100%;
  border: medium none;
  padding: 5px 10px;
  color: var(--white);
  border-radius: 5px;
}

.newsletter_box form button {
  position: absolute;
  border: medium none;
  background: transparent;
  right: 10px;
  height: 100%;
  color: var(--primary-color);
  cursor: pointer;
}

.footer-post-image {
  width: 70px;
  float: left;
  margin-right: 10px;
}

.footer-post-image img {
  width: 100%;
  height: 100%;
  border-radius: 5px;
}

.footer-post-text {
  overflow: hidden;
}

.single-footer-post {
  clear: both;
  margin-bottom: 15px;
  overflow: hidden;
}

.footer-post-text h3 {
  color: var(--white);
  font-size: 14px;
  margin-bottom: 5px;
  text-transform: capitalize;
  line-height: 23px;
  font-weight: normal;
}

.footer-post-text h3 a {
  color: var(--light-gray);
}

.footer-post-text h3 a:hover {
  color: var(--primary-color);
}

.footer-post-text p {
  color: var(--primary-light);
  font-style: italic;
  font-size: 13px;
}

.footer-bottom-area {
  padding: 20px 0;
  background: var(--secondary-dark);
  border-top: 1px solid var(--dark-gray);
}

.copyright p {
  color: var(--light-gray);
  font-size: 14px;
  text-transform: capitalize;
}

.copyright p svg {
  fill: var(--primary-color);
  margin: 0 2px;
}

.copyright p a {
  color: var(--light-gray);
}

.footer-social ul {
  text-align: right;
}

.footer-social ul li {
  display: inline-block;
  margin-left: 10px;
}

.footer-social ul li a {
  display: block;
  color: var(--white);
  font-size: 14px;
}

.footer-links li a:before {
  position: absolute;
  content: "";
  left: 0;
  top: 0;
  width: 15px;
  height: 100%;
  background: var(--primary-color);
  transform: skew(-20deg);
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s ease 0s;
}

.footer-links li a:hover {
  color: var(--primary-color);
  padding-left: 25px;
}

.footer-social a:hover {
  fill: var(--primary-color);
}

@media (min-width: 768px) and (max-width: 991px) {
  .single-footer {
    margin: 20px auto 20px;
  }
  .newsletter_box form {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .single-footer {
    margin: 20px 0;
  }
  .copyright {
    text-align: center;
    margin-bottom: 10px;
  }
  .footer-social ul {
    text-align: center;
  }
  .newsletter_box form {
    width: 100%;
  }
}
@media only screen and (min-width: 480px) and (max-width: 767px) {
  .single-footer {
    margin: 20px 0;
  }
  .copyright {
    text-align: center;
    margin-bottom: 10px;
  }
  .footer-social ul {
    text-align: center;
  }
  .newsletter_box form {
    width: 100%;
  }
}
