import React, { useState, useEffect } from 'react';
import { Container, Button } from 'react-bootstrap';
import { FaSort, FaSync } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import TenderItem from './TenderItem';
import './style.css';

// API servisi import edeceğiz
import { useMockAPI } from '../../services/api';
import tenderService from '../../services/tenderService';

// İhale durumları
const TENDER_STATUSES = {
  ALL: 0,
  DRAFT: 100,
  REVIEW: 120,
  LIVE: 300,
  RELIVE: 320,
  REJECT: 400,
  EXPIRED: 500,
  CANCELLED: 600,
  COMPLETED: 700
};

const ActiveTenders = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useSelector(state => state.auth);
  
  const [tenders, setTenders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentStatusId, setCurrentStatusId] = useState(TENDER_STATUSES.ALL); // Varsayılan olarak LIVE statüsü
  // Pagination için eklenen state'ler
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(3); // Varsayılan 10, istenirse değiştirilebilir
  const [totalCount, setTotalCount] = useState(0);
  
  // Kalan süreyi hesaplama fonksiyonu (her yerde kullanılabilir)
  const calculateRemainingTime = (endDateStr) => {
    const endDate = new Date(endDateStr);
    const now = new Date();
    
    const diffTime = endDate - now;
    if (diffTime <= 0) {
      return 'Süre doldu';
    }
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} gün ${diffHours} saat`;
    } else if (diffHours > 0) {
      return `${diffHours} saat`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} dakika`;
    } else {
      return 'Son dakika';
    }
  };

  // İhale durumunu belirleme fonksiyonu (her yerde kullanılabilir)
  const getStatusFromTender = (tender) => {
    if (tender.tenderStatus) {
      switch (tender.tenderStatus.id) {
        case TENDER_STATUSES.DRAFT: return 'Taslak';
        case TENDER_STATUSES.REVIEW: return 'İnceleme';
        case TENDER_STATUSES.LIVE: return 'Yayında';
        case TENDER_STATUSES.RELIVE: return 'Yeniden Yayında';
        case TENDER_STATUSES.REJECT: return 'Reddedildi';
        case TENDER_STATUSES.EXPIRED: return 'Sona Erdi';
        case TENDER_STATUSES.CANCELLED: return 'İptal Edildi';
        case TENDER_STATUSES.COMPLETED: return 'Tamamlandı';
        case TENDER_STATUSES.ALL: return 'Tümü';
        default: return tender.tenderStatus.statusName || 'Bilinmeyen Durum';
      }
    } else {
      return 'Bilinmeyen Durum';
    }
  };
  
  // API'dan veri çekme işlemi
  useEffect(() => {
    const fetchActiveTenders = async () => {
      setIsLoading(true);
      
      try {
        // Gerçek API çağrısı - ActiveTenders bileşeni için mock data kullanımı kapatıldı
        // Pagination parametreleri eklendi
        const response = await tenderService.getActiveTenders({
          pageNumber: pageNumber,
          pageSize: pageSize,
          statusId: currentStatusId
        });
        
        if (response.data && response.data.result) {
          // API'den gelen verileri işleme
          const apiTenders = response.data.result.map(tender => ({
            ...tender,
            offerCount: tender.offerCount !== null && tender.offerCount !== undefined ? tender.offerCount : 0,
            remainingTime: tender.endDate ? calculateRemainingTime(tender.endDate) : '-',
            minimumOffer: tender.minimumOffer !== null && tender.minimumOffer !== undefined
              ? `₺${tender.minimumOffer}`
              : 'Teklif bulunamadı',
          }));
          setTenders(apiTenders);
          // Toplam kayıt sayısı (API'dan gelmeli)
          setTotalCount(response.data.totalCount || 0);
        } else {
          setError('Veri formatı beklendiği gibi değil');
        }
        
        setIsLoading(false);
      } catch (err) {
        setError('Aktif ihaleler getirilirken hata oluştu');
        setIsLoading(false);
      }
    };

    // Başlangıç ve bitiş tarihlerine göre durum belirleme (yedek)
    const getStatusFromDates = (startDateStr, endDateStr) => {
      const startDate = new Date(startDateStr);
      const endDate = new Date(endDateStr);
      const now = new Date();
      
      if (now < startDate) {
        return 'Yakında Başlayacak';
      } else if (now > endDate) {
        return 'Tamamlandı';
      } else {
        // Son 24 saat içinde mi kontrol et
        const oneDay = 24 * 60 * 60 * 1000; // bir günlük milisaniye
        if ((endDate - now) < oneDay) {
          return 'Son 24 Saat';
        } else {
          return 'Aktif';
        }
      }
    };

    // Her durumda gerçek API verilerini getir
    fetchActiveTenders();
  }, [user, t, currentStatusId, pageNumber, pageSize]);
  
  useEffect(() => {
  }, [tenders]);
  
  // Refresh butonu için fonksiyon
  const handleRefresh = () => {
    setIsLoading(true);
    // Verileri yeniden yükleme
    setTimeout(() => {
      const fetchAgain = async () => {
        try {
          const response = await tenderService.getActiveTenders({
            pageNumber: pageNumber,
            pageSize: pageSize,
            statusId: currentStatusId
          });
          
          if (response.data && response.data.result) {
            const apiTenders = response.data.result.map(tender => ({
              ...tender,
              offerCount: tender.offerCount !== null && tender.offerCount !== undefined ? tender.offerCount : 0,
              remainingTime: tender.endDate ? calculateRemainingTime(tender.endDate) : '-',
              minimumOffer: tender.minimumOffer !== null && tender.minimumOffer !== undefined
                ? `₺${tender.minimumOffer}`
                : 'Teklif bulunamadı',
            }));
            
            setTenders(apiTenders);
          }
          setIsLoading(false);
        } catch (err) {
          console.error('Verileri yeniden yüklerken hata:', err);
          setError('Verileri yenilerken hata oluştu');
          setIsLoading(false);
        }
      };
      
      fetchAgain();
    }, 300);
  };
  
  // Status değiştirme işlevi
  const handleStatusChange = (statusId) => {
    setCurrentStatusId(statusId);
    setPageNumber(1);
  };
  
  // Tümünü Görüntüle butonuna tıklama
  const handleViewAllTenders = () => {
    navigate('/my-tenders');
  };
  
  // Sayfa değiştirici fonksiyon
  const handlePageChange = (newPage) => {
    setPageNumber(newPage);
  };
  
  return (
    <section className="active-tenders-section">
      <Container>
        <div className="section-header">
          <div className="section-title">
            <h3>{t('dashboard.active_tenders.title')}</h3>
          </div>
          <div className="section-actions d-flex align-items-center gap-2">
            <div className="status-filter me-2">
              <select 
                className="form-select form-select-sm"
                value={currentStatusId}
                onChange={(e) => handleStatusChange(Number(e.target.value))}
              >
                <option value={TENDER_STATUSES.ALL}>Tümü</option>
                <option value={TENDER_STATUSES.DRAFT}>Taslak</option>
                <option value={TENDER_STATUSES.REVIEW}>İnceleme</option>
                <option value={TENDER_STATUSES.LIVE}>Yayında</option>
                <option value={TENDER_STATUSES.RELIVE}>Yeniden Yayında</option>
                <option value={TENDER_STATUSES.REJECT}>Reddedildi</option>
                <option value={TENDER_STATUSES.EXPIRED}>Sona Erdi</option>
                <option value={TENDER_STATUSES.CANCELLED}>İptal Edildi</option>
                <option value={TENDER_STATUSES.COMPLETED}>Tamamlandı</option>
              </select>
            </div>
            <Button 
              variant="outline-secondary" 
              size="sm" 
              className="sort-button"
            >
              <FaSort /> {t('common.sort')}
            </Button>
            <Button 
              variant="outline-secondary" 
              size="sm" 
              className="refresh-button"
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <FaSync className={isLoading ? 'spin' : ''} /> 
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="loading-indicator">{t('common.loading')}</div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : tenders.length === 0 ? (
          <div className="no-items-message">{t('dashboard.active_tenders.no_items')}</div>
        ) : (
          <>
            <div className="tenders-card">
              <div className={`tenders-card-body${pageSize > 5 ? ' scrollable' : ''}`}>
                {tenders.map(tender => (
                  <TenderItem key={tender.id} tender={tender} />
                ))}
              </div>
            </div>
            {/* Pagination Componenti */}
            <div className="pagination-wrapper mt-3 d-flex flex-wrap align-items-center justify-content-center gap-3">
              <span className="text-muted small">
                {`Toplam: ${totalCount}`}
              </span>
              <nav>
                <ul className="pagination pagination-sm mb-0">
                  <li className={`page-item${pageNumber === 1 ? ' disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(pageNumber - 1)} disabled={pageNumber === 1}>
                      &laquo;
                    </button>
                  </li>
                  {/* Sayfa numaraları */}
                  {Array.from({ length: Math.ceil(totalCount / pageSize) }, (_, i) => i + 1).map(page => (
                    <li key={page} className={`page-item${pageNumber === page ? ' active' : ''}`}>
                      <button className="page-link" onClick={() => handlePageChange(page)}>{page}</button>
                    </li>
                  ))}
                  <li className={`page-item${pageNumber === Math.ceil(totalCount / pageSize) || totalCount === 0 ? ' disabled' : ''}`}>
                    <button className="page-link" onClick={() => handlePageChange(pageNumber + 1)} disabled={pageNumber === Math.ceil(totalCount / pageSize) || totalCount === 0}>
                      &raquo;
                    </button>
                  </li>
                </ul>
              </nav>
              <div className="page-size-filter d-flex align-items-center">
                <span className="text-secondary small fw-semibold me-2" style={{minWidth: 65, textAlign: 'right', letterSpacing: '0.01em'}}>Sayfa başı:</span>
                <select
                  className="form-select form-select-sm"
                  style={{ minWidth: 60 }}
                  value={pageSize}
                  onChange={e => {
                    setPageSize(Number(e.target.value));
                    setPageNumber(1);
                  }}
                >
                  {[3,5,10,20,50,100].map(size => (
                    <option key={size} value={size}>{size}</option>
                  ))}
                </select>
              </div>
            </div>
            <button className="view-all-button" onClick={handleViewAllTenders}>
              {t('dashboard.active_tenders.view_all')}
            </button>
          </>
        )}
      </Container>
    </section>
  );
};

export default ActiveTenders; 