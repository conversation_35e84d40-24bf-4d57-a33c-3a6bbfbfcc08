import api from './api';

// ----- Gerçek API Çağrıları ----- //

/**
 * Tüm ihaleleri getirir
 * @param {Object} params - Say<PERSON>lama ve filtreleme parametreleri
 * @returns {Promise} API yanıtı
 */
const getTenders = (params = { pageNumber: 1, pageSize: 100, statusId: null }) => {
  return api.post('/Tender/GetTenders', {
    pageNumber: params.pageNumber || 1,
    pageSize: params.pageSize || 100,
    requestModel: {
      statusId: params.statusId || null // Eğer statusId belirtilmişse onu kullan, yoksa tüm statüleri getir
    },
    sort: params.sort || "",
    isAscending: params.isAscending !== undefined ? params.isAscending : true,
    search: params.search || ""
  });
};

/**
 * Aktif ihaleleri getirir
 * @param {Object} params - Say<PERSON>lama ve filtreleme parametreleri
 * @returns {Promise} API yanıtı
 */
const getActiveTenders = (params = { pageNumber: 1, pageSize: 100, statusId: 300 }) => {
  return api.post('/Tender/GetTenders', {
    pageNumber: params.pageNumber || 1,
    pageSize: params.pageSize || 100,
    requestModel: {
      statusId: params.statusId !== undefined ? params.statusId : 300 // Varsayılan olarak LIVE statüsü (300)
    },
    sort: params.sort || "",
    isAscending: params.isAscending !== undefined ? params.isAscending : true,
    search: params.search || ""
  });
};

/**
 * Belirli bir ihaleyi ID'ye göre getirir
 * @param {string} id - İhale ID'si
 * @returns {Promise} API yanıtı
 */
const getTenderById = (id) => {
  return api.get(`/Tender/GetTender/${id}`);
};

/**
 * Kullanıcıya ait ihaleleri getirir
 * @param {Object} params - Sayfalama ve filtreleme parametreleri
 * @returns {Promise} API yanıtı
 */
const getUserTenders = (params = { pageNumber: 1, pageSize: 100 }) => {
  return api.get('/Tender/GetUserTenders', { params });
};

/**
 * Yeni ihale oluşturur
 * @param {Object} tenderData - İhale verileri
 * @returns {Promise} API yanıtı
 */
const createTender = (tenderData) => {
  return api.post('/Tender', tenderData);
};

/**
 * İhale bilgilerini günceller
 * @param {string} id - İhale ID'si
 * @param {Object} tenderData - Güncellenecek ihale verileri
 * @returns {Promise} API yanıtı
 */
const updateTender = (id, tenderData) => {
  return api.put(`/Tender/UpdateTender/${id}`, tenderData);
};

/**
 * İhale detaylarını getirir
 * @param {string} tenderId - İhale ID'si
 * @returns {Promise} API yanıtı
 */
const getTenderDetails = (tenderId) => {
  return api.post('/Tender/GetTenderDetails', { TenderId: tenderId });
};

/**
 * İhale başlatma için gerekli opsiyonları getirir
 * @returns {Promise} API yanıtı
 */
const getTenderOptions = () => {
  return api.get('/Tender/GetTenderOptions');
};

/**
 * Seçilen marka için araç modellerini getirir
 * @param {string} brandId
 * @returns {Promise} API yanıtı
 */
const getVehicleModels = (brandId) => {
  return api.post('/Tender/GetVehicleModel', { BrandId: brandId });
};

/**
 * Mevcut (yayındaki) ihaleleri getirir (diğer kullanıcıların ihaleleri)
 * @param {Object} params - Sayfalama ve filtreleme parametreleri
 * @returns {Promise} API yanıtı
 */
const getAvailableTenders = (params = { pageNumber: 1, pageSize: 10, statusId: 0, sort: null, isAscending: true, search: null }) => {
  return api.post('/Tender/GetAvailableTenders', {
    pageNumber: params.pageNumber || 1,
    pageSize: params.pageSize || 10,
    requestModel: {
      statusId: params.statusId !== undefined ? params.statusId : 0
    },
    sort: params.sort || null,
    isAscending: params.isAscending !== undefined ? params.isAscending : true,
    search: params.search || null
  });
};

/**
 * Başka bir kullanıcının açtığı ihalenin detayını getirir
 * @param {string} tenderId - İhale ID'si
 * @returns {Promise} API yanıtı
 */
const getAvailableTenderDetail = (tenderId) => {
  return api.post('/Tender/GetAvailableTenderDetail', { TenderId: tenderId });
};

/**
 * Teklif gönderir
 * @param {Object} bid Teklif modeli { tenderId, amount, vehicleYear, vehicleKm, usageStatusId }
 * @returns {Promise} API yanıtı
 */
const sendBid = (bid) => {
  return api.post('/Tender/SendBid', bid);
};

const tenderService = {
  getTenders,
  getActiveTenders,
  getTenderById,
  getUserTenders,
  createTender,
  updateTender,
  getTenderDetails,
  getTenderOptions,
  getVehicleModels,
  getAvailableTenders,
  getAvailableTenderDetail,
  sendBid
};

export default tenderService; 