import React from 'react';
import { FaAngleR<PERSON>, FaUsers, FaClock, FaTag, FaCar } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import './style.css';

const TenderItem = ({ tender }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleViewTenderDetails = () => {
    navigate('/tender-details', { state: { tenderId: tender.id } });
  };

  // Status badge gösterim fonksiyonu
  const renderStatusBadge = (status) => {
    switch(status) {
      case 'Aktif':
        return <span className="status-badge status-active">{t('dashboard.tender_status.active')}</span>;
      case 'Yeni Teklifler':
        return <span className="status-badge status-new-offers">{t('dashboard.tender_status.new_offers')}</span>;
      case 'Son 24 Saat':
        return <span className="status-badge status-last-24-hours">{t('dashboard.tender_status.last_24_hours')}</span>;
      default:
        return <span className="status-badge status-active">{status}</span>;
    }
  };

  // Yeni başlık formatı: "X Adet Y(Marka) - Z(Model) İhalesi"
  const vehicleQuantity = tender.vehicleQuantity || 1; // Varsayılan 1
  const brand = tender.model?.brandName || tender.brand || '';
  const model = tender.model?.name || '';
  const tenderTitle = `${vehicleQuantity} Adet ${brand} - ${model} İhalesi`;
  const statusName = tender.tenderStatus?.statusName || '';

  return (
    <div className="tender-item">
      <div className="tender-title-row">
        <h4 className="tender-title">{tenderTitle}</h4>
        {renderStatusBadge(statusName)}
      </div>
      <div className="tender-no-row">
        {t('dashboard.active_tenders.start_date')}: {new Date(tender.startDate).toLocaleString('tr-TR', {
          day: '2-digit', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit'
        })}
      </div>
      <div className="tender-info-row compact">
        <div className="tender-info-section">
          <FaUsers className="tender-info-icon" />
          <span className="tender-info-label">{t('dashboard.active_tenders.offer_count')}</span>
          <span className="tender-info-value">{tender.offerCount}</span>
        </div>
        <span className="tender-info-separator">|</span>
        <div className="tender-info-section">
          <FaClock className="tender-info-icon" />
          <span className="tender-info-label">{t('dashboard.active_tenders.remaining_time')}</span>
          <span className="tender-info-value time-value">{tender.remainingTime}</span>
        </div>
        <span className="tender-info-separator">|</span>
        <div className="tender-info-section">
          <FaTag className="tender-info-icon" />
          <span className="tender-info-label">{t('dashboard.active_tenders.lowest_offer')}</span>
          <span className="tender-info-value price-value">{tender.minimumOffer !== null ? tender.minimumOffer : '-'}</span>
        </div>
        <span className="tender-info-separator">|</span>
        <div className="tender-info-section">
          <FaCar className="tender-info-icon" />
          <span className="tender-info-label">{t('dashboard.active_tenders.vehicle_count')}</span>
          <span className="tender-info-value">{vehicleQuantity}</span>
        </div>
      </div>
      <div 
        className="tender-details-link flex-right"
        onClick={handleViewTenderDetails}
        style={{ cursor: 'pointer' }}
      >
        {t('dashboard.active_tenders.view_details')} <FaAngleRight />
      </div>
    </div>
  );
};

export default TenderItem; 