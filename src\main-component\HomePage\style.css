.homepage {
  font-family: '<PERSON>o', sans-serif;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
  padding: 80px 0;
  color: white;
}

.hero-section h1 {
  font-weight: 700;
}

/* Features Section */
.features-section {
  background-color: #f8f9fa;
  padding: 80px 0;
}





.feature-card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #e7f1ff;
  color: #0d6efd;
  margin-bottom: 1rem;
}

/* How It Works Section */
.how-it-works-section {
  padding: 80px 0;
  background-color: white;
}

.process-row {
  position: relative;
}

.process-row:after {
  content: '';
  position: absolute;
  top: 35px;
  left: 15%;
  right: 15%;
  height: 2px;
  background-color: #e9ecef;
  z-index: 0;
}

.process-item {
  text-align: center;
  position: relative;
  z-index: 1;
}

.process-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #0d6efd;
  color: white;
  font-size: 24px;
  font-weight: 700;
  margin: 0 auto 1.5rem;
}

.process-item h4 {
  margin-bottom: 1rem;
  font-weight: 600;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #0a58ca 0%, #0d6efd 100%);
  color: white;
  padding: 80px 0;
}

/* Responsive */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }

  .process-row:after {
    display: none;
  }
}

@media (max-width: 576px) {
  
}

/* Dashboard Stats Styling */
