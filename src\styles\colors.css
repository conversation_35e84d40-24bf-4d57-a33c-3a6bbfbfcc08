/**
 * Global renk tanımlamaları
 * Tüm proje bu değişkenleri kullanmalıdır
 */
:root {
  /* <PERSON> */
  --primary-color: #FF6200; /* Eski renk: #ec3323 */
  --primary-dark: color-mix(in srgb, var(--primary-color) 80%, black); /* %20 daha koyu versiyon */
  --primary-light: color-mix(in srgb, var(--primary-color) 80%, white); /* %20 daha açık versiyon */
  
  /* RGBA Kullanımı İçin RGB Değerleri */
  --primary-color-rgb: 255, 98, 0; /* Eski: 236, 51, 35 */
  
  /* Şeffaf versiyonlar */
  --primary-transparent: rgba(var(--primary-color-rgb), 0.1); /* Eski: rgba(236, 51, 35, 0.1) */
  --primary-shadow: rgba(var(--primary-color-rgb), 0.15); /* Eski: rgba(236, 51, 35, 0.15) */
  
  /* <PERSON>kin<PERSON><PERSON> */
  --secondary-color: #2e393f; /* Ana ikincil renk - koyu lacivert */
  --secondary-dark: color-mix(in srgb, var(--secondary-color) 80%, black); /* %20 daha koyu versiyon */
  --secondary-light: color-mix(in srgb, var(--secondary-color) 80%, white); /* %20 daha açık versiyon */
  
  /* RGBA Kullanımı İçin RGB Değerleri - Secondary */
  --secondary-color-rgb: 0, 18, 56; /* #001238'in RGB değerleri */
  
  /* Secondary için şeffaf versiyonlar */
  --secondary-transparent: rgba(var(--secondary-color-rgb), 0.1);
  --secondary-shadow: rgba(var(--secondary-color-rgb), 0.15);
  
  /* Standart renk paleti */
  --text-gray: #555555;
  --text-light-gray: #6c757d;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #555555;
  --white: #fff;
  --black: #000;
  
  /* UI renkleri */
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #17a2b8;
  
  /* Soft Renk Paleti */
  --soft-primary: #FFF1E6; /* Primary için soft arka plan */
  --soft-success: #E6F7EF; /* Success için soft arka plan */
  --soft-warning: #FFF8E6; /* Warning için soft arka plan */
  --soft-danger: #FFEBEB; /* Danger için soft arka plan */
  --soft-info: #E6F3F7; /* Info için soft arka plan */
  
  /* Gri Tonları */
  --gray-50: #F8F9FA;  /* En açık gri - arkaplan */
  --gray-100: #F1F3F5; /* Çok açık gri - alternatif arkaplan */
  --gray-200: #E9ECEF; /* Açık gri - kenarlıklar */
  --gray-300: #DEE2E6; /* Kenarlıklar için */
  --gray-400: #CED4DA; /* Açık metin - disabled */
  --gray-500: #ADB5BD; /* Orta ton gri - placeholder */
  --gray-600: #6C757D; /* Standart metin rengi */
  --gray-700: #495057; /* Koyu metin rengi */
  --gray-800: #343A40; /* Çok koyu metin */
  --gray-900: #212529; /* En koyu metin */

  /* Eski değişkene uyumluluk için eşleme (geriye dönük uyumluluk) */
  --app-primary: var(--primary-color);
  
  /* Brand renk tanımları - Diğer bileşenlerle uyumluluk için */
  --brand-color: var(--primary-color);
  --brand-color-hover: var(--primary-dark);
  --brand-color-light: var(--primary-light);
  --brand-color-lighter: rgba(var(--primary-color-rgb), 0.05);
  --brand-color-shadow: var(--primary-shadow);
}

/* Geriye dönük uyumluluk için yardımcı sınıflar */
.primary-color { color: var(--primary-color); }
.primary-bg { background-color: var(--primary-color); }
.primary-border { border-color: var(--primary-color); }

.secondary-color { color: var(--secondary-color); }
.secondary-bg { background-color: var(--secondary-color); }
.secondary-border { border-color: var(--secondary-color); } 

/* Bootstrap tema renk özelleştirmeleri */
.btn-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
  background-color: var(--primary-dark) !important;
  border-color: var(--primary-dark) !important;
}

.btn-outline-primary {
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

.btn-outline-primary:hover, .btn-outline-primary:focus, .btn-outline-primary:active {
  background-color: var(--primary-color) !important;
  color: white !important;
}

/* Secondary button ayarları */
.btn-secondary {
  background-color: var(--secondary-color) !important;
  border-color: var(--secondary-color) !important;
}

.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
  background-color: var(--secondary-dark) !important;
  border-color: var(--secondary-dark) !important;
}

.btn-outline-secondary {
  border-color: var(--secondary-color) !important;
  color: var(--secondary-color) !important;
}

.btn-outline-secondary:hover, .btn-outline-secondary:focus, .btn-outline-secondary:active {
  background-color: var(--secondary-color) !important;
  color: white !important;
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.border-primary {
  border-color: var(--primary-color) !important;
}

.border-secondary {
  border-color: var(--secondary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.nav-pills .nav-link.active, 
.nav-pills .show > .nav-link {
  background-color: var(--primary-color) !important;
}

.dropdown-item.active, 
.dropdown-item:active {
  background-color: var(--primary-color) !important;
}

.page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: var(--white) !important;
}

.page-link {
  color: var(--primary-color) !important;
}

.page-link:hover {
  color: var(--primary-dark) !important;
}

.form-control:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem var(--primary-transparent) !important;
}

.custom-control-input:checked ~ .custom-control-label::before {
  border-color: var(--primary-color) !important;
  background-color: var(--primary-color) !important;
}

a {
  color: var(--primary-color);
}

a:hover {
  color: var(--primary-dark);
} 