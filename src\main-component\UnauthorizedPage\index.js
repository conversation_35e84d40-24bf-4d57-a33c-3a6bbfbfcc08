import React, { Fragment } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { FaLock, FaHome } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import './style.css';

const UnauthorizedPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  return (
    <Fragment>
      <section className="unauthorized-section section_70">
        <Container>
          <Row className="justify-content-center">
            <Col md={8} lg={6}>
              <Card className="unauthorized-card text-center">
                <Card.Body>
                  <div className="icon-container mb-4">
                    <FaLock size={60} />
                  </div>
                  <h1 className="mb-3">{t('unauthorized.title') || 'Yet<PERSON>iz Erişim'}</h1>
                  <p className="lead mb-4">
                    {t('unauthorized.message') || 'Bu sayfaya erişmek için gerekli yetkilere sahip değilsiniz.'}
                  </p>
                  <p className="text-muted mb-4">
                    {t('unauthorized.info') || 'Farklı bir hesapla giriş yapabilir veya ana sayfaya dönebilirsiniz.'}
                  </p>
                  <div className="d-grid gap-2 d-md-flex justify-content-center">
                    <Button 
                      variant="primary" 
                      className="me-md-2"
                      onClick={() => navigate('/dashboard')}
                    >
                      {t('unauthorized.dashboard') || 'Dashboard\'a Git'}
                    </Button>
                    <Button 
                      variant="outline-primary"
                      onClick={() => navigate('/')}
                    >
                      <FaHome className="me-2" />
                      {t('unauthorized.home') || 'Ana Sayfa'}
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>
    </Fragment>
  );
};

export default UnauthorizedPage; 