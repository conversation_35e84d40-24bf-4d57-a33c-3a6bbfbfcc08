import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Row, Col, InputGroup, Badge } from 'react-bootstrap';
import { FaEdit, FaSave, FaTrash, FaTimes, FaCheck, FaCar, FaFileAlt } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

/**
 * Tek bir teklifi temsil eden satır bileşeni
 * Düzenleme, kaydetme, silme işlemlerini yönetir
 */
const BidRow = ({ bid, index, tender, onUpdate, onDelete, onToggleEdit, onSaveAsDraft }) => {
  const { t } = useTranslation();
  
  const [localData, setLocalData] = useState({
    totalAmount: bid.totalAmount || '',
    description: bid.description || '',
    terms: bid.terms || '',
    vehiclePrices: bid.vehiclePrices || {}
  });

  const [totalPrice, setTotalPrice] = useState(0);

  // Araç fiyatları değiştiğinde toplam fiyatı hesapla
  useEffect(() => {
    let total = 0;
    Object.values(localData.vehiclePrices).forEach(price => {
      total += parseFloat(price || 0);
    });
    setTotalPrice(total);
    
    // Toplam tutarı otomatik güncelle
    setLocalData(prev => ({
      ...prev,
      totalAmount: total.toString()
    }));
  }, [localData.vehiclePrices]);

  // Yerel veri değişikliklerini üst bileşene bildir
  const handleLocalChange = (field, value) => {
    const newData = { ...localData, [field]: value };
    setLocalData(newData);
    onUpdate(newData);
  };

  // Araç fiyatı değişikliği
  const handleVehiclePriceChange = (vehicleId, price) => {
    const newVehiclePrices = {
      ...localData.vehiclePrices,
      [vehicleId]: price
    };
    
    setLocalData(prev => ({
      ...prev,
      vehiclePrices: newVehiclePrices
    }));
    
    onUpdate({ vehiclePrices: newVehiclePrices });
  };

  // Kaydetme işlemi
  const handleSave = () => {
    onUpdate({ ...localData, isEditing: false });
    onSaveAsDraft();
  };

  // İptal etme işlemi
  const handleCancel = () => {
    // Orijinal verilere geri dön
    setLocalData({
      totalAmount: bid.totalAmount || '',
      description: bid.description || '',
      terms: bid.terms || '',
      vehiclePrices: bid.vehiclePrices || {}
    });
    onToggleEdit();
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount || 0);
  };

  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className={`bid-row mb-3 ${bid.isEditing ? 'editing' : ''} ${bid.isDraft ? 'draft' : 'submitted'}`}>
      <Card.Header className="d-flex justify-content-between align-items-center py-2">
        <div className="d-flex align-items-center gap-3">
          <Badge bg={bid.isDraft ? 'warning' : 'success'} className="bid-index">
            #{index}
          </Badge>
          <div>
            <h6 className="mb-0">
              {t('tender.bid.bid_title', 'Teklif {{index}}', { index })}
              {bid.isDraft && (
                <Badge bg="warning" className="ms-2 small">
                  {t('tender.bid.draft', 'Taslak')}
                </Badge>
              )}
            </h6>
            {(bid.createdAt || bid.updatedAt || bid.savedAt) && (
              <small className="text-muted">
                {bid.savedAt && t('tender.bid.saved_at', 'Kaydedildi: {{date}}', { date: formatDate(bid.savedAt) })}
                {!bid.savedAt && bid.updatedAt && t('tender.bid.updated_at', 'Güncellendi: {{date}}', { date: formatDate(bid.updatedAt) })}
                {!bid.savedAt && !bid.updatedAt && bid.createdAt && t('tender.bid.created_at', 'Oluşturuldu: {{date}}', { date: formatDate(bid.createdAt) })}
              </small>
            )}
          </div>
        </div>
        
        <div className="d-flex gap-2">
          {bid.isEditing ? (
            <>
              <Button 
                variant="success" 
                size="sm"
                onClick={handleSave}
                disabled={!localData.totalAmount || parseFloat(localData.totalAmount) <= 0}
              >
                <FaSave className="me-1" />
                {t('common.save', 'Kaydet')}
              </Button>
              <Button variant="outline-secondary" size="sm" onClick={handleCancel}>
                <FaTimes className="me-1" />
                {t('common.cancel', 'İptal')}
              </Button>
            </>
          ) : (
            <>
              <Button variant="outline-primary" size="sm" onClick={onToggleEdit}>
                <FaEdit className="me-1" />
                {t('common.edit', 'Düzenle')}
              </Button>
              <Button variant="outline-danger" size="sm" onClick={onDelete}>
                <FaTrash className="me-1" />
                {t('common.delete', 'Sil')}
              </Button>
            </>
          )}
        </div>
      </Card.Header>

      <Card.Body>
        {bid.isEditing ? (
          // Düzenleme Modu
          <div className="bid-edit-form">
            {/* Araç Fiyatları */}
            {tender?.requiredVehicles && tender.requiredVehicles.length > 0 && (
              <div className="mb-4">
                <h6 className="mb-3 d-flex align-items-center">
                  <FaCar className="me-2 text-primary" />
                  {t('tender.bid.vehicle_prices', 'Araç Fiyatları')}
                </h6>
                <Row className="g-3">
                  {tender.requiredVehicles.map((vehicle) => (
                    <Col md={6} key={vehicle.id}>
                      <Form.Group>
                        <Form.Label className="small fw-bold">
                          {vehicle.type} ({vehicle.quantity} {t('tender.vehicle.units', 'adet')})
                        </Form.Label>
                        <InputGroup size="sm">
                          <Form.Control
                            type="number"
                            placeholder={t('tender.bid.price_per_vehicle', 'Araç başına fiyat')}
                            value={localData.vehiclePrices[vehicle.id] || ''}
                            onChange={(e) => handleVehiclePriceChange(vehicle.id, e.target.value)}
                          />
                          <InputGroup.Text>₺</InputGroup.Text>
                        </InputGroup>
                      </Form.Group>
                    </Col>
                  ))}
                </Row>
              </div>
            )}

            {/* Toplam Tutar */}
            <Row className="mb-4">
              <Col md={6}>
                <Form.Group>
                  <Form.Label className="fw-bold">
                    {t('tender.bid.total_amount', 'Toplam Tutar')}
                  </Form.Label>
                  <InputGroup>
                    <Form.Control
                      type="number"
                      value={localData.totalAmount}
                      onChange={(e) => handleLocalChange('totalAmount', e.target.value)}
                      placeholder="0"
                      readOnly={tender?.requiredVehicles?.length > 0}
                    />
                    <InputGroup.Text>₺</InputGroup.Text>
                  </InputGroup>
                  {tender?.requiredVehicles?.length > 0 && (
                    <Form.Text className="text-muted">
                      {t('tender.bid.auto_calculated', 'Araç fiyatlarından otomatik hesaplanır')}
                    </Form.Text>
                  )}
                </Form.Group>
              </Col>
            </Row>

            {/* Açıklama ve Şartlar */}
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>{t('tender.bid.description', 'Açıklama')}</Form.Label>
                  <Form.Control 
                    as="textarea"
                    rows={3}
                    value={localData.description}
                    onChange={(e) => handleLocalChange('description', e.target.value)}
                    placeholder={t('tender.bid.description_placeholder', 'Teklifiniz hakkında detaylı bilgi...')}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>{t('tender.bid.terms', 'Şartlar ve Koşullar')}</Form.Label>
                  <Form.Control 
                    as="textarea"
                    rows={3}
                    value={localData.terms}
                    onChange={(e) => handleLocalChange('terms', e.target.value)}
                    placeholder={t('tender.bid.terms_placeholder', 'Ödeme şartları, teslimat koşulları vb.')}
                  />
                </Form.Group>
              </Col>
            </Row>
          </div>
        ) : (
          // Görüntüleme Modu
          <div className="bid-view">
            <Row>
              <Col md={8}>
                <div className="bid-summary">
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h5 className="text-primary mb-0">
                      {formatCurrency(localData.totalAmount)}
                    </h5>
                    <Badge bg={parseFloat(localData.totalAmount) > 0 ? 'success' : 'secondary'}>
                      {parseFloat(localData.totalAmount) > 0 ? (
                        <>
                          <FaCheck className="me-1" />
                          {t('tender.bid.ready', 'Hazır')}
                        </>
                      ) : (
                        t('tender.bid.incomplete', 'Eksik')
                      )}
                    </Badge>
                  </div>

                  {/* Araç Fiyat Özeti */}
                  {tender?.requiredVehicles && Object.keys(localData.vehiclePrices).length > 0 && (
                    <div className="vehicle-prices-summary mb-3">
                      <h6 className="small text-muted mb-2">
                        <FaCar className="me-1" />
                        {t('tender.bid.vehicle_breakdown', 'Araç Fiyat Dağılımı')}
                      </h6>
                      <div className="small">
                        {tender.requiredVehicles.map(vehicle => (
                          <div key={vehicle.id} className="d-flex justify-content-between">
                            <span>{vehicle.type} ({vehicle.quantity} adet):</span>
                            <span className="fw-bold">
                              {formatCurrency(localData.vehiclePrices[vehicle.id] || 0)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Açıklama ve Şartlar */}
                  {(localData.description || localData.terms) && (
                    <div className="bid-details">
                      {localData.description && (
                        <div className="mb-2">
                          <small className="text-muted d-flex align-items-center">
                            <FaFileAlt className="me-1" />
                            {t('tender.bid.description', 'Açıklama')}:
                          </small>
                          <p className="small mb-0">{localData.description}</p>
                        </div>
                      )}
                      {localData.terms && (
                        <div>
                          <small className="text-muted d-flex align-items-center">
                            <FaFileAlt className="me-1" />
                            {t('tender.bid.terms', 'Şartlar')}:
                          </small>
                          <p className="small mb-0">{localData.terms}</p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Col>
            </Row>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

BidRow.propTypes = {
  bid: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  tender: PropTypes.object.isRequired,
  onUpdate: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  onToggleEdit: PropTypes.func.isRequired,
  onSaveAsDraft: PropTypes.func.isRequired
};

export default BidRow;
