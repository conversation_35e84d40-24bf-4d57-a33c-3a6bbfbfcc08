import axios from 'axios';

// API için temel URL (environment değişkeninden veya varsayılan değer)
// Local
 const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://localhost:7198/api';
// Test ortamı
//const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://ltr-api-8qjd4.ondigitalocean.app/api';
 // Test ortamı 2
// const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://rentapi.anessoftware.com/api';
// API için genel yapılandırma ayarları
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// Mock API kullanımını kontrol eden flag - diğer componentler için tekrar aktif edildi
export const useMockAPI = false;

// Request interceptor - her istekte çalışır
api.interceptors.request.use(
  (config) => {
    // Yerel depodan token'ı al
    let token = localStorage.getItem('token') || sessionStorage.getItem('token');

    // Token varsa Authorization header'ına ekle
    if (token) {
      // "Bearer " önekini kaldır (birden fazla olabilir)
      while (token.startsWith('Bearer ')) {
        token = token.slice(7);
      }

      // Temizlenmiş token'a Bearer öneki ekle
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    // İstek hatası durumunda
    return Promise.reject(error);
  }
);

// Response interceptor - her yanıtta çalışır
api.interceptors.response.use(
  (response) => {
    // Başarılı yanıt
    return response;
  },
  (error) => {
    // Yetkisiz erişim (401) durumunda otomatik çıkış yap
    // Ancak User profil endpointleri için bu işlemi yapma
    if (error.response && error.response.status === 401) {
      // User profil endpointleri için özel kontrol
      const url = error.config?.url || '';
      const isUserProfileEndpoint = url.includes('/User/GetUserProfile') ||
                                   url.includes('/User/UpdateUserProfile') ||
                                   url.includes('/User/UpdateUserPassword');

      // Eğer User profil endpointi değilse, logout işlemini yap
      if (!isUserProfileEndpoint) {
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Giriş sayfasına yönlendir
        window.location.href = '/login';
      }
    }

    // Genel hata durumu
    return Promise.reject(error);
  }
);

export default api;