/* DashboardStats Component Styles */
@import url('../../styles/colors.css');

/* ============== Layout & Container Styles ============== */
.dashboard-stats-section {
  margin-bottom: 25px;
}

.dashboard-stats-row {
  margin-right: -10px;
  margin-left: -10px;
}

.dashboard-stats-col {
  padding-right: 10px;
  padding-left: 10px;
  margin-bottom: 15px;
}

/* ============== Heading Styles ============== */
.dashboard-heading {
  margin-bottom: 30px !important;
}

/* ============== Card Styles ============== */
.stat-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: var(--white);
  border-radius: 15px;
  padding: 22px 25px;
  margin-bottom: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  border: 1px solid #f5f5f5;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  padding-right: 15px;
  min-width: 0; /* Metinlerin taşmasını önler */
}

.stat-title {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 10px;
  font-weight: normal;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-value {
  font-size: 30px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  line-height: 1.1;
}

.stat-trend {
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 2px;
  flex-wrap: wrap;
}

.trend-icon {
  color: #4caf50;
  font-weight: bold;
}

.trend-value {
  color: #4caf50;
  font-weight: 500;
}

.trend-text {
  color: #4caf50;
  font-weight: 400;
}

.trend-period {
  color: #999;
  margin-left: 2px;
  font-weight: 400;
}

.stat-icon-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding-top: 5px;
  margin-left: 5px;
}

.stat-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 46px;
  height: 46px;
  border-radius: 50%;
  position: relative;
}

.stat-icon {
  font-size: 18px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-bg-orange {
  background-color: rgba(255, 152, 0, 0.12);
}

.icon-bg-dark {
  background-color: rgba(33, 33, 33, 0.05);
}

.icon-bg-green {
  background-color: rgba(76, 175, 80, 0.12);
}

.icon-bg-blue {
  background-color: rgba(33, 150, 243, 0.12);
}

.icon-bg-orange .stat-icon {
  color: #ff9800;
}

.icon-bg-dark .stat-icon {
  color: #333;
}

.icon-bg-green .stat-icon {
  color: #4caf50;
}

.icon-bg-blue .stat-icon {
  color: #2196f3;
}

.tenders-icon {
  color: #ff9800;
}

.bids-icon {
  color: #212121;
}

.calendar-icon {
  color: #4caf50;
}

.vehicles-icon {
  color: #2196f3;
}

/* ============== Responsive Styles ============== */
/* Büyük tablet ve küçük masaüstü için */
@media (max-width: 1200px) {
  .stat-card {
    padding: 20px 22px;
  }
  
  .stat-value {
    font-size: 28px;
  }
  
  .stat-icon-container {
    width: 42px;
    height: 42px;
  }
  
  .stat-icon {
    font-size: 17px;
  }
}

/* Tablet ekranları için */
@media (max-width: 992px) {
  .stat-card {
    padding: 18px 20px;
  }
  
  .stat-value {
    font-size: 26px;
    margin-bottom: 8px;
  }
  
  .stat-title {
    font-size: 0.85rem;
    margin-bottom: 8px;
  }
  
  .stat-trend {
    font-size: 0.75rem;
  }
  
  .stat-icon-container {
    width: 40px;
    height: 40px;
  }
  
  .stat-icon {
    font-size: 16px;
  }
}

/* Küçük tablet ve büyük telefon için */
@media (max-width: 768px) {
  .stat-card {
    padding: 16px 18px;
  }
  
  .stat-content {
    padding-right: 10px;
  }
  
  .stat-value {
    font-size: 24px;
    margin-bottom: 6px;
  }
  
  .stat-title {
    font-size: 0.8rem;
    margin-bottom: 6px;
  }
  
  .stat-trend {
    font-size: 0.7rem;
  }
  
  .stat-icon-container {
    width: 36px;
    height: 36px;
  }
  
  .stat-icon {
    font-size: 15px;
  }
}

/* Telefon için */
@media (max-width: 576px) {
  .dashboard-stats-col {
    margin-bottom: 12px;
  }
  
  .stat-card {
    padding: 14px 16px;
  }
  
  .stat-content {
    padding-right: 8px;
  }
  
  .stat-value {
    font-size: 22px;
    margin-bottom: 5px;
  }
  
  .stat-title {
    font-size: 0.75rem;
    margin-bottom: 5px;
  }
  
  .stat-trend {
    font-size: 0.65rem;
  }
  
  .stat-icon-container {
    width: 32px;
    height: 32px;
  }
  
  .stat-icon {
    font-size: 14px;
  }
}

/* Çok küçük telefon ekranları için */
@media (max-width: 400px) {
  .stat-card {
    padding: 12px 14px;
  }
  
  .stat-content {
    padding-right: 6px;
  }
  
  .stat-value {
    font-size: 20px;
    margin-bottom: 4px;
  }
  
  .stat-title {
    font-size: 0.7rem;
    margin-bottom: 4px;
  }
  
  .stat-trend {
    font-size: 0.6rem;
  }
  
  .stat-icon-container {
    width: 28px;
    height: 28px;
  }
  
  .stat-icon {
    font-size: 12px;
  }
} 