import { configureStore } from '@reduxjs/toolkit';
import authReducer from '../slices/authSlice';
import vehiclesReducer from '../slices/vehiclesSlice';
import tendersReducer from '../slices/tendersSlice';
import cartReducer from '../slices/cartSlice';
import uiReducer from '../slices/uiSlice';
import tenderReducer from '../slices/tenderSlice';
import contractsReducer from '../slices/contractsSlice';
import userReducer from '../slices/userSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    vehicles: vehiclesReducer,
    tenders: tendersReducer,
    tender: tenderReducer,
    contracts: contractsReducer,
    cart: cartReducer,
    ui: uiReducer,
    user: userReducer,
  },
});

export default store;