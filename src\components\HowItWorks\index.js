import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";
import { ArcherContainer, ArcherElement } from "react-archer";
import SectionTitle from "../common/SectionTitle";

import "./style.css";

const HowItWorks = () => {
  const { t } = useTranslation();
  
  // Mobil görünümü kontrol etmek için
  const [isMobile, setIsMobile] = useState(false);
  
  // Primary color değişkenini document'dan alıyoruz
  const [primaryColor, setPrimaryColor] = useState('#FF6200'); // Default turuncu rengi
  
  // ArcherContainer referansı
  const archerRef = useRef(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 992);
    };
    
    // İlk yükleme kontrolü
    checkMobile();
    
    // Primary color CSS değişkeninden alalım
    const getPrimaryColor = () => {
      const color = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
      if (color) {
        setPrimaryColor(color);
      }
    };
    
    getPrimaryColor();
    
    // Ekran boyutu değiştiğinde kontrol et
    window.addEventListener('resize', checkMobile);
    
    // Sayfanın yüklenmesinden 1 saniye sonra okları yeniden çiz
    const timer = setTimeout(() => {
      if (archerRef.current) {
        archerRef.current.refreshScreen();
      }
    }, 1000);
    
    // Ekstra yenileme için
    const extraRefreshTimer = setTimeout(() => {
      if (archerRef.current) {
        archerRef.current.refreshScreen();
      }
    }, 2000);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timer);
      clearTimeout(extraRefreshTimer);
    };
  }, []);

  return (
    <section className="how-it-works section_70">
      <Container>
        <Row className="text-center">
          <Col md={12}>
            <SectionTitle
              subtitle={t("about_page.how_it_works_subtitle")}
              title={t("about_page.how_it_works_title")}
              alignment="center"
            />
          </Col>
        </Row>
        
        <div className="position-relative mt-5">
          {!isMobile ? (
            <ArcherContainer 
              ref={archerRef}
              strokeColor={primaryColor}
              strokeWidth={2}
              strokeDasharray="5,8"
              noCurves={false}
              svgContainerStyle={{ 
                zIndex: 2,
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                pointerEvents: 'none',
                overflow: 'visible'
              }}
              offset={8}
              endMarker={false}
              arrowLength={0}
            >
              <Row className="justify-content-center">
                <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                  <div className="process-step-card">
                    <ArcherElement
                      id="step1"
                      relations={[{
                        targetId: 'step2',
                        targetAnchor: 'left',
                        sourceAnchor: 'right',
                        style: {
                          strokeColor: primaryColor,
                          strokeWidth: 3,
                          strokeDasharray: '6,8',
                          noCurves: false,
                          curveness: 0.8
                        }
                      }]}
                    >
                      <div className="process-step-number">
                        <span>1</span>
                      </div>
                    </ArcherElement>
                    <div className="process-step-content">
                      <h3>{t("about_page.step_1_title")}</h3>
                      <p>{t("about_page.step_1_text")}</p>
                    </div>
                  </div>
                </Col>
                
                <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                  <div className="process-step-card">
                    <ArcherElement
                      id="step2"
                      relations={[{
                        targetId: 'step3',
                        targetAnchor: 'left',
                        sourceAnchor: 'right',
                        style: {
                          strokeColor: primaryColor,
                          strokeWidth: 3,
                          strokeDasharray: '6,8',
                          noCurves: false,
                          curveness: 0.8
                        }
                      }]}
                    >
                      <div className="process-step-number">
                        <span>2</span>
                      </div>
                    </ArcherElement>
                    <div className="process-step-content">
                      <h3>{t("about_page.step_2_title")}</h3>
                      <p>{t("about_page.step_2_text")}</p>
                    </div>
                  </div>
                </Col>
                
                <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                  <div className="process-step-card">
                    <ArcherElement
                      id="step3"
                      relations={[{
                        targetId: 'step4',
                        targetAnchor: 'left',
                        sourceAnchor: 'right',
                        style: {
                          strokeColor: primaryColor,
                          strokeWidth: 3,
                          strokeDasharray: '6,8',
                          noCurves: false,
                          curveness: 0.8
                        }
                      }]}
                    >
                      <div className="process-step-number">
                        <span>3</span>
                      </div>
                    </ArcherElement>
                    <div className="process-step-content">
                      <h3>{t("about_page.step_3_title")}</h3>
                      <p>{t("about_page.step_3_text")}</p>
                    </div>
                  </div>
                </Col>
                
                <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                  <div className="process-step-card">
                    <ArcherElement id="step4">
                      <div className="process-step-number">
                        <span>4</span>
                      </div>
                    </ArcherElement>
                    <div className="process-step-content">
                      <h3>{t("about_page.step_4_title")}</h3>
                      <p>{t("about_page.step_4_text")}</p>
                    </div>
                  </div>
                </Col>
              </Row>
            </ArcherContainer>
          ) : (
            <Row className="justify-content-center">
              <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                <div className="process-step-card">
                  <div className="process-step-number">
                    <span>1</span>
                  </div>
                  <div className="process-step-content">
                    <h3>{t("about_page.step_1_title")}</h3>
                    <p>{t("about_page.step_1_text")}</p>
                  </div>
                </div>
              </Col>
              
              <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                <div className="process-step-card">
                  <div className="process-step-number">
                    <span>2</span>
                  </div>
                  <div className="process-step-content">
                    <h3>{t("about_page.step_2_title")}</h3>
                    <p>{t("about_page.step_2_text")}</p>
                  </div>
                </div>
              </Col>
              
              <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                <div className="process-step-card">
                  <div className="process-step-number">
                    <span>3</span>
                  </div>
                  <div className="process-step-content">
                    <h3>{t("about_page.step_3_title")}</h3>
                    <p>{t("about_page.step_3_text")}</p>
                  </div>
                </div>
              </Col>
              
              <Col lg={3} md={6} sm={12} className="mb-4 process-step-col">
                <div className="process-step-card">
                  <div className="process-step-number">
                    <span>4</span>
                  </div>
                  <div className="process-step-content">
                    <h3>{t("about_page.step_4_title")}</h3>
                    <p>{t("about_page.step_4_text")}</p>
                  </div>
                </div>
              </Col>
            </Row>
          )}
        </div>
      </Container>
    </section>
  );
};

export default HowItWorks; 