import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunk action creators
export const fetchTenders = createAsyncThunk(
  'tenders/fetchTenders',
  async (filters = {}, { rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.get('/tenders', { params: filters });
      
      // Simüle edilmiş veri
      const mockTenders = [
        {
          id: 1,
          title: 'Belediye Araç Kiralama İhalesi',
          description: 'Belediye hizmetleri için 10 adet araç kiralama ihalesi',
          startDate: '2025-04-01',
          endDate: '2025-04-15',
          status: 'ACTIVE',
          minBudget: 5000,
          maxBudget: 15000,
          requiredVehicles: [
            { type: 'SUV', count: 3 },
            { type: 'Sedan', count: 5 },
            { type: 'Van', count: 2 },
          ],
          createdBy: 'Ankara Belediyesi',
          createdAt: '2025-03-01',
        },
        {
          id: 2,
          title: 'Turizm Firması Araç Kiralama',
          description: '<PERSON><PERSON> sezonu için lüks araç kiralama ihalesi',
          startDate: '2025-05-01',
          endDate: '2025-05-20',
          status: 'PENDING',
          minBudget: 20000,
          maxBudget: 40000,
          requiredVehicles: [
            { type: 'Luxury', count: 5 },
            { type: 'SUV', count: 3 },
          ],
          createdBy: 'Blue Tours',
          createdAt: '2025-03-05',
        },
        {
          id: 3,
          title: 'Kurumsal Filo Kiralama İhalesi',
          description: 'Şirket çalışanları için 20 araçlık filo kiralama',
          startDate: '2025-06-01',
          endDate: '2025-06-15',
          status: 'ACTIVE',
          minBudget: 30000,
          maxBudget: 50000,
          requiredVehicles: [
            { type: 'Sedan', count: 15 },
            { type: 'SUV', count: 5 },
          ],
          createdBy: 'TechCorp',
          createdAt: '2025-03-10',
        },
      ];
      
      // Filtreleme işlemi
      let filteredTenders = [...mockTenders];
      
      if (filters.status) {
        filteredTenders = filteredTenders.filter(
          (tender) => tender.status.toLowerCase() === filters.status.toLowerCase()
        );
      }
      
      if (filters.createdBy) {
        filteredTenders = filteredTenders.filter(
          (tender) => tender.createdBy.toLowerCase().includes(filters.createdBy.toLowerCase())
        );
      }
      
      return filteredTenders;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'İhale verileri alınamadı');
    }
  }
);

export const fetchTenderById = createAsyncThunk(
  'tenders/fetchTenderById',
  async (tenderId, { rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.get(`/tenders/${tenderId}`);
      
      // Simüle edilmiş veri
      const mockTender = {
        id: tenderId,
        title: 'Belediye Araç Kiralama İhalesi',
        description: 'Belediye hizmetleri için 10 adet araç kiralama ihalesi',
        longDescription: 'Belediye hizmetlerinde kullanılmak üzere çeşitli türde araçların kiralanması için düzenlenen ihale. Bu ihaleye katılacak firmalar, belirtilen araç tiplerini belirtilen süre boyunca temin etmeyi taahhüt etmelidir. Araçlar, belediye logosu takılabilecek şekilde teslim edilmelidir.',
        startDate: '2025-04-01',
        endDate: '2025-04-15',
        status: 'ACTIVE',
        minBudget: 5000,
        maxBudget: 15000,
        requiredVehicles: [
          { type: 'SUV', count: 3 },
          { type: 'Sedan', count: 5 },
          { type: 'Van', count: 2 },
        ],
        createdBy: 'Ankara Belediyesi',
        createdAt: '2025-03-01',
        documents: [
          { id: 1, name: 'Şartname.pdf', url: '/documents/1' },
          { id: 2, name: 'Teknik Detaylar.pdf', url: '/documents/2' },
        ],
        bids: [
          { id: 1, company: 'A Rent A Car', amount: 12000, status: 'PENDING', createdAt: '2025-03-05' },
          { id: 2, company: 'B Araç Kiralama', amount: 10500, status: 'PENDING', createdAt: '2025-03-07' },
        ],
      };
      
      return mockTender;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'İhale detayları alınamadı');
    }
  }
);

export const createBid = createAsyncThunk(
  'tenders/createBid',
  async ({ tenderId, bidData }, { rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.post(`/tenders/${tenderId}/bids`, bidData);
      
      // Simüle edilmiş veri
      const mockBid = {
        id: Math.floor(Math.random() * 1000) + 10,
        tenderId,
        company: bidData.company,
        amount: bidData.amount,
        description: bidData.description,
        status: 'PENDING',
        createdAt: new Date().toISOString(),
      };
      
      return mockBid;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Teklif oluşturulamadı');
    }
  }
);

export const createTender = createAsyncThunk(
  'tenders/createTender',
  async (tenderData, { rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.post('/tenders', tenderData);
      
      // Simüle edilmiş veri
      const mockTender = {
        id: Math.floor(Math.random() * 1000) + 10,
        ...tenderData,
        status: 'PENDING',
        createdAt: new Date().toISOString(),
        bids: [],
      };
      
      return mockTender;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'İhale oluşturulamadı');
    }
  }
);

// Initial State
const initialState = {
  tenders: [],
  currentTender: null,
  userBids: [],
  loading: false,
  error: null,
  filters: {
    status: '',
    createdBy: '',
  },
};

// Tenders Slice
const tendersSlice = createSlice({
  name: 'tenders',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // fetchTenders
    builder
      .addCase(fetchTenders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTenders.fulfilled, (state, action) => {
        state.loading = false;
        state.tenders = action.payload;
      })
      .addCase(fetchTenders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // fetchTenderById
    builder
      .addCase(fetchTenderById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTenderById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentTender = action.payload;
      })
      .addCase(fetchTenderById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
      
    // createBid
    builder
      .addCase(createBid.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createBid.fulfilled, (state, action) => {
        state.loading = false;
        state.userBids.push(action.payload);
        if (state.currentTender && state.currentTender.id === action.payload.tenderId) {
          state.currentTender.bids = state.currentTender.bids || [];
          state.currentTender.bids.push(action.payload);
        }
      })
      .addCase(createBid.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
      
    // createTender
    builder
      .addCase(createTender.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createTender.fulfilled, (state, action) => {
        state.loading = false;
        state.tenders.push(action.payload);
      })
      .addCase(createTender.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setFilters, clearFilters, clearError } = tendersSlice.actions;

export default tendersSlice.reducer; 