@import url('../../styles/colors.css');

/* TenderPartnerBrands Styles */
.partner-brands-section {
  padding: 40px 0;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  position: relative;
  margin: 25px 0;
  border-radius: 15px;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.partner-brands-section:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
}

.partner-brands-heading {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
}

.partner-brands-heading:after {
  content: '';
  display: block;
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
  margin: 15px auto 0;
  border-radius: 3px;
}

.partner-brands-heading h3 {
  font-size: 26px;
  color: #001238;
  margin-bottom: 10px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.partner-brands-heading p {
  color: #666;
  font-size: 15px;
  max-width: 600px;
  margin: 0 auto;
}

.partner-brands-slider {
  padding: 10px 0 35px;
  margin: 0 -8px;
}

/* <PERSON>ni <PERSON>rt Tasarımı - K<PERSON><PERSON>lt<PERSON>lm<PERSON><PERSON> */
.partner-brand-item {
  text-align: center;
  padding: 10px;
  height: 100%;
  position: relative;
  z-index: 1;
}

.partner-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.05), 0 8px 20px rgba(0, 0, 0, 0.09);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.partner-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 22px 45px rgba(0, 0, 0, 0.15), 0 12px 25px rgba(0, 0, 0, 0.11);
  border-color: var(--primary-shadow);
}

.featured-badge {
  position: absolute;
  top: 0;
  left: 0;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 5px 10px;
  text-transform: uppercase;
  border-radius: 0 0 5px 0;
  z-index: 2;
}

.partner-card-header {
  padding: 20px 12px 12px;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  border-bottom: 1px solid #f1f1f1;
}

/* Yuvarlak logo ve hover büyütme efekti - Küçültülmüş */
.partner-logo-wrapper {
  height: 85px;
  width: 85px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 0;
  position: relative;
}

.partner-card:hover .partner-logo-wrapper {
  transform: scale(1.1);
  box-shadow: 0 10px 30px var(--primary-shadow);
}

.partner-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: grayscale(30%);
  opacity: 0.9;
  transition: all 0.3s ease;
}

.partner-card:hover .partner-logo {
  filter: grayscale(0%);
  opacity: 1;
}

.partner-logo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ddd;
  background-color: #f9f9f9;
  border-radius: 50%;
}

.partner-card-body {
  padding: 12px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.partner-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 5px 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
  height: 24px; /* sabit yükseklik */
}

.partner-card:hover .partner-name {
  color: var(--primary-color);
}

/* Açıklama kısmını tamamen kaldırıyoruz */
.partner-description {
  display: none;
}

/* İstatistik Stilleri - İyileştirilmiş yerleşim */
.partner-stats {
  display: flex;
  justify-content: space-around;
  margin: 0;
  padding: 10px 0;
  border-top: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background-color: var(--primary-transparent);
  border-radius: 5px;
}

.stat-icon {
  color: #777;
  font-size: 16px;
  margin-right: 6px;
  position: relative;
}

.stat-icon.win {
  color: var(--primary-color);
}

/* İstatistik içeriğini değiştirdik */
.stat-content {
  display: flex;
  align-items: center;
}

.stat-value {
  font-weight: 700;
  font-size: 18px;
  color: #333;
}

/* Label'ı gizle */
.stat-label {
  display: none;
}

/* Tooltip stillerini özelleştir */
.tooltip {
  font-size: 12px;
  opacity: 0.9 !important;
}

.tooltip-inner {
  background-color: #001238;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 6px 10px;
}

.bs-tooltip-top .arrow::before {
  border-top-color: #001238;
}

/* Footer - Geliştirilmiş yerleşim */
.partner-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  font-size: 11px;
  color: #888;
  padding-top: 8px;
  height: 28px; /* sabit yükseklik */
}

.since {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 50%;
}

.since-icon {
  margin-right: 4px;
  color: #aaa;
  min-width: 12px;
}

/* Rating Stars */
.partner-rating {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.partner-rating .star-filled,
.partner-rating .star-half {
  color: #f04c41;
  font-size: 12px;
  margin-right: 2px;
}

.partner-rating span {
  margin-left: 3px;
  font-weight: 600;
  color: #555;
}

@keyframes floating {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
  100% { transform: translateY(0px); }
}

/* Loading & Error States */
.partner-brands-loading,
.partner-brands-error,
.no-partners-found {
  text-align: center;
  padding: 50px 0;
  color: #666;
  background-color: #f9f9f9;
  border-radius: 10px;
}

.partner-brands-loading .fa-spin {
  animation: fa-spin 1.5s infinite cubic-bezier(0.68, -0.55, 0.27, 1.55);
  color: var(--primary-color);
  font-size: 35px;
  margin-bottom: 15px;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}

/* Slick Slider Overrides */
.slick-prev,
.slick-next {
  display: none !important; /* Okları gizle */
}

.slick-dots {
  bottom: -30px;
}

.slick-dots li button:before {
  font-size: 10px;
  color: #ddd;
  opacity: 1;
}

.slick-dots li.slick-active button:before {
  color: var(--primary-color);
  opacity: 1;
}

.slick-track {
  display: flex;
  padding: 10px 0;
}

.slick-slide {
  height: auto;
  margin: 0 8px;
}

.slick-slide > div {
  height: 100%;
}

/* Responsive */
@media (max-width: 991px) {
  .partner-brands-section {
    padding: 35px 0; 
  }
}

@media (max-width: 767px) {
  .partner-brands-heading h3 {
    font-size: 22px;
  }
  
  .partner-logo-wrapper {
    height: 80px;
    width: 80px;
  }
  
  .partner-brands-section {
    padding: 25px 15px;
    margin: 20px 0;
  }
}

@media (max-width: 480px) {
  .partner-brands-heading h3 {
    font-size: 20px;
  }
  
  .partner-brands-heading p {
    font-size: 13px;
  }
  
  .partner-brands-section {
    padding: 20px 10px;
  }
  
  .partner-logo-wrapper {
    height: 75px;
    width: 75px;
  }
  
  .partner-stats {
    flex-direction: column;
    align-items: center;
  }
  
  .stat-item {
    width: 100%;
    margin-bottom: 6px;
  }
}

.partner-logo-overlay {
  background-color: var(--primary-color);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 10px;
  right: 10px;
}

.partner-view-btn {
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: underline;
  margin-top: 5px;
  transition: all 0.3s ease;
}

.partner-brand-card:hover .partner-view-btn {
  color: var(--primary-color);
} 