import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import PageTitle from "../../components/PageTitle";
import Register from "../../components/Register";

const RegisterPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.register")}
        pagesub={t("header-navigation.register")}
      />
      <Register />
    </Fragment>
  );
};
export default RegisterPage;
