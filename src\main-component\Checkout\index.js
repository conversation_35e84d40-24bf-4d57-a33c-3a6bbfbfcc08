import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import Checkout from "../../components/Checkout";

const CheckoutPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.checkout")}
        pagesub={t("header-navigation.checkout")}
      />
      <Checkout />
    </Fragment>
  );
};
export default CheckoutPage;
