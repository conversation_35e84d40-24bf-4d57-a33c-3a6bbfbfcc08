// Rol tanımlamaları (API'den gelen rol ID'lerine göre)
export const USER_ROLES = {
  ADMIN: {
    id: 10,
    name: 'Admin',
    key: 'ADMIN',
    apiRoles: ['Role10'] // API'den gelen rol isimleri
  },
  CUSTOMER_SERVICE: {
    id: 20,
    name: 'Müşteri Hizmetleri',
    key: 'CS',
    apiRoles: ['Role20'] // API'den gelen rol isimleri
  },
  CUSTOMER: {
    id: 30,
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    key: 'Customer',
    apiRoles: ['Role30'] // API'den gelen rol isimleri
  }
};

// Rol ID'sine göre rol objesi getirme yardımcı fonksiyonu
export const getRoleById = (roleId) => {
  const roles = Object.values(USER_ROLES);
  return roles.find(role => role.id === roleId) || null;
};

// Rol key'ine göre rol objesi getirme yardımcı fonksiyonu
export const getRoleByKey = (key) => {
  return USER_ROLES[key] || null;
};

// API'den gelen rol dizisine göre rol ID'si belirleme
export const getRoleIdFromApiRoles = (apiRoles) => {
  if (!apiRoles || !Array.isArray(apiRoles) || apiRoles.length === 0) {
    return USER_ROLES.CUSTOMER.id; // Varsayılan olarak CUSTOMER rolü
  }

  // Role10 rolü varsa ADMIN olarak kabul et
  if (apiRoles.includes('Role10')) {
    return USER_ROLES.ADMIN.id;
  }

  // Role20 rolü varsa CUSTOMER_SERVICE olarak kabul et
  if (apiRoles.includes('Role20')) {
    return USER_ROLES.CUSTOMER_SERVICE.id;
  }

  // Role30 rolü varsa CUSTOMER olarak kabul et
  if (apiRoles.includes('Role30')) {
    return USER_ROLES.CUSTOMER.id;
  }

  return USER_ROLES.CUSTOMER.id; // Varsayılan olarak CUSTOMER rolü
};

// Kullanıcı rolüne göre ana sayfa path'i
export const getHomepagePathByRole = (roleId) => {
  switch (roleId) {
    case USER_ROLES.ADMIN.id:
      return '/admin/dashboard';
    case USER_ROLES.CUSTOMER_SERVICE.id:
      return '/cs/dashboard';
    case USER_ROLES.CUSTOMER.id:
      return '/dashboard';
    default:
      return '/'; // Rol belirlenmemişse ana sayfaya yönlendir
  }
};