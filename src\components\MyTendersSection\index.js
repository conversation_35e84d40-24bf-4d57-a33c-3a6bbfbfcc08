import React, { useState } from 'react';
import { <PERSON>, Badge, Spinner, Tab, Tabs } from 'react-bootstrap';
import { 
  FaPlus, 
  FaEye, 
  FaExclamationTriangle,
  FaCheckCircle,
  FaHourglassHalf,
  FaTimesCircle,
  FaArchive,
  FaFileAlt,
  FaHandshake,
  FaCarAlt
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import './style.css';

/**
 * Dashboard içeriğini ve tab yapısını yöneten bileşen
 */
const MyTendersSection = ({ 
  tenders = [],
  openTenders = [],
  myBids = { pending: [], approved: [], rejected: [] },
  onStartNewTender,
  onViewTender,
  onBidTender,
  isLoading = false
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('myTenders');

  // Badge rengini belirleme fonksiyonu
  const getBadgeColor = (status) => {
    switch(status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'closed': return 'secondary';
      case 'cancelled': return 'danger';
      default: return 'info';
    }
  };
  
  // Badge ikonu belirleme fonksiyonu
  const getStatusIcon = (status) => {
    switch(status) {
      case 'active': return <FaCheckCircle />;
      case 'pending': return <FaHourglassHalf />;
      case 'closed': return <FaArchive />;
      case 'cancelled': return <FaTimesCircle />;
      default: return null;
    }
  };

  // İhalelerim Tab İçeriği
  const renderMyTendersTab = () => (
    <Card className="border-0 shadow-sm mb-4">
      <Card.Header className="d-flex justify-content-between align-items-center bg-white py-3">
        <h5 className="mb-0 section-title">{t('dashboard.my_tenders')}</h5>
        <button onClick={onStartNewTender} className="btn btn-primary btn-with-icon">
          <FaPlus className="me-2" /> <span>{t('dashboard.start_new_tender')}</span>
        </button>
      </Card.Header>
      <Card.Body className="p-0">
        {isLoading ? (
          <div className="loading-container py-5">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3">{t('common.loading')}</p>
          </div>
        ) : tenders && tenders.length > 0 ? (
          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th>{t('dashboard.table.id')}</th>
                  <th>{t('dashboard.table.title')}</th>
                  <th>{t('dashboard.table.start_date')}</th>
                  <th className="cell-center">{t('dashboard.table.vehicle_count')}</th>
                  <th>{t('dashboard.table.status')}</th>
                  <th className="cell-actions">{t('dashboard.table.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {tenders.map(tender => (
                  <tr key={tender.id} className="table-row">
                    <td className="cell-primary">{tender.id}</td>
                    <td className="cell-title">{tender.title}</td>
                    <td>{tender.startDate}</td>
                    <td className="cell-center">{tender.vehicleCount}</td>
                    <td>
                      <Badge bg={getBadgeColor(tender.status)} className="status-badge">
                        {getStatusIcon(tender.status)} {t(`dashboard.status.${tender.status}`)}
                      </Badge>
                    </td>
                    <td className="cell-actions">
                      <button 
                        onClick={() => onViewTender(tender.id)} 
                        className="btn-table btn-table-outline-primary"
                        title={t('dashboard.actions.view')}
                      >
                        <FaEye /> <span className="action-text">{t('dashboard.actions.view')}</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="empty-state py-5">
            <div className="empty-icon mb-3">
              <FaExclamationTriangle size={36} />
            </div>
            <h6>{t('dashboard.no_tenders_title')}</h6>
            <p className="text-muted">{t('dashboard.no_tenders')}</p>
            <button onClick={onStartNewTender} className="btn-table btn-table-primary mt-2">
              <FaPlus className="me-2" /> {t('dashboard.start_first_tender')}
            </button>
          </div>
        )}
      </Card.Body>
    </Card>
  );

  // Açık İhaleler Tab İçeriği
  const renderOpenTendersTab = () => (
    <Card className="border-0 shadow-sm mb-4">
      <Card.Header className="d-flex justify-content-between align-items-center bg-white py-3">
        <h5 className="mb-0 section-title">{t('dashboard.open_tenders')}</h5>
      </Card.Header>
      <Card.Body className="p-0">
        {isLoading ? (
          <div className="loading-container py-5">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3">{t('common.loading')}</p>
          </div>
        ) : openTenders && openTenders.length > 0 ? (
          <div className="table-container">
            <table className="data-table">
              <thead>
                <tr>
                  <th>{t('dashboard.table.id')}</th>
                  <th>{t('dashboard.table.customer')}</th>
                  <th>{t('dashboard.table.title')}</th>
                  <th className="cell-center">{t('dashboard.table.vehicle_count')}</th>
                  <th>{t('dashboard.table.segment')}</th>
                  <th>{t('dashboard.table.duration')}</th>
                  <th>{t('dashboard.table.remaining')}</th>
                  <th className="cell-actions">{t('dashboard.table.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {openTenders.map(tender => (
                  <tr key={tender.id} className="table-row">
                    <td className="cell-primary">{tender.id}</td>
                    <td>{tender.customerName}</td>
                    <td className="cell-title">{tender.title}</td>
                    <td className="cell-center">{tender.vehicleCount}</td>
                    <td>{tender.segment}</td>
                    <td>{tender.duration}</td>
                    <td>{tender.remainingTime}</td>
                    <td className="cell-actions">
                      <button 
                        onClick={() => onViewTender(tender.id)} 
                        className="btn-table btn-table-outline-primary"
                        title={t('dashboard.actions.view')}
                      >
                        <FaEye /> <span className="action-text">{t('dashboard.actions.view')}</span>
                      </button>
                      <button 
                        onClick={() => onBidTender(tender.id)} 
                        className="btn-table btn-table-primary"
                        title={t('dashboard.actions.bid')}
                      >
                        <FaHandshake /> <span className="action-text">{t('dashboard.actions.bid')}</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="empty-state py-5">
            <div className="empty-icon mb-3">
              <FaExclamationTriangle size={36} />
            </div>
            <h6>{t('dashboard.no_open_tenders_title')}</h6>
            <p className="text-muted">{t('dashboard.no_open_tenders')}</p>
          </div>
        )}
      </Card.Body>
    </Card>
  );

  // Tekliflerim Tab İçeriği
  const renderMyBidsTab = () => (
    <Card className="border-0 shadow-sm mb-4">
      <Card.Header className="d-flex justify-content-between align-items-center bg-white py-3">
        <h5 className="mb-0 section-title">{t('dashboard.my_bids')}</h5>
      </Card.Header>
      <Card.Body className="p-0">
        {isLoading ? (
          <div className="loading-container py-5">
            <Spinner animation="border" variant="primary" />
            <p className="mt-3">{t('common.loading')}</p>
          </div>
        ) : (
          <Tabs defaultActiveKey="pending" className="inner-tabs">
            <Tab eventKey="pending" title={t('dashboard.bids.pending')}>
              {renderBidsTable(myBids.pending, "pending")}
            </Tab>
            <Tab eventKey="approved" title={t('dashboard.bids.approved')}>
              {renderBidsTable(myBids.approved, "approved")}
            </Tab>
            <Tab eventKey="rejected" title={t('dashboard.bids.rejected')}>
              {renderBidsTable(myBids.rejected, "rejected")}
            </Tab>
          </Tabs>
        )}
      </Card.Body>
    </Card>
  );

  // Teklif Tablosu Render Fonksiyonu
  const renderBidsTable = (bids, type) => {
    // Eğer hiç teklif yoksa boş durum mesajı göster
    if (!bids || bids.length === 0) {
      return (
        <div className="empty-state py-5">
          <div className="empty-icon mb-3">
            <FaExclamationTriangle size={36} />
          </div>
          <h6>{t(`dashboard.no_${type}_bids_title`)}</h6>
          <p className="text-muted">{t(`dashboard.no_${type}_bids`)}</p>
        </div>
      );
    }

    // Tabloda reddedilen teklifler için farklı başlıklar göster
    const isRejected = type === "rejected";

    return (
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>{t('dashboard.table.id')}</th>
              <th>{t('dashboard.table.tender_title')}</th>
              <th>{t('dashboard.table.customer')}</th>
              <th>{t('dashboard.table.offer_amount')}</th>
              <th>{t('dashboard.table.bid_date')}</th>
              {isRejected ? 
                <th>{t('dashboard.table.rejection_reason')}</th> : 
                <th className="cell-actions">{t('dashboard.table.actions')}</th>
              }
            </tr>
          </thead>
          <tbody>
            {bids.map(bid => (
              <tr key={bid.id} className="table-row">
                <td className="cell-primary">{bid.id}</td>
                <td className="cell-title">{bid.tenderTitle}</td>
                <td>{bid.customerName}</td>
                <td>{bid.offerAmount}</td>
                <td>{bid.bidDate}</td>
                {isRejected ? (
                  <td>{bid.rejectionReason}</td>
                ) : (
                  <td className="cell-actions">
                    <button 
                      onClick={() => onViewTender(bid.tenderId)} 
                      className="btn-table btn-table-outline-primary"
                      title={t('dashboard.actions.view')}
                    >
                      <FaEye /> <span className="action-text">{t('dashboard.actions.view')}</span>
                    </button>
                    {type === 'pending' && (
                      <button 
                        className="btn-table btn-table-outline-warning"
                        title={t('dashboard.actions.edit')}
                      >
                        <FaEye /> <span className="action-text">{t('dashboard.actions.edit')}</span>
                      </button>
                    )}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <section className="dashboard-tabs-section">
      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-4 dashboard-tabs"
      >
        <Tab 
          eventKey="myTenders" 
          title={<><FaFileAlt className="me-2" />{t('dashboard.tabs.my_tenders')}</>}
        >
          {renderMyTendersTab()}
        </Tab>
        <Tab 
          eventKey="openTenders" 
          title={<><FaHandshake className="me-2" />{t('dashboard.tabs.open_tenders')}</>}
        >
          {renderOpenTendersTab()}
        </Tab>
        <Tab 
          eventKey="myBids" 
          title={<><FaCarAlt className="me-2" />{t('dashboard.tabs.my_bids')}</>}
        >
          {renderMyBidsTab()}
        </Tab>
      </Tabs>
    </section>
  );
};

export default MyTendersSection; 