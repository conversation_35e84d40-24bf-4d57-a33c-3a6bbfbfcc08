*.swp
*.*~
project.lock.json
.DS_Store
*.pyc

# Visual Studio Code
.vscode

# User-specific files
*.suo
*.user
*.userosscache
*.sln.docstates

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
build/
bld/
[Bb]in/
[Oo]bj/
msbuild.log
msbuild.err
msbuild.wrn

# Visual Studio 2015
.vs/

# Node.js dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production build folders
/build
/WebUI/build
/WebUI/node_modules

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.idea/workspace.xml
.idea/vcs.xml
.idea/workspace.xml
.idea/inspectionProfiles/Project_Default.xml
.idea/workspace.xml

