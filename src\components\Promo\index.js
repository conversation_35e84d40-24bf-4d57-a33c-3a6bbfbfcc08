import React from "react";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";
import { FaMoneyBillWave } from "react-icons/fa";

import img1 from "../../img/new-images/Promo/kirmizi-araba-ihaleden-kirala.png";

import "./style.css";

const Promo = () => {
  const { t } = useTranslation();

  return (
    <section className="gauto-promo-area">
      <Container>
        <Row className="align-items-center">
          <Col md="6">
            <div className="promo-box-left">
              <img src={img1} alt="kurumsal araç kiralama" className="img-fluid" />
            </div>
          </Col>
          <Col md="6">
            <div className="promo-box-right">
              <h3>{t("promo.title")}</h3>
              <p>{t("promo.subtitle")}</p>
              
              <Link to="/ihale-sureci-detay" className="gauto-btn">
                {t("promo.cta_button")}
              </Link>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default Promo;
