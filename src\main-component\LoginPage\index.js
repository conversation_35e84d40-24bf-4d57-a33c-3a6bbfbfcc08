import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import PageTitle from "../../components/PageTitle";
import Login from "../../components/Login";

const LoginPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.login")}
        pagesub={t("header-navigation.login")}
      />
      <Login />
    </Fragment>
  );
};
export default LoginPage;
