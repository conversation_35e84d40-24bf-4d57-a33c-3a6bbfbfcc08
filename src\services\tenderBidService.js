// tenderBidService.js - İhale teklif verme servis fonksiyonları

import axios from "axios";
import { 
  mockTenderDetails, 
  mockCompanyData, 
  mockBidResponses 
} from "./mockData";

/**
 * İhale detaylarını getiren fonksiyon
 * @param {number} tenderId - İhale ID'si
 * @returns {Promise} - İhale detay verisi
 */
export function getTenderDetails(tenderId) {
  return new Promise((resolve, reject) => {
    try {
      // API çağrısı simülasyonu
      setTimeout(() => {
        if (!tenderId) {
          reject(new Error("Geçersiz ihale ID'si"));
          return;
        }
        
        // mockData'dan ihale detaylarını al
        const tenderData = { ...mockTenderDetails };
        
        // Servis yanıtını döndür
        const response = {
          success: true,
          message: "İhale detayları başarıyla alındı",
          data: tenderData
        };
        
        resolve(response);
      }, 1500);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Şirket bilgilerini getiren fonksiyon
 * @returns {Promise} - Şirket verileri
 */
export function getCompanyData() {
  return new Promise((resolve, reject) => {
    try {
      // API çağrısı simülasyonu
      setTimeout(() => {
        // Şirket verilerini al
        const companyData = { ...mockCompanyData };
        
        // Servis yanıtını döndür
        const response = {
          success: true,
          message: "Şirket bilgileri başarıyla alındı",
          data: companyData
        };
        
        resolve(response);
      }, 1200);
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * İhale teklifi gönderen fonksiyon
 * @param {number} tenderId - İhale ID'si
 * @param {object} bidData - Teklif verileri
 * @returns {Promise} - Yanıt
 */
export function submitBid(tenderId, bidData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // İhale ID ve teklif verisi doğrulaması
      if (!tenderId || !bidData) {
        resolve({
          success: false,
          message: "Geçersiz ihale ID veya teklif verisi"
        });
        return;
      }
      
      // Teklifin yaratılma zamanını ekle
      const now = new Date().toISOString();
      const submittedBid = {
        id: Math.floor(Math.random() * 1000) + 100, // Rastgele benzersiz ID
        tenderId: parseInt(tenderId),
        tenderTitle: mockTenderDetails.title,
        tenderCompany: mockTenderDetails.companyName,
        status: "SUBMITTED",
        statusText: "Verilen Teklif",
        submittedAt: now,
        responseDeadline: new Date(new Date(now).getTime() + 12 * 60 * 60 * 1000).toISOString(), // 12 saat sonra
        createdAt: now,
        ...bidData
      };
      
      resolve({
        success: true,
        message: "Teklifiniz başarıyla gönderildi. Müşterinin yanıtı 12 saat içinde bekleniyor.",
        data: submittedBid
      });
    }, 2000);
  });
}

/**
 * Taslak teklifi kaydeden fonksiyon
 * @param {number} tenderId - İhale ID'si
 * @param {object} bidData - Teklif verileri
 * @returns {Promise} - Yanıt
 */
export function saveDraftBid(tenderId, bidData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // İhale ID ve teklif verisi doğrulaması
      if (!tenderId || !bidData) {
        resolve({
          success: false,
          message: "Geçersiz ihale ID veya teklif verisi"
        });
        return;
      }
      
      const now = new Date().toISOString();
      const draftBid = {
        id: Math.floor(Math.random() * 1000) + 100, // Rastgele benzersiz ID
        tenderId: parseInt(tenderId),
        tenderTitle: mockTenderDetails.title,
        tenderCompany: mockTenderDetails.companyName,
        status: "DRAFT",
        statusText: "Taslak Teklif",
        createdAt: now,
        ...bidData
      };
      
      resolve({
        success: true,
        message: "Taslak teklifiniz başarıyla kaydedildi",
        data: draftBid
      });
    }, 1500);
  });
}

/**
 * Verilen ihalenin teklif yanıtlarını getiren fonksiyon
 * @param {number} tenderId - İhale ID'si (opsiyonel, belirli bir ihaleye ait teklifleri getirmek için)
 * @returns {Promise} - Teklif listesi
 */
export function getBidResponses(tenderId) {
  if (tenderId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let responses = [...mockBidResponses];
        
        // Eğer tenderId ile filtreleme isteniyorsa
        responses = responses.filter(bid => bid.tenderId === parseInt(tenderId));
        
        resolve({
          success: true,
          message: `${responses.length} adet teklif yanıtı başarıyla alındı`,
          data: responses
        });
      }, 1000);
    });
  } else {
    return new Promise((resolve) => {
      setTimeout(() => {
        let responses = [...mockBidResponses];
        
        resolve({
          success: true,
          message: `${responses.length} adet teklif yanıtı başarıyla alındı`,
          data: responses
        });
      }, 1000);
    });
  }
}

/**
 * Belirli bir teklifi ID'ye göre getiren fonksiyon
 * @param {number} bidId - Teklif ID'si
 * @returns {Promise} - Teklif detayları
 */
export function getBidById(bidId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const bid = mockBidResponses.find(b => b.id === parseInt(bidId));
      
      if (!bid) {
        resolve({
          success: false,
          message: `${bidId} ID'li teklif bulunamadı`,
          data: null
        });
        return;
      }
      
      resolve({
        success: true,
        message: "Teklif bilgileri başarıyla alındı",
        data: bid
      });
    }, 1200);
  });
}

/**
 * Teklif durumunu kontrol eden fonksiyon
 * @param {number} bidId - Teklif ID'si
 * @returns {Promise} - Teklif durum bilgileri
 */
export function checkBidStatus(bidId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const bid = mockBidResponses.find(b => b.id === parseInt(bidId));
      
      if (!bid) {
        resolve({
          success: false,
          message: `${bidId} ID'li teklif bulunamadı`,
          data: null
        });
        return;
      }
      
      const statusInfo = {
        bidId: bid.id,
        tenderId: bid.tenderId,
        tenderTitle: bid.tenderTitle,
        status: bid.status,
        statusText: bid.statusText,
        submittedAt: bid.submittedAt || null,
        responseDeadline: bid.responseDeadline || null,
        pendingSince: bid.pendingSince || null,
        rejectedAt: bid.rejectedAt || null,
        approvedAt: bid.approvedAt || null,
        response: bid.response || null,
        contractStatus: bid.contractStatus || null,
        contractDate: bid.contractDate || null
      };
      
      resolve({
        success: true,
        message: `Teklif durumu: ${statusInfo.statusText}`,
        data: statusInfo
      });
    }, 1000);
  });
}

/**
 * Onaylanan ihale için sözleşme bilgilerini getiren fonksiyon
 * @param {number} bidId - Teklif ID'si
 * @returns {Promise} - Sözleşme detayları
 */
export function getContractDetails(bidId) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const bid = mockBidResponses.find(b => b.id === parseInt(bidId));
      
      if (!bid) {
        resolve({
          success: false,
          message: `${bidId} ID'li teklif bulunamadı`,
          data: null
        });
        return;
      }
      
      if (bid.status !== "APPROVED") {
        resolve({
          success: false,
          message: "Bu teklif henüz onaylanmadığı için sözleşme bilgileri bulunmuyor",
          data: null
        });
        return;
      }
      
      const contractDetails = {
        bidId: bid.id,
        tenderId: bid.tenderId,
        tenderTitle: bid.tenderTitle,
        tenderCompany: bid.tenderCompany,
        contractStatus: bid.contractStatus || "Hazırlanıyor",
        contractDate: bid.contractDate || null,
        contractDocuments: bid.contractDocuments || [],
        vehicleCount: bid.vehicleCount,
        vehicleDetails: bid.vehicleDetails,
        bidAmount: bid.bidAmount,
        bidPerMonth: bid.bidPerMonth,
        notes: bid.notes || ""
      };
      
      resolve({
        success: true,
        message: "Sözleşme detayları başarıyla alındı",
        data: contractDetails
      });
    }, 1500);
  });
}

/**
 * İmzalanan sözleşmeyi yükleyen fonksiyon
 * @param {number} bidId - Teklif ID'si
 * @param {object} documentData - Belge verileri
 * @returns {Promise} - Yüklenen belge bilgileri
 */
export function uploadContractDocument(bidId, documentData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Teklif ID ve belge verisi doğrulaması
      if (!bidId || !documentData || !documentData.file) {
        resolve({
          success: false,
          message: "Geçersiz teklif ID veya belge verisi",
          data: null
        });
        return;
      }
      
      const now = new Date().toISOString();
      const document = {
        id: Math.floor(Math.random() * 1000) + 1,
        name: documentData.file.name,
        url: URL.createObjectURL(documentData.file),
        uploadDate: now.split('T')[0],
        size: `${Math.floor(documentData.file.size / 1024)} KB`,
        type: documentData.file.type,
        status: "UPLOADED"
      };
      
      resolve({
        success: true,
        message: "Sözleşme belgesi başarıyla yüklendi",
        data: document
      });
    }, 2500);
  });
}

/**
 * Teklif ücretini ödeme fonksiyonu
 * @param {object} bidData - Teklif verileri
 * @returns {Promise} - Ödeme bilgileri
 */
export function payBidFee(bidData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Teklif verileri doğrulaması
      if (!bidData || !bidData.vehicleCount) {
        resolve({
          success: false,
          message: "Geçersiz teklif verisi, araç sayısı belirtilmemiş",
          data: null
        });
        return;
      }
      
      // Araç sayısına göre ücretlendirme hesaplama
      const vehicleCount = bidData.vehicleCount;
      let fee = 0;
      
      if (vehicleCount <= 10) {
        fee = 500;
      } else if (vehicleCount <= 50) {
        fee = 1000;
      } else if (vehicleCount <= 100) {
        fee = 2000;
      } else {
        fee = 5000;
      }
      
      // Segmente göre ek ücretlendirme
      let segmentFee = 0;
      if (bidData.vehicleDetails && Array.isArray(bidData.vehicleDetails)) {
        bidData.vehicleDetails.forEach(vehicle => {
          if (vehicle.segment === "D" || vehicle.segment === "E") {
            segmentFee += 100 * vehicle.count; // D ve E segment araçlar için ek ücret
          } else if (vehicle.segment === "SUV" || vehicle.segment === "Van") {
            segmentFee += 150 * vehicle.count; // SUV ve Van için ek ücret
          }
        });
      }
      
      const totalFee = fee + segmentFee;
      
      const paymentInfo = {
        bidId: bidData.id || "new",
        tenderId: bidData.tenderId,
        baseFee: fee,
        segmentFee: segmentFee,
        totalFee: totalFee,
        currency: "TRY",
        paymentDate: new Date().toISOString(),
        transactionId: `TRX-${Math.floor(Math.random() * 1000000)}`,
        status: "COMPLETED",
        validUntil: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString(), // 3 ay geçerli
        vehicleCount: vehicleCount
      };
      
      resolve({
        success: true,
        message: `${totalFee} TL tutarındaki teklif ücreti başarıyla ödendi (Geçerlilik: 3 ay)`,
        data: paymentInfo
      });
    }, 2000);
  });
} 