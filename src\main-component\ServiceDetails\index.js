import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import ServiceDetailsPage from "../../components/ServiceDetails";

const ServiceSingle = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.service_details")}
        pagesub={t("header-navigation.service_details")}
      />
      <ServiceDetailsPage />
    </Fragment>
  );
};
export default ServiceSingle;
