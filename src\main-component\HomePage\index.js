import React, { Fragment, useEffect } from "react";
import HeroVisitor from "../../components/HeroVisitor";
import About from "../../components/About";
import Promo from "../../components/Promo";
import LongTermRentalAdvantages from "../../components/LongTermRentalAdvantages";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { PAGE_SEO_META } from "../../components/common/SEOMeta/defaultMetaTags";

const HomePage = () => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const navigate = useNavigate();
  
  // Kullanıcı giriş yapmışsa doğrudan dashboard'a yönlendir
  useEffect(() => {
    if (isAuthenticated && user) {
      const targetPath = '/dashboard';
      navigate(targetPath, { replace: true });
    }
  }, [isAuthenticated, user, navigate]);

  // Ziyaretçi ekranı - giriş yapmamış kullanıcılar için
  return (
    <Fragment>
      <HeroVisitor />
      <About />
      <Promo />
      <LongTermRentalAdvantages />
    </Fragment>
  );
};

export default HomePage;
