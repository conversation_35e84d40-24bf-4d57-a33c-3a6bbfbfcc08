import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { authService } from '../../services';
import { getHomepagePathByRole, USER_ROLES, getRoleIdFromApiRoles } from '../../constants/userRoles';
import { parseJwt, extractUserFromToken } from '../../utils/jwtHelper';

// Async thunk action creators
export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak giriş işlemi
      const response = await authService.login(credentials);

      // API yanıt formatını kontrol et
      if (!response.data || !response.data.result) {
        throw new Error('Geçersiz API yanıtı');
      }

      // API yanıtından token'ı al
      let { token } = response.data.result;

      if (!token) {
        throw new Error('Token bulunamadı');
      }

      // "Bearer " önekini kaldır (birden fazla olabilir)
      while (token.startsWith('Bearer ')) {
        token = token.slice(7);
      }

      // Token'dan kullanıcı bilgilerini çıkar
      const userData = extractUserFromToken(token);

      if (!userData) {
        throw new Error('Token çözümlenemedi');
      }

      // Kullanıcı rolünü belirle
      const roleId = getRoleIdFromApiRoles(userData.roles);

      // Kullanıcı nesnesini oluştur
      const user = {
        name: userData.name,
        email: userData.email,
        companyId: userData.companyId,
        roleId: roleId,
        isVerified: true, // Token alınabildiğine göre hesap doğrulanmış kabul edilir
      };

      // JWT token'ı rememberMe'ye göre kaydet
      if (credentials.rememberMe) {
        localStorage.setItem('token', token);
        localStorage.setItem('user', JSON.stringify(user));
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('user');
      } else {
        sessionStorage.setItem('token', token);
        sessionStorage.setItem('user', JSON.stringify(user));
        localStorage.removeItem('token');
        localStorage.removeItem('user');
      }

      return { user, token };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message ||
        error.response?.data?.errorCode ||
        error.message ||
        'Giriş başarısız'
      );
    }
  }
);

export const registerUser = createAsyncThunk(
  'auth/register',
  async (userData, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak kayıt işlemi
      const response = await authService.register(userData);

      // API yanıt formatını kontrol et
      if (!response.data || !response.data.isSuccess) {
        throw new Error(response.data?.message || 'Kayıt işlemi başarısız oldu');
      }

      // Başarılı kayıt sonrası login sayfasına yönlendirme için
      return { success: true, message: response.data.message || 'Kayıt başarılı. Lütfen giriş yapın.' };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message ||
        error.message ||
        'Kayıt işlemi başarısız oldu'
      );
    }
  }
);

export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak çıkış işlemi
      await authService.logout();

      // Localstorage'dan kullanıcı bilgilerini temizle
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      return true;
    } catch (error) {
      // API çağrısı başarısız olsa bile localStorage'ı temizle
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Başarısız olsa bile çıkış işlemini tamamla
      return true;
    }
  }
);

export const getUserProfile = createAsyncThunk(
  'auth/profile',
  async (_, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak profil bilgilerini getir
      const response = await authService.getProfile();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Profil bilgileri getirilemedi');
    }
  }
);

export const verifyUserAccount = createAsyncThunk(
  'auth/verifyAccount',
  async (verificationData, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak doğrulama işlemi
      const response = await authService.verifyAccount(verificationData);

      // Güncellenmiş kullanıcı bilgilerini localStorage'a kaydet
      localStorage.setItem('user', JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Doğrulama işlemi başarısız oldu');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'auth/updateProfile',
  async (profileData, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak profil bilgilerini güncelle
      const response = await authService.updateProfile(profileData);

      // Güncellenmiş kullanıcı bilgilerini localStorage'a kaydet
      localStorage.setItem('user', JSON.stringify(response.data));

      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Profil güncellenemedi');
    }
  }
);

export const updateUserPassword = createAsyncThunk(
  'auth/updatePassword',
  async (passwordData, { rejectWithValue }) => {
    try {
      // Auth servisini kullanarak şifre güncelle
      const response = await authService.updatePassword(passwordData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Şifre güncellenemedi');
    }
  }
);

// Sayfa yüklendiğinde localStorage ve sessionStorage'dan kullanıcı bilgilerini al
const loadUserFromStorage = () => {
  try {
    // Öncelik: localStorage (beni hatırla seçiliyse)
    let token = localStorage.getItem('token') || sessionStorage.getItem('token');
    const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');

    // Token varsa "Bearer " önekini kaldır (birden fazla olabilir)
    if (token) {
      while (token.startsWith('Bearer ')) {
        token = token.slice(7);
      }

      // Token'ın süresi dolmuş mu kontrol et
      const tokenData = extractUserFromToken(token);
      if (tokenData && tokenData.exp) {
        const now = Date.now() / 1000; // saniye cinsinden
        if (tokenData.exp < now) {
          // Token süresi dolmuş, logout işlemi
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          sessionStorage.removeItem('token');
          sessionStorage.removeItem('user');
          return { user: null, token: null, isAuthenticated: false };
        }
      }

      // Temizlenmiş token'ı tekrar kaydet
      if (localStorage.getItem('token')) {
        localStorage.setItem('token', token);
      } else {
        sessionStorage.setItem('token', token);
      }
    }

    if (token && userStr) {
      let user = JSON.parse(userStr);

      // Token'dan kullanıcı bilgilerini çıkarmayı dene
      try {
        const tokenData = extractUserFromToken(token);

        // Token geçerliyse ve kullanıcı bilgileri çıkarılabiliyorsa
        if (tokenData) {
          // Kullanıcı rolünü belirle
          const roleId = getRoleIdFromApiRoles(tokenData.roles);

          // Kullanıcı nesnesini güncelle
          user = {
            ...user,
            name: tokenData.name || user.name,
            email: tokenData.email || user.email,
            companyId: tokenData.companyId || user.companyId,
            roleId: roleId,
            isVerified: true
          };

          // Güncellenmiş kullanıcı bilgilerini tekrar kaydet
          if (localStorage.getItem('user')) {
            localStorage.setItem('user', JSON.stringify(user));
          } else {
            sessionStorage.setItem('user', JSON.stringify(user));
          }
        } else {
        }
      } catch (tokenError) {
      }

      // Rol kontrolü ve düzeltme
      if (user && user.roleId !== USER_ROLES.CUSTOMER.id &&
          user.roleId !== USER_ROLES.ADMIN.id &&
          user.roleId !== USER_ROLES.CUSTOMER_SERVICE.id) {
        user.roleId = USER_ROLES.CUSTOMER.id; // Bilinmeyen roller CUSTOMER olarak ayarlanır
      }

      return { user, token, isAuthenticated: true };
    }

    return { user: null, token: null, isAuthenticated: false };
  } catch (error) {
    // Hata durumunda tüm verileri temizle
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('user');
    return { user: null, token: null, isAuthenticated: false };
  }
};

// Debug için kullanıcı bilgilerini kontrol et ve konsola yazdır
try {
  const storedUser = localStorage.getItem('user');
  const storedToken = localStorage.getItem('token');
} catch (e) {
  console.error('Error checking initial state:', e);
}

const initialState = {
  ...loadUserFromStorage(),
  loading: false,
  error: null,
  successMessage: null,
  homepagePath: '/', // Kullanıcı rolüne göre ana sayfa yolu
  verificationStatus: null, // Doğrulama durumu
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    setHomepagePath: (state) => {
      if (state.user && state.user.roleId) {
        state.homepagePath = getHomepagePathByRole(state.user.roleId);
      } else {
        state.homepagePath = '/';
      }
    },
    clearVerificationStatus: (state) => {
      state.verificationStatus = null;
    },
    // Manuel kimlik doğrulama durumu sıfırlama
    resetAuth: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.loading = false;
      state.error = null;
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    },
    // Test için kullanılabilecek manuel giriş
    manualLogin: (state, action) => {
      const { user, token: rawToken } = action.payload;

      // Token'dan Bearer önekini kaldır
      let token = rawToken;
      while (token && token.startsWith('Bearer ')) {
        token = token.slice(7);
      }

      state.user = user;
      state.token = token;
      state.isAuthenticated = true;
      state.loading = false;
      state.error = null;

      // Temizlenmiş token'ı localStorage'a kaydet
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
    }
  },
  extraReducers: (builder) => {
    builder
      // Login işlemi
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
        state.homepagePath = getHomepagePathByRole(action.payload.user.roleId);
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Giriş başarısız oldu';
        state.isAuthenticated = false;
      })

      // Register işlemi
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.successMessage = action.payload.message;
        // Kayıt başarılı olsa bile kullanıcı giriş yapmış sayılmaz
        state.isAuthenticated = false;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Kayıt işlemi başarısız oldu';
        state.isAuthenticated = false;
      })

      // Logout işlemi
      .addCase(logoutUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.loading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.homepagePath = '/';
      })
      .addCase(logoutUser.rejected, (state) => {
        state.loading = false;
        // Hata olsa bile çıkış yap
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.homepagePath = '/';
      })

      // Profil getirme işlemi
      .addCase(getUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.homepagePath = getHomepagePathByRole(action.payload.roleId);
      })
      .addCase(getUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Profil bilgileri getirilemedi';
      })

      // Kullanıcı doğrulama işlemi
      .addCase(verifyUserAccount.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
        state.verificationStatus = 'pending';
      })
      .addCase(verifyUserAccount.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.successMessage = 'Hesap doğrulama işlemi başarıyla tamamlandı';
        state.verificationStatus = 'success';
      })
      .addCase(verifyUserAccount.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Hesap doğrulama işlemi başarısız oldu';
        state.verificationStatus = 'failed';
      })

      // Profil güncelleme işlemi
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload;
        state.successMessage = 'Profil başarıyla güncellendi';
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Profil güncellenemedi';
      })

      // Şifre güncelleme işlemi
      .addCase(updateUserPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
      })
      .addCase(updateUserPassword.fulfilled, (state) => {
        state.loading = false;
        state.successMessage = 'Şifre başarıyla güncellendi';
      })
      .addCase(updateUserPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Şifre güncellenemedi';
      });
  }
});

export const {
  clearError,
  clearSuccessMessage,
  setHomepagePath,
  clearVerificationStatus,
  resetAuth,
  manualLogin
} = authSlice.actions;

export default authSlice.reducer;