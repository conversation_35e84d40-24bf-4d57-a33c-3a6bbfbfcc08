import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import CarBooking from "../../components/CarBooking";

const CarBookingPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.car_booking")}
        pagesub={t("header-navigation.car_booking")}
      />
      <CarBooking />
    </Fragment>
  );
};
export default CarBookingPage;
