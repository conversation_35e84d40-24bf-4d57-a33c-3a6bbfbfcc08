import React from 'react';
import { Nav, Badge } from 'react-bootstrap';
import { FaFileAlt, FaFileContract, FaMoneyBillWave } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

/**
 * İhale sekme navigasyonunu gösteren bileşen
 * @param {object} props - Bileşen propsları
 * @param {string} props.activeKey - Aktif sekme
 * @param {function} props.onSelect - Sekme de<PERSON> o<PERSON>
 * @param {object} props.tender - İhale verileri 
 */
const TenderTabs = ({ activeKey, onSelect, tender }) => {
  const { t } = useTranslation();
  
  return (
    <div className="tender-tabs">
      <Nav variant="tabs" className="tender-nav">
        <Nav.Item>
          <Nav.Link eventKey="details" className="d-flex align-items-center">
            <FaFileAlt className="me-2" />
            {t('tender.tabs.details', 'Detaylar')}
          </Nav.Link>
        </Nav.Item>
        <Nav.Item>
          <Nav.Link eventKey="vehicles" className="d-flex align-items-center">
            <i className="bi bi-truck me-2"></i>
            {t('tender.tabs.vehicles', 'Araçlar')}
            <Badge bg="secondary" pill className="ms-2">{tender?.requiredVehicles?.length || 0}</Badge>
          </Nav.Link>
        </Nav.Item>
        <Nav.Item>
          <Nav.Link eventKey="documents" className="d-flex align-items-center">
            <FaFileContract className="me-2" />
            {t('tender.tabs.documents', 'Dokümanlar')}
            <Badge bg="secondary" pill className="ms-2">{tender?.documents?.length || 0}</Badge>
          </Nav.Link>
        </Nav.Item>
        <Nav.Item>
          <Nav.Link eventKey="createBid" className="d-flex align-items-center">
            <FaMoneyBillWave className="me-2" />
            {t('tender.tabs.create_bid', 'Teklif Oluştur')}
          </Nav.Link>
        </Nav.Item>
      </Nav>
    </div>
  );
};

export default TenderTabs; 