import React from 'react';
import { <PERSON>, Button } from 'react-bootstrap';
import { FaClock, FaInfoCircle, FaListAlt } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import TenderBidsTable from './TenderBidsTable';
import CustomerInfo from './CustomerInfo';
import { formatDate } from '../utils/formatters';

/**
 * İhale detay içeriğini gösteren bileşen
 * @param {object} props - Bileşen propsları
 * @param {object} props.tender - İhale verileri
 * @param {object} props.company - Şirket verileri
 * @param {function} props.onTabChange - Sekme değişim fonksiyonu
 */
const TenderDetailContent = ({ tender, company, onTabChange }) => {
  const { t } = useTranslation();
  
  if (!tender) return null;
  
  return (
    <div className="compact-detail-content">
      {/* Müşteri Bilgisi ve Zaman Çizelgesi - K<PERSON><PERSON><PERSON><PERSON> */}
      <div className="detail-compact-row">
        <div className="detail-compact-col">
          <CustomerInfo customer={tender?.customer} />
        </div>
        
        <div className="detail-compact-col">
          <Card className="mini-card">
            <Card.Header className="mini-card-header py-1 px-2">
              <h6 className="mb-0 mini-header-title d-flex align-items-center">
                <FaClock className="me-1 small-icon" />
                {t('tender.timeframe', 'Zaman Çizelgesi')}
              </h6>
            </Card.Header>
            <Card.Body className="mini-card-body p-2">
              <div className="d-flex align-items-center mb-1">
                <div className="small-icon text-primary me-1">•</div>
                <div>
                  <div className="mini-label">{t('tender.start_date', 'Başlangıç')}: <span className="mini-value">{formatDate(tender?.startDate)}</span></div>
                </div>
              </div>
              <div className="d-flex align-items-center">
                <div className="small-icon text-danger me-1">•</div>
                <div>
                  <div className="mini-label">{t('tender.end_date', 'Bitiş')}: <span className="mini-value">{formatDate(tender?.endDate)}</span></div>
                </div>
              </div>
            </Card.Body>
          </Card>
          
          <Button 
            variant="primary" 
            size="sm" 
            className="w-100 mt-2 compact-btn"
            onClick={() => onTabChange && onTabChange('createBid')}
          >
            {t('tender.submit_bid', 'Teklif Ver')}
          </Button>
        </div>
      </div>
      
      {/* Detaylı Açıklama */}
      <Card className="compact-card">
        <Card.Header className="compact-card-header py-1 px-2">
          <h6 className="mb-0 d-flex align-items-center">
            <FaInfoCircle className="me-1 small-icon" />
            {t('tender.detailed_description', 'Detaylı Açıklama')}
          </h6>
        </Card.Header>
        <Card.Body className="compact-card-body p-2">
          <p className="compact-description mb-2">{tender?.longDescription}</p>
          
          <div className="compact-info-grid">
            {tender?.additionalInfo?.deliveryLocation && (
              <div className="compact-info-item">
                <div className="compact-info-label">{t('tender.delivery_location', 'Teslimat Yeri')}</div>
                <div className="compact-info-value">{tender.additionalInfo.deliveryLocation}</div>
              </div>
            )}
            {tender?.additionalInfo?.paymentTerms && (
              <div className="compact-info-item">
                <div className="compact-info-label">{t('tender.payment_terms', 'Ödeme Koşulları')}</div>
                <div className="compact-info-value">{tender.additionalInfo.paymentTerms}</div>
              </div>
            )}
            {tender?.additionalInfo?.insuranceRequirements && (
              <div className="compact-info-item">
                <div className="compact-info-label">{t('tender.insurance_requirements', 'Sigorta Gereksinimleri')}</div>
                <div className="compact-info-value">{tender.additionalInfo.insuranceRequirements}</div>
              </div>
            )}
            {tender?.additionalInfo?.maintenanceTerms && (
              <div className="compact-info-item">
                <div className="compact-info-label">{t('tender.maintenance_terms', 'Bakım Koşulları')}</div>
                <div className="compact-info-value">{tender.additionalInfo.maintenanceTerms}</div>
              </div>
            )}
          </div>
        </Card.Body>
      </Card>
      
      {/* Mevcut Teklifler */}
      {tender?.bids?.length > 0 && (
        <Card className="compact-card">
          <Card.Header className="compact-card-header py-1 px-2">
            <h6 className="mb-0 d-flex align-items-center">
              <FaListAlt className="me-1 small-icon" />
              {t('tender.current_bids', 'Mevcut Teklifler')} ({tender.bids.length})
            </h6>
          </Card.Header>
          <Card.Body className="compact-card-body p-1">
            <TenderBidsTable bids={tender.bids} />
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

export default TenderDetailContent; 