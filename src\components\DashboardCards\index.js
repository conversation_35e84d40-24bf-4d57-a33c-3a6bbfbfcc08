import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { FaCarAlt, FaChartLine, FaBullhorn, FaHandshake, FaArrowUp, FaArrowDown } from 'react-icons/fa';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  AreaChart, 
  Area,
  BarChart,
  Bar,
  Legend,
  ReferenceLine
} from 'recharts';
import './style.css';

// CSS değişken renk değerlerini alma fonksiyonu
const getCSSVariable = (varName) => {
  // Default renk değeri
  let defaultColor = '#FF6200';
  
  // Eğer document tanımlıysa, CSS değişkenini al
  if (typeof document !== 'undefined') {
    const value = getComputedStyle(document.documentElement)
      .getPropertyValue(varName)
      .trim();
    return value || defaultColor;
  }
  
  return defaultColor;
};

const DashboardCards = () => {
  // CSS değişkenlerini state olarak tut
  const [primaryColor, setPrimaryColor] = useState('#FF6200');
  const [primaryLight, setPrimaryLight] = useState('#ff8133');

  useEffect(() => {
    // CSS değişkenlerini yükle
    setPrimaryColor(getCSSVariable('--primary-color'));
    setPrimaryLight(getCSSVariable('--primary-light'));
  }, []);

  // Dummy dashboard data
  const [dashboardData, setDashboardData] = useState({
    activeAuctions: 38,
    ongoingAuctions: 15,
    activeFleet: 127,
    lastMonthData: {
      auctions: 32,
      ongoing: 18,
      fleet: 119
    },
    monthlyData: [
      { name: 'Oca', activeCount: 10, completedCount: 15, value: 25 },
      { name: 'Şub', activeCount: 15, completedCount: 10, value: 30 },
      { name: 'Mar', activeCount: 20, completedCount: 18, value: 38 },
      { name: 'Nis', activeCount: 18, completedCount: 22, value: 40 },
      { name: 'May', activeCount: 22, completedCount: 15, value: 37 },
      { name: 'Haz', activeCount: 35, completedCount: 10, value: 45 }
    ],
    fleetUsage: [
      { name: 'Oca', sedan: 20, suv: 15, hatchback: 12 },
      { name: 'Şub', sedan: 25, suv: 18, hatchback: 15 },
      { name: 'Mar', sedan: 30, suv: 20, hatchback: 18 },
      { name: 'Nis', sedan: 35, suv: 22, hatchback: 20 },
      { name: 'May', sedan: 40, suv: 25, hatchback: 22 },
      { name: 'Haz', sedan: 45, suv: 30, hatchback: 25 }
    ]
  });

  const compareWithLastMonth = (current, type) => {
    let previousMonth = 0;
    let currentMonth = 0;
    
    if (type === 'auctions') {
      previousMonth = dashboardData.monthlyData[5].value;
      currentMonth = dashboardData.monthlyData[6].value;
    } else if (type === 'ongoing') {
      previousMonth = dashboardData.monthlyData[5].activeCount;
      currentMonth = dashboardData.monthlyData[6].activeCount;
    } else if (type === 'fleet') {
      previousMonth = dashboardData.fleetUsage[5].sedan + dashboardData.fleetUsage[5].suv + dashboardData.fleetUsage[5].hatchback;
      currentMonth = dashboardData.fleetUsage[6].sedan + dashboardData.fleetUsage[6].suv + dashboardData.fleetUsage[6].hatchback;
    }
    
    const percentChange = ((currentMonth - previousMonth) / previousMonth) * 100;
    const isIncrease = percentChange > 0;
    
    return (
      <div className="trend-indicator">
        {isIncrease ? (
          <><FaArrowUp className="trend-up" /> <span className="trend-value trend-up">{percentChange.toFixed(1)}%</span></>
        ) : (
          <><FaArrowDown className="trend-down" /> <span className="trend-value trend-down">{Math.abs(percentChange).toFixed(1)}%</span></>
        )}
      </div>
    );
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="tooltip-label">{`${label}`}</p>
          {payload.map((entry, index) => (
            <p key={`item-${index}`} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="dashboard-cards-section section_70">
      <Container>
        <div className="site-heading dashboard-heading">
          <h4>Genel Bakış</h4>
          <h2>İhale ve Filo Durumu</h2>
        </div>
        
        <Row>
          <Col sm={6} md={3} className="mb-4">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaBullhorn size={30} />
                </div>
                <h2 className="count">{dashboardData.activeAuctions}</h2>
                <p className="label mb-0">Aktif İhaleler</p>
                {compareWithLastMonth(dashboardData.activeAuctions, 'auctions')}
              </Card.Body>
            </Card>
          </Col>

          <Col sm={6} md={3} className="mb-4">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaHandshake size={30} />
                </div>
                <h2 className="count">{dashboardData.ongoingAuctions}</h2>
                <p className="label mb-0">Devam Eden İhaleler</p>
                {compareWithLastMonth(dashboardData.ongoingAuctions, 'ongoing')}
              </Card.Body>
            </Card>
          </Col>

          <Col sm={6} md={3} className="mb-4">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaCarAlt size={30} />
                </div>
                <h2 className="count">{dashboardData.activeFleet}</h2>
                <p className="label mb-0">Aktif Filo</p>
                {compareWithLastMonth(dashboardData.activeFleet, 'fleet')}
              </Card.Body>
            </Card>
          </Col>

          <Col sm={6} md={3} className="mb-4">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center justify-content-between">
                <div className="icon-container mb-3">
                  <FaChartLine size={30} />
                </div>
                <div className="mini-chart">
                  <ResponsiveContainer width="100%" height={80}>
                    <AreaChart data={dashboardData.monthlyData}>
                      <defs>
                        <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={primaryColor} stopOpacity={0.8}/>
                          <stop offset="95%" stopColor={primaryColor} stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <Area 
                        type="monotone" 
                        dataKey="value" 
                        stroke={primaryColor} 
                        fillOpacity={1} 
                        fill="url(#colorValue)"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
                <p className="label mb-0">İhale Trendi</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg={8} className="mb-4">
            <Card className="chart-card">
              <Card.Header>
                <h5 className="mb-0">İhale İstatistikleri</h5>
              </Card.Header>
              <Card.Body>
                <div className="chart-tabs">
                  <span className="chart-tab active">Aylık İhaleler</span>
                  <span className="chart-tab">Çeyreklik</span>
                  <span className="chart-tab">Yıllık</span>
                </div>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={dashboardData.monthlyData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                    <XAxis dataKey="name" axisLine={false} tickLine={false} />
                    <YAxis axisLine={false} tickLine={false} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <ReferenceLine y={0} stroke="#000" />
                    <Bar name="Aktif İhaleler" dataKey="activeCount" stackId="a" fill={primaryColor} radius={[4, 4, 0, 0]} />
                    <Bar name="Tamamlanan İhaleler" dataKey="completedCount" stackId="a" fill={primaryLight} radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </Card.Body>
            </Card>
          </Col>
          <Col lg={4} className="mb-4">
            <Card className="chart-card">
              <Card.Header>
                <h5 className="mb-0">Filo Kullanımı</h5>
              </Card.Header>
              <Card.Body>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart
                    data={dashboardData.fleetUsage}
                    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                  >
                    <defs>
                      <linearGradient id="colorSedan" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                      </linearGradient>
                      <linearGradient id="colorSuv" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#82ca9d" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#82ca9d" stopOpacity={0}/>
                      </linearGradient>
                      <linearGradient id="colorHatchback" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#ffc658" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#ffc658" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f5f5f5" />
                    <XAxis dataKey="name" axisLine={false} tickLine={false} />
                    <YAxis axisLine={false} tickLine={false} />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Area 
                      type="monotone" 
                      dataKey="sedan" 
                      name="Sedan" 
                      stroke="#8884d8" 
                      fillOpacity={1} 
                      fill="url(#colorSedan)" 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="suv" 
                      name="SUV" 
                      stroke="#82ca9d" 
                      fillOpacity={1} 
                      fill="url(#colorSuv)" 
                    />
                    <Area 
                      type="monotone" 
                      dataKey="hatchback" 
                      name="Hatchback" 
                      stroke="#ffc658" 
                      fillOpacity={1} 
                      fill="url(#colorHatchback)" 
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default DashboardCards; 