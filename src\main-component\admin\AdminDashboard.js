import React from 'react';
import { Container, Row, Col, Card } from 'react-bootstrap';
import { useSelector } from 'react-redux';
import { FaUsers, FaBuilding, FaCar, FaChartLine } from 'react-icons/fa';
import './AdminDashboard.css';

const AdminDashboard = () => {
  const { user } = useSelector((state) => state.auth);

  // Örnek veri - gerçek uygulamada API'den gelecek
  const dashboardData = {
    totalCustomers: 145,
    totalRentalCompanies: 26,
    totalActiveTenders: 32,
    totalVehicles: 287
  };

  return (
    <div className="admin-dashboard py-5">
      <Container>
        <Row className="mb-4">
          <Col>
            <h1>Ho<PERSON> Geldiniz, {user?.name || 'Admin'}</h1>
            <p className="text-muted">Sistem Yönetim Paneli</p>
          </Col>
        </Row>

        <Row className="dashboard-stats mb-5">
          <Col sm={6} md={3} className="mb-3">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaBuilding size={30} />
                </div>
                <h2 className="count">{dashboardData.totalCustomers}</h2>
                <p className="label mb-0">Kurumsal Müşteriler</p>
              </Card.Body>
            </Card>
          </Col>

          <Col sm={6} md={3} className="mb-3">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaUsers size={30} />
                </div>
                <h2 className="count">{dashboardData.totalRentalCompanies}</h2>
                <p className="label mb-0">Kiralama Şirketleri</p>
              </Card.Body>
            </Card>
          </Col>

          <Col sm={6} md={3} className="mb-3">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaChartLine size={30} />
                </div>
                <h2 className="count">{dashboardData.totalActiveTenders}</h2>
                <p className="label mb-0">Aktif İhaleler</p>
              </Card.Body>
            </Card>
          </Col>

          <Col sm={6} md={3} className="mb-3">
            <Card className="dashboard-card h-100">
              <Card.Body className="d-flex flex-column align-items-center">
                <div className="icon-container mb-3">
                  <FaCar size={30} />
                </div>
                <h2 className="count">{dashboardData.totalVehicles}</h2>
                <p className="label mb-0">Toplam Araç</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        <Row>
          <Col lg={8} className="mb-4">
            <Card className="h-100">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Son Kayıt Olan Şirketler</h5>
                <button className="btn btn-sm btn-outline-primary">Tümünü Gör</button>
              </Card.Header>
              <Card.Body>
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>Şirket Adı</th>
                        <th>Tip</th>
                        <th>Kayıt Tarihi</th>
                        <th>Durum</th>
                        <th>İşlem</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>Modern Otomotiv A.Ş.</td>
                        <td>Kiralama Şirketi</td>
                        <td>25.05.2023</td>
                        <td><span className="badge bg-warning">Onay Bekliyor</span></td>
                        <td>
                          <button className="btn btn-sm btn-success me-1">Onayla</button>
                          <button className="btn btn-sm btn-danger">Reddet</button>
                        </td>
                      </tr>
                      <tr>
                        <td>Teknoloji Partners Ltd.</td>
                        <td>Kurumsal Müşteri</td>
                        <td>20.05.2023</td>
                        <td><span className="badge bg-warning">Onay Bekliyor</span></td>
                        <td>
                          <button className="btn btn-sm btn-success me-1">Onayla</button>
                          <button className="btn btn-sm btn-danger">Reddet</button>
                        </td>
                      </tr>
                      <tr>
                        <td>Mega Taşımacılık A.Ş.</td>
                        <td>Kurumsal Müşteri</td>
                        <td>18.05.2023</td>
                        <td><span className="badge bg-success">Onaylandı</span></td>
                        <td>
                          <button className="btn btn-sm btn-secondary">Detay</button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={4} className="mb-4">
            <Card className="h-100">
              <Card.Header>
                <h5 className="mb-0">Yönetim Araçları</h5>
              </Card.Header>
              <Card.Body>
                <div className="d-grid gap-2">
                  <button className="btn btn-primary">Kullanıcı Yönetimi</button>
                  <button className="btn btn-outline-secondary">Şirket Onayları</button>
                  <button className="btn btn-outline-secondary">İhale Yönetimi</button>
                  <button className="btn btn-outline-secondary">Sistem Ayarları</button>
                  <button className="btn btn-outline-secondary">Raporlar</button>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default AdminDashboard; 