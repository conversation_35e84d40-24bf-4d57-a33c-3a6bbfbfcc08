import React from "react";
import { Card, ListGroup, Badge } from "react-bootstrap";
import { FaUser } from "react-icons/fa";

// <PERSON>ruma göre renk döndüren yardımcı fonksiyon
const getStatusVariant = (status) => {
  switch (status) {
    case "LIVE":
      return "success";
    case "COMPLETED":
      return "primary";
    case "CANCELLED":
      return "danger";
    case "REVIEW":
      return "secondary";
    default:
      return "warning";
  }
};

// history: [{ historyId, createdBy, createdDate, tenderStatus: { statusName, name } }]
const HistoryPanel = ({ history = [], t }) => {
  return (
    <Card className="tender-detail-card history-panel">
      <Card.Header as="h3">
        {t ? t("tender_details.history_panel_title") : "İşlem Geçmişi"}
      </Card.Header>
      <Card.Body style={{ padding: 0 }}>
        <ListGroup variant="flush">
          {history.length === 0 ? (
            <ListGroup.Item style={{ textAlign: "center", color: "#888", padding: "32px 0" }}>
              {t ? t("tender_details.no_history") : "Geçmiş kaydı yok."}
            </ListGroup.Item>
          ) : (
            history.map((item) => (
              <ListGroup.Item
                key={item.historyId}
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: 12,
                  padding: "14px 12px",
                  borderBottom: "1px solid #f0f0f0",
                  background: "#fcfcfc",
                  borderRadius: 8,
                  marginBottom: 8,
                  boxShadow: "0 1px 4px rgba(0,0,0,0.02)",
                }}
              >
                <span style={{ fontSize: 14, color: "#555", fontWeight: 500, display: "flex", alignItems: "center", minWidth: 0 }}>
                  <FaUser style={{ marginRight: 4, color: "#888" }} />
                  {item.createdBy}
                </span>
                <span style={{ fontSize: 13, color: "#888", marginLeft: 16, whiteSpace: "nowrap", flex: 1 }}>
                  {new Date(item.createdDate).toLocaleString("tr-TR", {
                    year: "numeric",
                    month: "short",
                    day: "2-digit",
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </span>
                <Badge
                  bg={getStatusVariant(item.tenderStatus?.name)}
                  style={{ fontSize: 12, padding: "6px 12px", height: 24, display: "flex", alignItems: "center", justifyContent: "center", minWidth: 0 }}
                >
                  {item.tenderStatus?.statusName || item.tenderStatus?.name || "-"}
                </Badge>
              </ListGroup.Item>
            ))
          )}
        </ListGroup>
      </Card.Body>
    </Card>
  );
};

export default HistoryPanel; 