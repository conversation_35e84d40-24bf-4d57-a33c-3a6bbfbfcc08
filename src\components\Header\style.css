/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@500;600;700&family=Bebas+Neue&family=Archivo+Black&family=Barlow:wght@700;800;900&display=swap');
@import url('../../styles/colors.css');

/* Charlevoix Font */
@font-face {
  font-family: 'Charlevoix';
  src: url('../../fonts/CharlevoixPro-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Charlevoix';
  src: url('../../fonts/CharlevoixPro-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Charlevoix';
  src: url('../../fonts/CharlevoixPro-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Charlevoix';
  src: url('../../fonts/CharlevoixPro-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

/* Logo Stilleri */
.brand-logo {
  max-height: 45px;
  width: auto;
}

.footer-brand-logo {
  max-height: 60px;
  width: auto;
  filter: brightness(1.1);
}

/* ===================== HEADER ======================*/
.gauto-header-top-area {
  background: #020202 none repeat scroll 0 0;
  color: #eee;
  margin-bottom: 10px;
}

.header-top-left {
  position: relative;
  z-index: 1;
  padding: 10px 0;
}

.header-top-left:before {
  position: absolute;
  background: var(--primary-color) none repeat scroll 0 0;
  content: "";
  top: 0;
  right: 37%;
  width: 2030px;
  height: 120%;
  -webkit-transform: skewX(45deg);
  transform: skewX(45deg);
  z-index: -1;
}

.header-top-right {
  text-align: right;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.header-dropdowns {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 10px;
  height: 100%;
}

.header-dropdowns .dropdown {
  display: inline-flex;
  align-items: center;
  height: 100%;
}

.header-dropdowns > a {
  color: #eee;
  text-transform: capitalize;
  margin-right: 13px;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.header-dropdowns > a svg {
  margin-right: 5px;
}

.header-top-left p {
  position: relative;
  top: 5px;
  font-size: 14px;
  color: #ffffff;
  font-weight: 500;
  text-transform: capitalize;
  letter-spacing: 1px;
}

.header-top-left p svg {
  width: 25px;
  text-align: center;
  height: 25px;
  line-height: 25px;
  background: #fff none repeat scroll 0 0;
  color: var(--primary-color);
  border-radius: 50%;
  margin: 0 2px;
  padding: 5px;
}

.header-top-right > .dropdown {
  display: inline-block;
  vertical-align: middle;
  height: 100%;
  margin-top: 0;
  margin-bottom: 0;
}

.header-top-right > .dropdown button {
  background: transparent;
  color: #eee;
  border: medium none;
  padding: 0;
  cursor: pointer;
  font-size: 14px;
}

/* Kullanıcı dropdown menüsü stil başlangıç */
.header-dropdowns .dropdown .user-dropdown,
.header-dropdowns .dropdown #dropdown-basic {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #fff;
  padding: 0px 10px;
  background-color: transparent;
  border: none;
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  height: 30px;
  line-height: 30px;
}

.header-dropdowns .dropdown .user-dropdown svg,
.header-dropdowns .dropdown #dropdown-basic svg {
  font-size: 18px;
  margin-right: 3px;
}

.header-dropdowns .dropdown .user-dropdown:hover,
.header-dropdowns .dropdown .user-dropdown:focus,
.header-dropdowns .dropdown #dropdown-basic:hover,
.header-dropdowns .dropdown #dropdown-basic:focus {
  color: var(--primary-color);
  background-color: transparent;
  box-shadow: none;
  border: none;
}

.header-dropdowns .dropdown .dropdown-menu .dropdown-item svg {
  margin-right: 8px;
  font-size: 14px;
}

.header-dropdowns .dropdown .dropdown-menu .dropdown-divider {
  margin: 0.25rem 0;
  border-color: #444;
}
/* Kullanıcı dropdown menüsü stil bitiş */

.header-top-right > .dropdown .dropdown-menu.show {
  border-radius: 0;
  border: medium none;
  background: #020202 none repeat scroll 0 0;
  color: #fff;
  margin: 0;
  left: auto !important;
  right: 0 !important;
  padding: 0;
  min-width: 130px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

.header-top-right > .dropdown .dropdown-menu a {
  padding: 5px 10px;
  border-bottom: 1px solid #444;
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
  color: #fff;
}

.header-top-right > .dropdown .dropdown-menu a:hover {
  background: #2e2e2e none repeat scroll 0 0;
}

.header-top-right > .dropdown .dropdown-menu li img {
  margin-right: 5px;
}

.header-top-right > a {
  color: #eee;
  text-transform: capitalize;
  margin-right: 13px;
  display: inline-flex;
  align-items: center;
}

.header-top-right > a svg {
  margin-right: 5px;
}

.gauto-main-header-area {
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.gauto-main-header-area .container {
  display: flex;
  align-items: center;
}

.gauto-main-header-area .row {
  width: 100%;
  align-items: center;
}

.site-logo {
  display: flex;
  align-items: center;
  padding: 5px 0;
}

.header-promo {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.single-header-promo {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
  padding: 5px 10px;
  transition: all 0.3s ease;
}

.single-header-promo:hover {
  transform: translateY(-2px);
}

.header-promo-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: #fff;
  transition: all 0.3s ease;
}

.header-promo-icon .promo-icon {
  font-size: 16px;
}

.single-header-promo:hover .header-promo-icon {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.header-promo-info {
  color: #555;
}

.header-promo-info h3 {
  font-size: 14px;
  line-height: 18px;
  color: #001238;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  margin-bottom: 0;
  transition: color 0.3s ease;
}

.header-promo-info p {
  font-size: 12px;
  margin: 0;
  color: #666;
  transition: color 0.3s ease;
}

.header-action {
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 100%;
}

.header-action a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 6px 15px 6px 0px;
  background: var(--primary-color) none repeat scroll 0 0;
  color: #fff;
  text-transform: uppercase;
  font-weight: 500;
  position: relative;
  z-index: 1;
  font-size: 12px;
  height: 34px;
  transition: all 0.3s ease;
}

.header-action a:before {
  position: absolute;
  background: var(--primary-color) none repeat scroll 0 0;
  content: "";
  top: 0;
  left: -18px;
  width: 35%;
  height: 100%;
  -webkit-transform: skewX(45deg);
  transform: skewX(45deg);
  z-index: -1;
  transition: all 0.3s ease;
}

.header-action a:hover {
  background-color: var(--secondary-color);
}

.header-action a:hover:before {
  background-color: var(--secondary-color);
}

.header-action a svg {
  margin-right: 5px;
  font-size: 12px;
}

.gauto-mainmenu-area {
  background: var(--secondary-dark) none repeat scroll 0 0;
  position: relative;
  z-index: 9;
}

.mainmenu ul {
  text-align: left;
}

.mainmenu ul li {
  display: inline-block;
  position: relative;
}

.mainmenu ul li a {
  color: #eee;
  display: block;
  padding: 20px 12px;
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
}

.mainmenu ul li:hover > a,
.mainmenu ul li.active > a {
  color: var(--primary-color);
}

.mainmenu ul li ul {
  position: absolute;
  top: 120%;
  left: 0;
  width: 180px;
  background: #fff none repeat scroll 0 0;
  z-index: 99;
  visibility: hidden;
  opacity: 0;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

.mainmenu ul li:hover ul {
  visibility: visible;
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  top: 100%;
}

.mainmenu ul li ul li {
  display: block;
  border-bottom: 1px solid #eee;
}

.mainmenu ul li ul li:last-child {
  border-bottom: 0px solid #eee;
}

.mainmenu ul li ul li a {
  color: #001238;
  padding: 10px 15px;
  display: block;
  font-size: 14px;
}

.mainmenu ul li ul li a:hover {
  padding-left: 20px;
}

.main-search-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: 13px;
  position: relative;
}

.header-cart-box #dropdownMenu1 {
  border: medium none;
  background: url(/src/img/icon-cart.png) no-repeat scroll 0 0;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: inline-block;
  position: relative;
  top: 8px;
}

.header-cart-box #dropdownMenu1 > span {
  position: absolute;
  width: 18px;
  height: 18px;
  background: var(--primary-color) none repeat scroll 0 0;
  font-size: 10px;
  line-height: 20px;
  color: #fff;
  border-radius: 50%;
  right: -6px;
  top: -10px;
  text-align: center;
}

.cart-icon.dropdown-toggle:after {
  color: var(--primary-color);
}

/* Arama kutusu stili başlangıç */
.mainmenu-right {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.header-search-form {
  width: 100%;
  position: relative;
}

.header-search-form form {
  position: relative;
  background: #1a1a1a none repeat scroll 0 0;
  height: 40px;
  width: 100%;
  border-radius: 20px;
}

.header-search-form form input {
  position: absolute;
  left: 0;
  width: 100%;
  border: medium none;
  background: transparent;
  padding: 5px 20px;
  height: 100%;
  color: #eee;
}

.header-search-form form button {
  position: absolute;
  right: 0;
  background: transparent;
  color: #eee;
  border: medium none;
  height: 100%;
  width: 50px;
  cursor: pointer;
  font-size: 16px;
}
/* Arama kutusu stili bitiş */

.search-box {
  width: 100%;
  margin-left: 20px;
}

.dropdown-menu.cart-dropdown {
  left: auto;
  min-width: 320px;
  right: 0;
  padding: 20px;
  border-radius: 0 !important;
  top: 80px;
  margin-top: -1px;
}

.login .dropdown {
  height: 80px;
  padding: 30px 0;
}

.header-cart-box .dropdown.show .cart-dropdown {
  opacity: 1;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  visibility: visible;
  margin-top: 0;
  z-index: 9999999;
  color: var(--primary-color);
}

.product-remove {
  float: right;
  font-size: 16px !important;
  color: var(--primary-color);
}

.cart-btn-pro-img {
  float: left;
  margin-bottom: 0;
  margin-right: 20px;
  width: 75px;
}

.cart-btn-pro-img > a {
  padding: 0 !important;
}

.cart-dropdown > ul.product_list > li {
  border-bottom: 1px solid #ddd;
  margin-bottom: 10px;
  padding-bottom: 10px;
}

.cart-btn-pro-cont h4,
.cart-btn-pro-cont h4 a {
  font-size: 17px;
  line-height: 30px;
  color: #001238;
  font-weight: 600;
  font-family: "Poppins", sans-serif;
  text-transform: capitalize;
}

.cart-btn-pro-cont p {
  color: #777;
  font-size: 14px;
}

.cart-btn-pro-cont span.price {
  color: #111;
  font-weight: 500;
}

.cart-subtotal p {
  color: #444;
  text-align: center;
  margin: 10px 0;
  font-size: 14px;
}

.cart-subtotal p span {
  color: #111;
  font-weight: 500;
  font-size: 16px;
}

.cart-btn {
  text-align: center;
  background: #020202 none repeat scroll 0 0;
  position: relative;
  z-index: 1;
  overflow: hidden;
  width: 100%;
  margin: 15px auto 0;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

.cart-btn a {
  display: inline-block;
  text-transform: uppercase;
  padding: 7px 15px;
  font-weight: 500;
  color: #fff;
  font-size: 14px;
}

.cart-btn:after {
  position: absolute;
  content: "";
  width: 66%;
  height: 100%;
  background: var(--primary-color) none repeat scroll 0 0;
  right: -36px;
  z-index: -1;
  -webkit-transform: skewX(40deg);
  transform: skewX(40deg);
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

.cart-btn:hover {
  background: var(--primary-color) none repeat scroll 0 0;
}

.cart-btn:hover:after,
.cart-btn:hover:before {
  background: var(--primary-color) none repeat scroll 0 0;
}

.btn-check:active + .btn-success:focus,
.btn-check:checked + .btn-success:focus,
.btn-success.active:focus,
.btn-success:active:focus,
.show > .btn-success.dropdown-toggle:focus {
  box-shadow: none;
}
.btn-check:focus + .btn-success,
.btn-success:focus {
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
}
.btn-check:active + .btn-success,
.btn-check:checked + .btn-success,
.btn-success.active,
.btn-success:active,
.show > .btn-success.dropdown-toggle {
  background-color: transparent;
  border-color: transparent;
}
@media (min-width: 992px) and (max-width: 1169px) {
  .header-top-left p {
    letter-spacing: 0;
  }
  .header-top-left p i {
    height: 20px;
    width: 20px;
    line-height: 20px;
  }
  .header-promo-info h3 {
    font-size: 17px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .header-top-left:before {
    right: 10%;
  }
  .header-top-left:before {
    right: 10%;
  }
  .gauto-responsive-menu {
    display: block;
    width: 100%;
  }
  .header-action {
    margin-top: 0;
  }
  .mainmenu {
    display: none !important;
  }
  .mainmenu-right {
    display: none !important;
  }
  .main-search-right {
    margin-top: 0;
    padding: 7px 0;
  }
  .header-cart-box #dropdownMenu1 {
    top: 0;
  }
  .gauto-about-area {
    padding-bottom: 50px;
  }
  .gauto-mainmenu-area {
    display: block !important;
    border-bottom: none;
    min-height: 70px;
  }
  .header-cart-box {
    width: 100%;
    text-align: right;
  }
  .site-logo {
    margin-top: 10px;
  }
}
@media (max-width: 767px) {
  .header-top-left:before {
    display: none;
  }
  .header-top-left {
    display: none;
  }
  .header-top-right {
    text-align: center;
  }
  .gauto-responsive-menu {
    display: block;
  }
  .site-logo {
    text-align: center;
    width: 35%;
    margin: 0 auto;
  }
  .gauto-about-area {
    padding-bottom: 50px;
  }
  .site-logo img {
    width: 100%;
  }
  .header-promo,
  .header-action {
    display: none;
  }
  .gauto-header-top-area {
    margin-bottom: 0;
  }
  .mainmenu {
    display: none !important;
  }
  .gauto-header-top-area {
    background: #fff none repeat scroll 0 0;
    border-bottom: 1px solid #eee;
  }
  .header-top-left p,
  .header-top-right > a,
  .header-top-right > .dropdown button {
    color: #001238;
  }
  .header-top-left p i {
    background: var(--primary-color) none repeat scroll 0 0;
    color: #fff;
  }
  .mainmenu-right {
    display: none !important;
  }
  .header-cart-box #dropdownMenu1 {
    float: right;
  }
  .header-cart-box {
    float: right;
    margin-top: 25px;
  }
  .login.dropdown {
    margin-top: 9px;
  }
  .gauto-mainmenu-area {
    display: block !important;
    border-bottom: none;
    min-height: 70px;
  }
  .header-cart-box .dropdown.show .cart-dropdown {
    margin-top: 13px;
    min-width: 270px;
    margin-right: 15px;
  }
  .cart-btn-pro-img {
    width: 60px;
    margin-right: 10px;
  }
  .cart-btn-pro-cont {
    width: 100px;
  }
  .header-cart-box #dropdownMenu1 {
    margin-right: 15px;
  }
  .main-search-right {
    margin-top: 0;
    width: 88%;
    margin-left: 15px;
  }
  .mean-container .mean-nav ul li a:hover {
    color: var(--primary-color);
  }
}
@media only screen and (min-width: 480px) and (max-width: 767px) {
  .header-top-left:before {
    display: none;
  }
  .header-top-left {
    display: none;
  }
  .header-top-right {
    text-align: center;
  }
  .gauto-responsive-menu {
    display: block;
  }
  .site-logo {
    text-align: center;
    width: 35%;
    margin: 0 auto;
  }
  .gauto-about-area {
    padding-bottom: 50px;
  }
  .site-logo img {
    width: 100%;
  }
  .single-service {
    padding: 20px;
  }
  .service-text h3 {
    font-size: 18px;
    letter-spacing: 0;
  }
  .header-promo,
  .header-action {
    display: none;
  }
  .gauto-header-top-area {
    margin-bottom: 0;
  }
  .mainmenu {
    display: none;
  }
  .gauto-header-top-area {
    background: #fff none repeat scroll 0 0;
    border-bottom: 1px solid #eee;
  }
  .header-top-left p,
  .header-top-right > a,
  .header-top-right > .dropdown button {
    color: #001238;
  }
  .header-top-left p i {
    background: var(--primary-color) none repeat scroll 0 0;
    color: #fff;
  }
  .offer-action:after {
    width: 62%;
  }
  .service-details-image img {
    width: 100%;
  }
  .mainmenu-right {
    display: none;
  }
  .header-cart-box #dropdownMenu1 {
    float: right;
  }
  .header-cart-box {
    float: right;
    margin-top: 25px;
  }
  .login.dropdown {
    margin-top: 9px;
  }
  .gauto-mainmenu-area {
    display: none;
  }
  .header-cart-box .dropdown.show .cart-dropdown {
    margin-top: 13px;
    min-width: 270px;
    margin-right: 15px;
  }
  .cart-btn-pro-img {
    width: 60px;
    margin-right: 10px;
  }
  .cart-btn-pro-cont {
    width: 100px;
  }
  .main-search-right {
    margin-top: 0;
    width: 88%;
    margin-left: 15px;
  }
  .mean-container .mean-nav ul li a:hover {
    color: var(--primary-color);
  }
}

/* Logo bileşeni artık ayrı bir component olarak tanımlandı */

.site-logo {
  padding: 5px 0;
}

.single-header-promo:hover .header-promo-info h3 {
  color: var(--primary-color);
}

.single-header-promo:hover .header-promo-info p {
  color: #333;
}
