/* ===================== HOW IT WORKS ======================*/
@import url('../../styles/colors.css');

.how-it-works {
  background-color: #f8f9fa;
  padding: 80px 0;
}

.process-title-area {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.process-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: var(--primary-color);
  font-size: 20px;
  margin-right: 15px;
}

.subtitle {
  text-transform: uppercase;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 5px;
}

.process-step-card {
  position: relative;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 25px 20px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.5s ease;
  overflow: visible;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.process-step-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.process-step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  font-weight: 700;
  font-size: 22px;
  margin-bottom: 15px;
  box-shadow: 0 4px 10px var(--primary-shadow);
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

.process-step-number:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 15px var(--primary-shadow);
}

.process-step-number span {
  display: block;
  line-height: 1;
}

.process-step-content h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #1c3b37;
}

.process-step-content p {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.process-step-card:hover .process-step-number {
  transform: scale(1.1);
}

/* SVG Path için stiller */
.process-svg-container {
  display: none;
}

.process-path {
  display: none;
}

/* Process Steps Row için pozisyon */
.process-steps-row {
  position: relative;
  display: flex;
  justify-content: center;
}

.process-step-col {
  display: flex;
  justify-content: center;
}

/* Numaralar arasındaki bağlantı çizgileri için stil */
.connecting-paths {
  display: none;
}

/* Container stil düzenlemeleri */
.position-relative {
  overflow: visible !important;
  padding-top: 30px;
  padding-bottom: 30px;
  position: relative;
}

/* react-archer için stil düzenlemeleri */
._react-archer__arrow {
  z-index: 5 !important;
  filter: drop-shadow(0 0 2px rgba(var(--primary-color-rgb), 0.4));
  pointer-events: none;
  animation: fadeIn 1s ease-out forwards;
  animation-delay: 1.5s;
  opacity: 0;
}

._react-archer__arrow-path {
  stroke: var(--primary-color);
  stroke-dasharray: 5;
  animation: dash 0.5s linear infinite;
  filter: drop-shadow(0 0 2px rgba(var(--primary-color-rgb), 0.4));
}

@keyframes dash {
  to {
    stroke-dashoffset: -300;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* SVG Container için stiller */
._react-archer__svg-container {
  overflow: visible !important;
  z-index: 2 !important;
  position: absolute !important;
  pointer-events: none;
}

/* Süreç kartları için farklı konumlar - ama animasyonsuz */
.process-step-col:nth-child(odd) {
  transform: translateY(-20px);
}

.process-step-col:nth-child(even) {
  transform: translateY(20px);
}

/* Kartların asenkron animasyonları için farklı gecikmeler - sadece fadeInUp için */
.process-step-col:nth-child(1) .process-step-card {
  animation-delay: 0.1s;
}

.process-step-col:nth-child(2) .process-step-card {
  animation-delay: 0.3s;
}

.process-step-col:nth-child(3) .process-step-card {
  animation-delay: 0.5s;
}

.process-step-col:nth-child(4) .process-step-card {
  animation-delay: 0.7s;
}

/* Numaralı butonlar için dönen parıltı efekti */
.process-step-number::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
  border-radius: 50%;
  border: 2px solid rgba(var(--primary-color-rgb), 0.3);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  70% {
    transform: scale(1.15);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@media (max-width: 767.98px) {
  .process-title-area {
    flex-direction: column;
    text-align: center;
  }
  
  .process-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .process-step-card {
    padding: 20px 15px;
  }
  
  .process-step-number {
    width: 45px;
    height: 45px;
  }
  
  .process-step-number span {
    font-size: 20px;
  }
  
  .process-step-content h3 {
    font-size: 16px;
  }
}

@media (max-width: 767px) {
  .process-step-card {
    margin-bottom: 15px;
  }
  
  .process-step-content h3 {
    font-size: 16px;
  }
  
  .how-it-works {
    padding: 50px 0;
  }
}

@media (max-width: 991.98px) {
  .process-svg-container {
    display: none;
  }
  
  .process-step-col:nth-child(odd),
  .process-step-col:nth-child(even) {
    transform: none;
    margin-bottom: 15px;
  }
  
  .process-step-card {
    animation: none !important;
    opacity: 1;
    transform: none !important;
  }
  
  .process-step-card:hover {
    transform: translateY(-5px) !important;
  }
}

.process-title strong {
  color: var(--primary-color);
}

.process-cta-button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 12px 25px;
  border-radius: 30px;
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
  margin-top: 40px;
}

.process-cta-button:hover {
  background-color: #e74700;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(var(--primary-color-rgb), 0.4);
  color: white;
} 