import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import Header from "../../components/Header";
import HotOffers from "../../components/HotOffers";
import Service from "../../components/Service";
import CarList from "../../components/CarList";
import Testimonial from "../../components/Testimonial";
import Footer from "../../components/Footer";
import Hero from '../../components/Hero';
import { useTranslation } from 'react-i18next';
import DashboardCards from '../../components/DashboardCards';
const CustomerDashboard = () => {
  const { user } = useSelector((state) => state.auth);
  const { t } = useTranslation();

  const heroTitle = t("hero.customer.title", { name: user.name });
  const heroDescription = t("hero.customer.subtitle");
  const key = "hero.customer.buttonText";
  const heroButtonText = t(key) === key ? t("hero.buttonText"): t(key);
  // Örnek veri - gerçek uygulamada API'den gelecek
  const dashboardData = {
    activeRentals: 2,
    upcomingBookings: 1, 
    totalSpent: '24.500 ₺',
    notifications: 3
  };

  return (
    <Fragment>
      <Header />
      {/* Müşteri Karşılama Bölümü */}
      <Hero 
      title={heroTitle}
      description={heroDescription}
      buttonText={heroButtonText} />
      <DashboardCards />
      {/* Müşteri Yorumları */}
      <Testimonial />
      <Footer />
    </Fragment>
  );
};

export default CustomerDashboard; 