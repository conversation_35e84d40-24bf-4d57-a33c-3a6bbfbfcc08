import React, { useState, useEffect, useRef } from "react";
import { Collapse } from "react-bootstrap";
import { Link } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  FaUserCircle,
  FaSignOutAlt,
} from "react-icons/fa";
import { logoutUser } from "../../redux/slices/authSlice";

import "./style.css";

const MobileMenu = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  const [isMenuShow, setIsMenuShow] = useState(false);
  const [isOpen, setIsOpen] = useState(0);
  const menuRef = useRef(null);
  const spinnerRef = useRef(null);

  // Menüyü aç/kapat
  const menuHandler = () => {
    setIsMenuShow(!isMenuShow);
  };

  // Mobil menü açıldığında, body'nin scrollunu engelle
  useEffect(() => {
    if (isMenuShow) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMenuShow]);

  // Alt menü açma/kapama
  const handleSetIsOpen = (id) => () => {
    setIsOpen(id === isOpen ? 0 : id);
  };
  
  // Çıkış yapma
  const handleLogout = () => {
    dispatch(logoutUser());
    setIsMenuShow(false);
  };

  // Dışarı tıklama kapatma
  const handleClickOutside = (e) => {
    if (
      menuRef.current && 
      !menuRef.current.contains(e.target) && 
      spinnerRef.current && 
      !spinnerRef.current.contains(e.target) && 
      isMenuShow
    ) {
      setIsMenuShow(false);
    }
  };

  // Dışarı tıklama efekti
  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuShow]);

  // Menü öğeleri
  const menus = [
    {
      id: 1,
      title: t("header-navigation.home"),
      link: "/home",
    },
    {
      id: 2,
      title: t("header-navigation.about"),
      link: "/about",
    },
    {
      id: 3,
      title: t("header-navigation.service"),
      link: "/service",
      submenu: [
        {
          id: 31,
          title: t("header-navigation.all_service"),
          link: "/service",
        },
        {
          id: 32,
          title: t("header-navigation.service_details"),
          link: "/service-single",
        },
      ],
    },
    {
      id: 4,
      title: t("header-navigation.cars"),
      link: "/car-listing",
      submenu: [
        {
          id: 41,
          title: t("header-navigation.car_listing"),
          link: "/car-listing",
        },
        {
          id: 42,
          title: t("header-navigation.car_booking"),
          link: "/car-booking",
        },
      ],
    },
    {
      id: 8,
      title: t("header-navigation.contact"),
      link: "/contact",
    }
  ];
  
  // Authenticated kullanıcılar için ek menüler
  const authenticatedMenus = isAuthenticated ? [
    {
      id: 5,
      title: t("header-navigation.my_tenders"),
      link: "/my-tenders",
    }
  ] : [];
  
  // Tüm menüleri birleştir
  const allMenus = [...menus.slice(0, 4), ...authenticatedMenus, ...menus.slice(4)];

  return (
    <div className="responsiveMenu">
      {/* Hamburger Menu Button */}
      <div 
        ref={spinnerRef}
        className={`spinner-master ${isMenuShow ? "active" : ""}`}
        onClick={menuHandler}
      >
        <label htmlFor="spinner-form" aria-label={t("common.toggle_menu")}>
          <div className="spinner diagonal part-1"></div>
          <div className="spinner horizontal"></div>
          <div className="spinner diagonal part-2"></div>
        </label>
      </div>
      
      {/* Mobile Menu Container */}
      <div 
        ref={menuRef}
        className={`mobileMenu ${isMenuShow ? "active" : ""}`}
      >
        <ul className="responsivemenu">
          {/* User Info if Logged In */}
          {isAuthenticated && (
            <li className="mobile-user-info">
              <Link to="/profile" onClick={() => setIsMenuShow(false)}>
                <FaUserCircle className="mobile-icon" />
                <span>{user?.name || t("header-navigation.user")}</span>
              </Link>
            </li>
          )}
          
          {/* Menu Items */}
          {allMenus.map((item) => (
            <li key={item.id}>
              {item.submenu ? (
                // Items with submenu
                <>
                  <p
                    onClick={handleSetIsOpen(item.id)}
                    aria-expanded={item.id === isOpen}
                  >
                    {item.title}
                    <span>{item.id === isOpen ? "-" : "+"}</span>
                  </p>
                  <Collapse in={item.id === isOpen}>
                    <ul className="sub-menu">
                      {item.submenu.map((submenu) => (
                        <li key={submenu.id}>
                          <Link 
                            to={submenu.link}
                            onClick={() => setIsMenuShow(false)}
                          >
                            {submenu.title}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </Collapse>
                </>
              ) : (
                // Direct link items
                <Link 
                  to={item.link}
                  onClick={() => setIsMenuShow(false)}
                >
                  {item.title}
                </Link>
              )}
            </li>
          ))}
          
          {/* Logout Option if Logged In */}
          {isAuthenticated && (
            <li className="mobile-logout">
              <Link to="/" onClick={handleLogout}>
                <FaSignOutAlt className="mobile-icon" />
                <span>{t("header-navigation.logout")}</span>
              </Link>
            </li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default MobileMenu;
