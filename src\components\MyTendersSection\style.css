/* MyTendersSection Component Styles */
@import '../../styles/colors.css';

/* <PERSON><PERSON><PERSON> */
:root {
  --text-color: #212529;
  --text-muted-color: #6c757d;
  --border-color: #e9ecef;
  --bg-light: #f8f9fa;
  
  /* <PERSON><PERSON><PERSON> */
  --table-header-bg: #f5f7fa;
  --table-row-hover: #f1f4f8;
  --table-border: #e9ecef;
  
  /* <PERSON><PERSON> ve <PERSON>tki<PERSON>şim */
  --hover-transition: all 0.3s ease;
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 30px;
}

/* Dashboard Tab Bölümü */
.dashboard-tabs-section {
  margin-bottom: 30px;
}

/* Tab Stilleri - Geleneksel ve Şık */
.dashboard-tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.dashboard-tabs .nav-item {
  margin-right: 4px;
}

.dashboard-tabs .nav-link {
  padding: 12px 20px;
  border: 1px solid transparent;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  color: var(--text-muted-color);
  font-weight: 600;
  background-color: transparent;
  transition: var(--hover-transition);
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: -1px;
}

.dashboard-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: #fff;
  border-color: var(--border-color) var(--border-color) #fff;
}

.dashboard-tabs .nav-link:hover:not(.active) {
  background-color: var(--bg-light);
  color: var(--text-color);
  border-color: var(--bg-light) var(--bg-light) var(--border-color);
}

.dashboard-tabs .nav-link svg {
  margin-right: 8px;
  font-size: 16px;
}

/* İç Tablar */
.inner-tabs {
  border-bottom: 1px solid var(--border-color);
  padding: 0 1rem;
  margin-bottom: 1.5rem;
}

.inner-tabs .nav-item {
  margin-right: 0.5rem;
}

.inner-tabs .nav-link {
  padding: 10px 16px;
  font-weight: 500;
  color: var(--text-muted-color);
  border: none;
  border-bottom: 3px solid transparent;
  border-radius: 0;
  background-color: transparent;
  transition: var(--hover-transition);
}

.inner-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: transparent;
  border-bottom-color: var(--primary-color);
}

.inner-tabs .nav-link:hover:not(.active) {
  color: var(--text-color);
  border-bottom-color: var(--border-color);
}

/* Kart gölgesi ve geçişleri */
.card {
  transition: var(--hover-transition);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  border-color: var(--border-color) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Tablo stilleri - Global */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.data-table {
  width: 100%;
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.data-table th {
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 15px;
  border-top: none;
  border-bottom: 2px solid var(--table-border);
  color: var(--text-muted-color);
  background-color: var(--table-header-bg);
}

.data-table td {
  padding: 15px;
  vertical-align: middle;
  border-top: none;
  border-bottom: 1px solid var(--table-border);
  color: var(--text-color);
  font-size: 14px;
}

/* Satır vurgulaması */
.table-row {
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background-color: var(--table-row-hover);
}

.cell-primary {
  font-weight: 600;
  color: var(--primary-color);
}

.cell-title {
  font-weight: 500;
  max-width: 250px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cell-center {
  text-align: center;
}

.cell-actions {
  text-align: right;
  white-space: nowrap;
}

/* Durum Rozeti (Status Badge) Stilleri */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.3px;
  position: relative;
  border: 1px solid transparent;
}

.status-badge svg,
.status-badge i {
  margin-right: 6px;
  font-size: 12px;
}

/* Status Badge Override Bootstrap */
.status-badge.bg-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #28a745 !important;
  border-color: rgba(40, 167, 69, 0.2) !important;
}

.status-badge.bg-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  color: #856404 !important;
  border-color: rgba(255, 193, 7, 0.2) !important;
}

.status-badge.bg-secondary {
  background-color: rgba(108, 117, 125, 0.1) !important;
  color: #6c757d !important;
  border-color: rgba(108, 117, 125, 0.2) !important;
}

.status-badge.bg-danger {
  background-color: rgba(220, 53, 69, 0.1) !important;
  color: #dc3545 !important;
  border-color: rgba(220, 53, 69, 0.2) !important;
}

.status-badge.bg-info {
  background-color: rgba(23, 162, 184, 0.1) !important;
  color: #17a2b8 !important;
  border-color: rgba(23, 162, 184, 0.2) !important;
}

/* Status Badge Indicators */
.status-badge.bg-success svg {
  color: #28a745;
}

.status-badge.bg-warning svg {
  color: #856404;
}

.status-badge.bg-secondary svg {
  color: #6c757d;
}

.status-badge.bg-danger svg {
  color: #dc3545;
}

.status-badge.bg-info svg {
  color: #17a2b8;
}

/* Buton stilleri - Standartlaştırılmış */
.btn-table {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 7px 14px;
  border-radius: var(--border-radius-sm);
  font-weight: 500;
  font-size: 13px;
  transition: var(--hover-transition);
  border: 1px solid transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  margin-right: 0.5rem;
}

.btn-table:last-child {
  margin-right: 0;
}

.btn-table i,
.btn-table svg {
  margin-right: 6px;
  font-size: 14px;
}

.btn-table:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-table:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Buton varyantları */
.btn-table-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-table-primary:hover {
  background-color: #0069d9;
}

.btn-table-success {
  background-color: var(--success-color);
  color: white;
}

.btn-table-success:hover {
  background-color: #218838;
}

.btn-table-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-table-danger:hover {
  background-color: #c82333;
}

.btn-table-info {
  background-color: var(--info-color);
  color: white;
}

.btn-table-info:hover {
  background-color: #138496;
}

/* Outline Buton Varyantları */
.btn-table-outline-primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

.btn-table-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

.btn-table-outline-danger {
  border-color: var(--danger-color);
  color: var(--danger-color);
  background-color: transparent;
}

.btn-table-outline-danger:hover {
  background-color: var(--danger-color);
  color: white;
}

.btn-table-outline-success {
  border-color: var(--success-color);
  color: var(--success-color);
  background-color: transparent;
}

.btn-table-outline-success:hover {
  background-color: var(--success-color);
  color: white;
}

.btn-table-outline-warning {
  border-color: var(--warning-color);
  color: var(--warning-color);
  background-color: transparent;
}

.btn-table-outline-warning:hover {
  background-color: var(--warning-color);
  color: white;
}

/* Ana buton stilleri */
.btn-with-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-weight: 500;
  transition: var(--hover-transition);
  border-radius: var(--border-radius-sm);
}

/* Responsive buton metni gizleme */
@media (max-width: 768px) {
  .action-text {
    display: none;
  }
  
  .cell-actions .btn-table {
    padding: 7px;
    margin-right: 4px;
  }
  
  .btn-table i, 
  .btn-table svg {
    margin-right: 0;
  }
  
  .data-table th,
  .data-table td {
    padding: 12px 8px;
  }
  
  .dashboard-tabs .nav-link {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .dashboard-tabs .nav-link svg {
    margin-right: 4px;
    font-size: 14px;
  }
  
  .inner-tabs .nav-link {
    padding: 8px 10px;
    font-size: 13px;
  }
}

/* Yükleme durumu stilleri */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-container p {
  color: var(--text-muted-color);
  font-size: 14px;
  margin-top: 15px;
}

/* Boş durumlar için stiller */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 250px;
  padding: 30px;
}

.empty-icon {
  color: var(--text-muted-color);
  opacity: 0.6;
}

.empty-state h6 {
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.empty-state p {
  margin-bottom: 20px;
  max-width: 400px;
  color: var(--text-muted-color);
} 