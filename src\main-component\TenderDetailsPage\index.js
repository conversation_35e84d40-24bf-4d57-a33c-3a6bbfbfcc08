import React, { Fragment, useEffect, useState } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, Badge, Card, ListGroup, Button, Alert, Toast, Tooltip, OverlayTrigger } from "react-bootstrap";
import { 
  FaCar, 
  FaCogs, 
  FaGasPump, 
  FaCalendarAlt, 
  FaUserFriends, 
  FaClock, 
  FaArrowLeft,
  FaMapMarkerAlt,
  FaMoneyBillWave,
  FaBuilding,
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaChartLine,
  FaPhone,
  FaEnvelope,
  FaGlobe,
  FaStar,
  FaGavel,
  FaMedal,
  FaSnowflake,
  FaRegClipboard,
  FaIdCard
} from "react-icons/fa";

import PageTitle from "../../components/PageTitle";
import tenderService from "../../services/tenderService";
import HistoryPanel from "../../components/common/HistoryPanel";

import "./style.css";

// Yardımcı fonksiyonlar component dışına taşındı
const getTenderStatusClass = (status, t) => {
  if (!status) return { className: '', text: '(eksik)', variant: 'secondary' };
  switch (status.name) {
    case "LIVE":
      return {
        className: "tender-status-active",
        text: t("tenders_page.active"),
        variant: "success"
      };
    case "COMPLETED":
      return {
        className: "tender-status-completed",
        text: t("tenders_page.completed"),
        variant: "primary"
      };
    case "CANCELLED":
      return {
        className: "tender-status-cancelled",
        text: t("tenders_page.cancelled"),
        variant: "danger"
      };
    default:
      return {
        className: "",
        text: status.statusName || status.name,
        variant: "secondary"
      };
  }
};
const formatDate = (dateString) => {
  if (!dateString) return '(eksik)';
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('tr-TR', options);
};
const formatTenderId = (id) => {
  if (!id) return '(eksik)';
  if (id.length <= 10) return id;
  return `${id.slice(0, 8)}...`;
};
const renderRating = (rating) => {
  const stars = [];
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  for (let i = 0; i < fullStars; i++) {
    stars.push(<FaStar key={`star-${i}`} className="star-icon filled" />);
  }
  if (hasHalfStar) {
    stars.push(<FaStar key="half-star" className="star-icon half" />);
  }
  const emptyStars = 5 - stars.length;
  for (let i = 0; i < emptyStars; i++) {
    stars.push(<FaStar key={`empty-star-${i}`} className="star-icon empty" />);
  }
  return <div className="company-rating">{stars} <span className="rating-value">({rating})</span></div>;
};

// Ana bilgiler Card.Body'sini fonksiyon olarak ayır
function TenderMainInfo({ tender, t, remainingTime }) {
  return (
    <div className="tender-main-info">
      <div className="tender-car-info">
        <Row className="car-specs">
          <Col md={6}>
            <div className="detail-item">
              <FaGasPump className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.fuel_type")}</span>
                <span className="detail-value">{tender.fuelType?.typeName || '(eksik)'}</span>
              </div>
            </div>
          </Col>
          <Col md={6}>
            <div className="detail-item">
              <FaCogs className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.transmission")}</span>
                <span className="detail-value">{tender.gearType?.typeName || '(eksik)'}</span>
              </div>
            </div>
          </Col>
        </Row>
        <Row className="car-specs">
          <Col md={6}>
            <div className="detail-item">
              <FaUserFriends className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.usage_status")}</span>
                <span className="detail-value">{tender.usageStatus?.statusName || '(eksik)'}</span>
              </div>
            </div>
          </Col>
          <Col md={6}>
            <div className="detail-item">
              <FaBuilding className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.body_type")}</span>
                <span className="detail-value">{tender.vehicleBodyType?.typeName || '(eksik)'}</span>
              </div>
            </div>
          </Col>
        </Row>
        <Row className="car-specs">
          <Col md={6}>
            <div className="detail-item">
              <FaChartLine className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.km_limit")}</span>
                <span className="detail-value">{tender.kmLimit?.limit || '(eksik)'}</span>
              </div>
            </div>
          </Col>
          <Col md={6}>
            <div className="detail-item">
              <FaMapMarkerAlt className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.city")}</span>
                <span className="detail-value">{tender.city?.name || '(eksik)'}</span>
              </div>
            </div>
          </Col>
        </Row>
        <Row className="car-specs">
          <Col md={6}>
            <div className="detail-item">
              <FaStar className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.usage_purpose")}</span>
                <span className="detail-value">{tender.usagePurpose?.purpose || '(eksik)'}</span>
              </div>
            </div>
          </Col>
          <Col md={6}>
            <div className="detail-item">
              <FaMedal className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.declaration_limit")}</span>
                <span className="detail-value">{tender.declarationLimit?.limitName || '(eksik)'}</span>
              </div>
            </div>
          </Col>
        </Row>
        <Row className="car-specs">
          <Col md={6}>
            <div className="detail-item">
              <FaCheckCircle className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.maintenance_responsible")}</span>
                <span className="detail-value">{tender.maintenanceResponsible?.responsible || '(eksik)'}</span>
              </div>
            </div>
          </Col>
          <Col md={6}>
            <div className="detail-item">
              <FaSnowflake className="detail-icon" />
              <div className="detail-content">
                <span className="detail-label">{t("tender_details.has_winter_tire")}</span>
                <span className="detail-value">{tender.hasWinterTire ? t("common.yes") : t("common.no")}</span>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      <div className="tender-period-info">
        <h4 className="section-title">{t("tender_details.rental_period")}</h4>
        <div className="detail-item">
          <FaCalendarAlt className="detail-icon" />
          <div className="detail-content">
            <span className="detail-value">{tender.rentalPeriod?.period || '(eksik)'}</span>
          </div>
        </div>
      </div>

      <div className="tender-date-info">
        <h4 className="section-title">{t("tender_details.tender_period")}</h4>
        <div className="detail-item">
          <FaClock className="detail-icon" />
          <div className="detail-content">
            <span className="detail-label">{t("tender_details.start_date")}</span>
            <span className="detail-value">{formatDate(tender.startDate)}</span>
          </div>
        </div>
        <div className="detail-item">
          <FaClock className="detail-icon" />
          <div className="detail-content">
            <span className="detail-label">{t("tender_details.end_date")}</span>
            <span className="detail-value">{formatDate(tender.endDate)}</span>
          </div>
        </div>
        
        {tender.state === "ACTIVE" && (
          <div className="detail-item remaining-time-item">
            <FaHourglassHalf className="detail-icon" />
            <div className="detail-content">
              <span className="detail-label">{t("tender_details.remaining_time")}</span>
              <span className="detail-value remaining-time">{remainingTime}</span>
            </div>
          </div>
        )}
      </div>

      {/* İhalenin ek açıklamaları (Mock) */}
      <div className="tender-description">
        <h4 className="section-title">{t("tender_details.description")}</h4>
        <p>{tender.declarationLimit?.description || '(eksik)'}</p>
      </div>

      <div className="tender-user-company-info">
        <h4 className="section-title">{t("tender_details.user_and_company")}</h4>
        {/* Kullanıcı ve Firma Bilgileri Bölümü */}
        <div className="info-sections-container">
          {/* Kullanıcı Bilgisi */}
          <div className="info-section">
            <div className="info-header">
              <FaUserFriends className="info-icon" />
              <span className="info-label">{t("tender_details.user_information")}</span>
            </div>
            <div className="info-body">
              <div className="info-row">
                <span className="info-name">
                  {tender.userInformation?.userInformation?.firstName} {tender.userInformation?.userInformation?.lastName}
                </span>
              </div>
              <div className="info-row">
                <OverlayTrigger
                  placement="top"
                  overlay={<Tooltip>{tender.userInformation?.userInformation?.email}</Tooltip>}
                >
                  <a href={`mailto:${tender.userInformation?.userInformation?.email}`} className="info-link">
                    <FaEnvelope className="link-icon" />
                    <span>{tender.userInformation?.userInformation?.email}</span>
                  </a>
                </OverlayTrigger>
              </div>
              <div className="info-row">
                <OverlayTrigger
                  placement="top"
                  overlay={<Tooltip>{tender.userInformation?.userInformation?.phone}</Tooltip>}
                >
                  <a href={`tel:${tender.userInformation?.userInformation?.phone}`} className="info-link">
                    <FaPhone className="link-icon" />
                    <span>{tender.userInformation?.userInformation?.phone}</span>
                  </a>
                </OverlayTrigger>
              </div>
            </div>
          </div>

          {/* Firma Bilgisi */}
          <div className="info-section">
            <div className="info-header">
              <FaBuilding className="info-icon" />
              <span className="info-label">{t("tender_details.company_information")}</span>
            </div>
            <div className="info-body">
              <div className="info-row">
                <span className="info-name">
                  {tender.userInformation?.companyInformation?.name}
                </span>
              </div>
              <div className="info-row">
                <OverlayTrigger
                  placement="top"
                  overlay={<Tooltip>
                    <div className="tooltip-title">Vergi No:</div>
                    {tender.userInformation?.companyInformation?.taxNumber}
                  </Tooltip>}
                >
                  <div className="info-text">
                    <FaIdCard className="link-icon" />
                    <span>{tender.userInformation?.companyInformation?.taxNumber}</span>
                  </div>
                </OverlayTrigger>
              </div>
              <div className="info-row">
                <OverlayTrigger
                  placement="top"
                  overlay={<Tooltip>
                    <div className="tooltip-title">Adres:</div>
                    {tender.userInformation?.companyInformation?.address}
                  </Tooltip>}
                >
                  <div className="info-text">
                    <FaMapMarkerAlt className="link-icon" />
                    <span className="address-text">{tender.userInformation?.companyInformation?.address}</span>
                  </div>
                </OverlayTrigger>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
// Özet kartını fonksiyon olarak ayır
function TenderSummaryCard({ tender, t, remainingTime, copied, handleCopyTenderId }) {
  return (
    <Card className="tender-detail-card summary-card">
      <Card.Header as="h3">
        {t("tender_details.tender_summary")}
      </Card.Header>
      <Card.Body>
        <ListGroup variant="flush" className="tender-summary-list">
          <ListGroup.Item>
            <span className="summary-label">{t("tender_details.tender_id")}</span>
            <span className="summary-value" title={tender.tenderId || ''} style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
              {formatTenderId(tender.tenderId)}
              {tender.tenderId && (
                <span
                  style={{ cursor: 'pointer', color: '#666' }}
                  title={t('common.copy') || 'Kopyala'}
                  onClick={() => handleCopyTenderId()}
                >
                  <FaRegClipboard size={16} />
                </span>
              )}
              {copied && (
                <span style={{ color: '#28a745', marginLeft: 8, fontSize: 12 }}>{t('common.copied') || 'Kopyalandı!'}</span>
              )}
            </span>
          </ListGroup.Item>
          <ListGroup.Item>
            <span className="summary-label">{t("tender_details.state")}</span>
            <span className={`summary-value status-${(tender.tenderStatus?.name || '').toLowerCase()}`}>{getTenderStatusClass(tender.tenderStatus, t).text}</span>
          </ListGroup.Item>
          <ListGroup.Item>
            <span className="summary-label">{t("tender_details.created_by")}</span>
            <span className="summary-value">{tender.userInformation?.userInformation?.username || '(eksik)'}</span>
          </ListGroup.Item>
          <ListGroup.Item>
            <span className="summary-label">{t("tender_details.company_responses")}</span>
            <span className="summary-value">{tender.companyResponses}</span>
          </ListGroup.Item>
          {tender.state === "ACTIVE" && (
            <ListGroup.Item>
              <span className="summary-label">{t("tender_details.remaining_time")}</span>
              <span className="summary-value remaining-time">
                {remainingTime}
              </span>
            </ListGroup.Item>
          )}
          {tender.state === "COMPLETED" && tender.acceptedOffer && (
            <ListGroup.Item>
              <span className="summary-label">{t("tender_details.accepted_price")}</span>
              <span className="summary-value accepted-price">
                {tender.acceptedOffer.offerAmount} TL / {t("tender_details.month")}
              </span>
            </ListGroup.Item>
          )}
        </ListGroup>
      </Card.Body>
      <Card.Footer>
        {tender.state === "ACTIVE" && (
          <>
            <Button 
              variant="success" 
              className="tender-action-btn"
              onClick={() => {}}
            >
              <FaCheckCircle /> {t("tender_details.complete_tender")}
            </Button>
            <Button 
              variant="danger" 
              className="tender-action-btn"
              onClick={() => {}}
            >
              <FaTimesCircle /> {t("tender_details.cancel_tender")}
            </Button>
          </>
        )}
        {tender.state === "COMPLETED" && (
          <Alert variant="success" className="mb-0">
            {tender.acceptedOffer 
              ? t("tender_details.completed_with_offer_message") 
              : t("tender_details.completed_message")
            }
          </Alert>
        )}
        {tender.state === "CANCELLED" && (
          <Alert variant="danger" className="mb-0">
            {t("tender_details.cancelled_message")}
          </Alert>
        )}
      </Card.Footer>
    </Card>
  );
}
// Teklifler kartını fonksiyon olarak ayır
function CompanyResponsesCard({ tender, t, handleAcceptOffer }) {
  return (
    <Card className="tender-detail-card companies-card">
      <Card.Header as="h3" style={{ padding: 0, background: 'inherit', border: 'none' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', gap: 8, width: '100%', padding: '12px 20px' }}>
          <FaBuilding className="card-header-icon" />
          <span style={{ fontWeight: 500, fontSize: 18 }}>
            {t("tender_details.company_responses")} ({tender.companyResponses})
          </span>
        </div>
      </Card.Header>
      <Card.Body>
        {tender.companyResponses > 0 ? (
          <div className="company-responses-list">
            {/* Eğer tamamlanmış ve kabul edilmiş teklif varsa */}
            {tender.state === "COMPLETED" && tender.acceptedOffer && (
              <div className="accepted-offer">
                <Alert variant="success" className="accepted-offer-alert">
                  <div className="accepted-offer-header">
                    <FaCheckCircle className="accepted-icon" />
                    <h5>{t("tender_details.accepted_offer_title")}</h5>
                  </div>
                  <div className="company-response-item accepted">
                    <div className="company-info">
                      <div className="company-logo-placeholder accepted-logo">
                        {tender.acceptedOffer.logo ? (
                          <img
                            src={tender.acceptedOffer.logo}
                            alt={tender.acceptedOffer.name}
                            className="company-logo"
                          />
                        ) : (
                          tender.acceptedOffer.name.charAt(0)
                        )}
                      </div>
                      <div className="company-details">
                        <h5 className="company-name">
                          {tender.acceptedOffer.name}
                        </h5>
                        <div className="company-location">
                          <FaMapMarkerAlt /> {tender.acceptedOffer.location}
                        </div>
                        <div className="accepted-date">
                          <FaCalendarAlt className="accepted-date-icon" /> 
                          {t("tender_details.accepted_on")}: {formatDate(tender.acceptedOffer.acceptedDate)}
                        </div>
                        {tender.acceptedOffer.rating && (
                          <div className="company-rating-container">
                            {renderRating(tender.acceptedOffer.rating)}
                            <span className="review-count">({tender.acceptedOffer.reviewCount} {t("tender_details.reviews")})</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="company-offer">
                      <div className="offer-amount accepted-amount">
                        <FaMoneyBillWave className="offer-icon" />
                        <span className="amount">
                          {tender.acceptedOffer.offerAmount} TL / {t("tender_details.month")}
                        </span>
                      </div>
                      <div className="offer-details">
                        {tender.acceptedOffer.offerDetails}
                      </div>
                      <div className="offer-actions">
                        <Button variant="primary" size="sm" className="offer-action-btn">
                          {t("tender_details.contract_details")}
                        </Button>
                      </div>
                    </div>
                  </div>
                  {/* Şirket detayları gösterimi */}
                  <div className="company-additional-info">
                    <h6>{t("tender_details.company_details")}</h6>
                    <Row className="company-stats-info">
                      <Col md={4}>
                        <div className="stats-item">
                          <div className="stat-label">{t("tender_details.tenders_participated")}</div>
                          <div className="stat-value">
                            <FaGavel className="stat-icon" /> {tender.acceptedOffer.tendersParticipated}
                          </div>
                        </div>
                      </Col>
                      <Col md={4}>
                        <div className="stats-item">
                          <div className="stat-label">{t("tender_details.tenders_won")}</div>
                          <div className="stat-value">
                            <FaMedal className="stat-icon win" /> {tender.acceptedOffer.tendersWon}
                          </div>
                        </div>
                      </Col>
                      <Col md={4}>
                        <div className="stats-item">
                          <div className="stat-label">{t("tender_details.company_founded")}</div>
                          <div className="stat-value">
                            <FaBuilding className="stat-icon" /> {tender.acceptedOffer.foundedYear}
                          </div>
                        </div>
                      </Col>
                    </Row>
                    <Row className="company-contact-info">
                      <Col md={4}>
                        <div className="contact-item">
                          <FaPhone className="contact-icon" />
                          <a href={`tel:${tender.acceptedOffer.phone}`}>{tender.acceptedOffer.phone}</a>
                        </div>
                      </Col>
                      <Col md={4}>
                        <div className="contact-item">
                          <FaEnvelope className="contact-icon" />
                          <a href={`mailto:${tender.acceptedOffer.email}`}>{tender.acceptedOffer.email}</a>
                        </div>
                      </Col>
                      <Col md={4}>
                        <div className="contact-item">
                          <FaGlobe className="contact-icon" />
                          <a href={`https://${tender.acceptedOffer.website}`} target="_blank" rel="noopener noreferrer">
                            {tender.acceptedOffer.website}
                          </a>
                        </div>
                      </Col>
                    </Row>
                    <div className="company-description">
                      <p>{tender.acceptedOffer.description}</p>
                    </div>
                  </div>
                </Alert>
              </div>
            )}
            {/* Diğer teklifler listesi */}
            {(tender.state !== "COMPLETED" || !tender.acceptedOffer) && 
              Array.from({ length: tender.companyResponses }).map((_, index) => (
                <div className="company-response-item" key={index}>
                  <div className="company-info">
                    <div className="company-logo-placeholder">
                      {String.fromCharCode(65 + index)}
                    </div>
                    <div className="company-details">
                      <h5 className="company-name">
                        {t("tender_details.mock_company_name")} {index + 1}
                      </h5>
                      <div className="company-location">
                        <FaMapMarkerAlt /> {t("tender_details.mock_location")}
                      </div>
                    </div>
                  </div>
                  <div className="company-offer">
                    <div className="offer-amount">
                      <FaMoneyBillWave className="offer-icon" />
                      <span className="amount">
                        {(Math.random() * 1000 + 2000).toFixed(0)} TL / {t("tender_details.month")}
                      </span>
                    </div>
                    <div className="offer-actions">
                      <Button variant="outline-primary" size="sm" className="offer-action-btn">
                        {t("tender_details.view_details")}
                      </Button>
                      {/* Sadece aktif ihalelerde teklif kabul etme butonu göster */}
                      {tender.state === "ACTIVE" && (
                        <Button 
                          variant="success" 
                          size="sm" 
                          className="offer-action-btn"
                          onClick={() => handleAcceptOffer(index)}
                        >
                          <FaCheckCircle /> {t("tender_details.accept_offer")}
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            }
          </div>
        ) : (
          <Alert variant="info">
            {t("tender_details.no_company_responses")}
          </Alert>
        )}
      </Card.Body>
    </Card>
  );
}

const TenderDetailsPage = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const tenderId = location.state?.tenderId;
  const navigate = useNavigate();
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  
  const [tender, setTender] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [remainingTime, setRemainingTime] = useState(null);
  const [remainingTimeObj, setRemainingTimeObj] = useState(null);
  const [copied, setCopied] = useState(false);

  // Giriş yapmamış kullanıcıları giriş sayfasına yönlendir
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  // İhale detaylarını yükle
  useEffect(() => {
    const loadTenderDetails = async () => {
      if (!tenderId) {
        setError('İhale ID bilgisi bulunamadı. Lütfen tekrar deneyin.');
        setLoading(false);
        return;
      }

      try {
        const response = await tenderService.getTenderDetails(tenderId);
        console.log('API Yanıtı:', response); // Debug için API yanıtını logla

        if (response.data && response.data.result) {
          setTender(response.data.result);
          setError(null);
        } else {
          setError("İhale detayları bulunamadı.");
        }
      } catch (err) {
        console.error('API Hatası:', err); // Hata detayını logla
        setError(err.message || "İhale detayları yüklenirken bir hata oluştu.");
      } finally {
        setLoading(false);
      }
    };

    loadTenderDetails();
  }, [tenderId]);

  // Kalan süreyi hesapla (canlı sayaç)
  useEffect(() => {
    // Debug için tender durumunu kontrol et
    console.log('Tender Durumu:', {
      tenderVar: !!tender,
      startDate: tender?.startDate,
      endDate: tender?.endDate,
      status: tender?.tenderStatus?.name
    });

    // DRAFT ve LIVE durumlarını aktif olarak kabul et
    const isActiveStatus = tender?.tenderStatus?.name === "LIVE" || tender?.tenderStatus?.name === "DRAFT";

    if (!tender || !tender.endDate || !tender.startDate || !tender.tenderStatus || !isActiveStatus) {
      console.log('Kalan süre hesaplaması yapılamıyor:', {
        tender: tender,
        endDate: tender?.endDate,
        startDate: tender?.startDate,
        status: tender?.tenderStatus?.name,
        isActiveStatus
      });
      return;
    }
    
    const calculateRemainingTime = () => {
      const now = new Date();
      const startDate = new Date(tender.startDate);
      const endDate = new Date(tender.endDate);

      console.log('Tarih Hesaplaması:', {
        şuAn: now.toLocaleString(),
        başlangıç: startDate.toLocaleString(),
        bitiş: endDate.toLocaleString(),
        başlamadıMı: now < startDate,
        bittiMi: now > endDate
      });

      // Eğer başlangıç tarihi henüz gelmediyse
      if (now < startDate) {
        setRemainingTime(t("tender_details.not_started"));
        setRemainingTimeObj(null);
        return;
      }

      // Eğer bitiş tarihi geçtiyse
      if (now > endDate) {
        setRemainingTime(t("tender_details.expired"));
        setRemainingTimeObj(null);
        return;
      }

      // Kalan süreyi hesapla
      const diff = endDate.getTime() - now.getTime();
      
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      console.log('Hesaplanan Kalan Süre:', { days, hours, minutes, seconds });

      let remainingTimeText = '';
      if (days > 0) {
        remainingTimeText = `${days} ${t("tender_details.days")} ${hours} ${t("tender_details.hours")}`;
      } else if (hours > 0) {
        remainingTimeText = `${hours} ${t("tender_details.hours")} ${minutes} ${t("tender_details.minutes")}`;
      } else {
        remainingTimeText = `${minutes} ${t("tender_details.minutes")} ${seconds} ${t("tender_details.seconds")}`;
      }

      setRemainingTime(remainingTimeText);
      setRemainingTimeObj({ days, hours, minutes, seconds });
    };

    calculateRemainingTime();
    const timer = setInterval(calculateRemainingTime, 1000);
    return () => clearInterval(timer);
  }, [tender, t]);

  // İhale durumuna göre CSS sınıfı ve metni belirle
  const getTenderStatusClass = (status) => {
    if (!status) return { className: '', text: '(eksik)', variant: 'secondary' };
    switch (status.name) {
      case "LIVE":
        return {
          className: "tender-status-active",
          text: t("tenders_page.active"),
          variant: "success"
        };
      case "COMPLETED":
        return {
          className: "tender-status-completed",
          text: t("tenders_page.completed"),
          variant: "primary"
        };
      case "CANCELLED":
        return {
          className: "tender-status-cancelled",
          text: t("tenders_page.cancelled"),
          variant: "danger"
        };
      default:
        return {
          className: "",
          text: status.statusName || status.name,
          variant: "secondary"
        };
    }
  };

  // Tarih formatı
  const formatDate = (dateString) => {
    if (!dateString) return '(eksik)';
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('tr-TR', options);
  };

  // Geri dön butonu işleyicisi
  const handleGoBack = () => {
    navigate(-1); // Önceki sayfaya dön
  };

  // Teklifi kabul etme işleyicisi
  const handleAcceptOffer = (companyIndex) => {
    if (!tender || tender.state !== "ACTIVE") return;
    
    // Gerçek uygulamada API çağrısı yapılacak
    alert(`${companyIndex + 1} numaralı şirketin teklifi kabul edildi.`);
  };

  // İhaleyi tamamla işleyicisi
  const handleCompleteTender = () => {
    if (!tender || tender.state !== "ACTIVE") return;
    
    // Gerçek uygulamada API çağrısı yapılacak
    alert("İhale tamamlanacak. Onaylıyor musunuz?");
  };

  // İhaleyi iptal et işleyicisi
  const handleCancelTender = () => {
    if (!tender || tender.state !== "ACTIVE") return;
    
    // Gerçek uygulamada API çağrısı yapılacak
    alert("İhale iptal edilecek. Onaylıyor musunuz?");
  };

  // Yıldız ratingini göster
  const renderRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={`star-${i}`} className="star-icon filled" />);
    }

    if (hasHalfStar) {
      stars.push(<FaStar key="half-star" className="star-icon half" />);
    }

    const emptyStars = 5 - stars.length;
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<FaStar key={`empty-star-${i}`} className="star-icon empty" />);
    }

    return <div className="company-rating">{stars} <span className="rating-value">({rating})</span></div>;
  };

  // İhale No'yu kısaltan yardımcı fonksiyon
  const formatTenderId = (id) => {
    if (!id) return '(eksik)';
    if (id.length <= 10) return id;
    return `${id.slice(0, 8)}...`;
  };

  // Kopyalama fonksiyonu
  const handleCopyTenderId = () => {
    if (tender?.tenderId) {
      navigator.clipboard.writeText(tender.tenderId);
      setCopied(true);
      setTimeout(() => setCopied(false), 1200);
    }
  };

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("tender_details.title")}
        pagesub={t("tender_details.subtitle")}
      />
      
      <div className="tender-details-area">
        <Container>
          {/* Geri dön butonu */}
          <div className="back-button-container">
            <Button 
              variant="outline-secondary" 
              onClick={handleGoBack}
              className="back-button"
            >
              <FaArrowLeft /> {t("tender_details.back")}
            </Button>
          </div>

          {loading ? (
            <div className="tender-loading">
              <div className="spinner-border text-danger" role="status">
                <span className="visually-hidden">{t("tender_details.loading")}</span>
              </div>
            </div>
          ) : error ? (
            <Alert variant="danger">{error}</Alert>
          ) : tender ? (
            <Row>
              {/* Sol kolon - İhale genel bilgileri */}
              <Col lg={8} md={7} sm={12}>
                <Card className="tender-detail-card main-info-card compact">
                  <Card.Header style={{ padding: 0, background: 'inherit', border: 'none' }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', padding: '16px 20px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                        <FaCar className="info-icon" size={22} style={{ color: 'var(--primary-color)' }} />
                        <span style={{ fontWeight: 600, fontSize: 20 }}>
                          {tender.vehicleBrand || '(eksik)'} / {tender.vehicleModel || '(eksik)'}
                        </span>
                        <Badge 
                          bg={getTenderStatusClass(tender.tenderStatus).variant}
                          className="tender-status-badge"
                          style={{ fontSize: 14, padding: '6px 16px', marginLeft: 8 }}
                        >
                          {getTenderStatusClass(tender.tenderStatus).text}
                        </Badge>
                      </div>
                      <span className="tender-id" style={{ display: 'flex', alignItems: 'center', gap: 6, fontSize: 14, color: '#666', cursor: 'pointer' }} title={tender.tenderId || ''} onClick={handleCopyTenderId}>
                        {formatTenderId(tender.tenderId)}
                        <FaRegClipboard size={16} />
                        {copied && (
                          <span style={{ color: '#28a745', marginLeft: 8, fontSize: 12 }}>{t('common.copied') || 'Kopyalandı!'}</span>
                        )}
                      </span>
                    </div>
                  </Card.Header>
                  <Card.Body style={{ padding: 20 }}>
                    {/* Tarihler ve kalan süre en üstte */}
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: 24, 
                      marginBottom: 24, 
                      background: '#f8f9fa',
                      padding: '12px 16px',
                      borderRadius: 8,
                      border: '1px solid #e9ecef'
                    }}>
                      <div style={{ 
                        display: 'flex', 
                        alignItems: 'center', 
                        gap: 16,
                        flex: 1,
                        borderRight: '1px solid #dee2e6',
                        paddingRight: 16
                      }}>
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                          <span style={{ fontSize: 13, color: '#6c757d', marginBottom: 4 }}>Başlangıç</span>
                          <span style={{ fontSize: 15, fontWeight: 500, color: '#212529', display: 'flex', alignItems: 'center', gap: 6 }}>
                            <FaCalendarAlt style={{ color: 'var(--primary-color)', fontSize: 14 }} />
                            {formatDate(tender.startDate)}
                          </span>
                        </div>
                        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                          <span style={{ fontSize: 13, color: '#6c757d', marginBottom: 4 }}>Bitiş</span>
                          <span style={{ fontSize: 15, fontWeight: 500, color: '#212529', display: 'flex', alignItems: 'center', gap: 6 }}>
                            <FaCalendarAlt style={{ color: 'var(--primary-color)', fontSize: 14 }} />
                            {formatDate(tender.endDate)}
                          </span>
                        </div>
                      </div>
                      <div style={{ 
                        display: 'flex', 
                        flexDirection: 'column', 
                        alignItems: 'flex-start',
                        minWidth: 180
                      }}>
                        <span style={{ fontSize: 13, color: '#6c757d', marginBottom: 4 }}>Kalan Süre</span>
                        <span style={{ 
                          fontSize: 15, 
                          fontWeight: 600, 
                          color: 'var(--primary-color)', 
                          display: 'flex', 
                          alignItems: 'center', 
                          gap: 6,
                          background: '#fff',
                          padding: '6px 12px',
                          borderRadius: 6,
                          border: '1px solid #e9ecef'
                        }}>
                          <FaHourglassHalf style={{ fontSize: 14 }} />
                          {remainingTimeObj ? (
                            <>
                              {remainingTimeObj.days > 0 && <span>{remainingTimeObj.days}g </span>}
                              <span>{remainingTimeObj.hours.toString().padStart(2, '0')}s </span>
                              <span>{remainingTimeObj.minutes.toString().padStart(2, '0')}d </span>
                              <span>{remainingTimeObj.seconds.toString().padStart(2, '0')}sn</span>
                            </>
                          ) : (
                            <span>{remainingTime || t('tender_details.expired')}</span>
                          )}
                        </span>
                      </div>
                    </div>

                    {/* 3 sütunlu grid ile özet bilgiler */}
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '8px 24px', marginBottom: 18 }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaMapMarkerAlt style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Şehir:</strong> {tender.city?.name || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaChartLine style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>KM Limiti:</strong> {tender.kmLimit?.limit || '(eksik)'} km</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaUserFriends style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Kullanım:</strong> {tender.usageStatus?.statusName || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaBuilding style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Gövde:</strong> {tender.vehicleBodyType?.typeName || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaCalendarAlt style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Kira Süresi:</strong> {tender.rentalPeriod?.period || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaCogs style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Vites:</strong> {tender.gearType?.typeName || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaGasPump style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Yakıt:</strong> {tender.fuelType?.typeName || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaSnowflake style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Kış Lastiği:</strong> {tender.hasWinterTire ? t('common.yes') : t('common.no')}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaCheckCircle style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Bakım:</strong> {tender.maintenanceResponsible?.responsible || '(eksik)'}</span></div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}><FaMedal style={{ color: 'var(--primary-color)', fontSize: 16 }} /><span><strong>Beyan Limiti:</strong> {tender.declarationLimit?.limitName || '(eksik)'}</span></div>
                    </div>

                    {/* Kullanıcı ve Firma Bilgileri Bölümü */}
                    <div className="info-sections-container">
                      {/* Kullanıcı Bilgisi */}
                      <div className="info-section">
                        <div className="info-header">
                          <FaUserFriends className="info-icon" />
                          <span className="info-label">{t("tender_details.user_information")}</span>
                        </div>
                        <div className="info-body">
                          <div className="info-row">
                            <span className="info-name">
                              {tender.userInformation?.userInformation?.firstName} {tender.userInformation?.userInformation?.lastName}
                            </span>
                          </div>
                          <div className="info-row">
                            <OverlayTrigger
                              placement="top"
                              overlay={<Tooltip>{tender.userInformation?.userInformation?.email}</Tooltip>}
                            >
                              <a href={`mailto:${tender.userInformation?.userInformation?.email}`} className="info-link">
                                <FaEnvelope className="link-icon" />
                                <span>{tender.userInformation?.userInformation?.email}</span>
                              </a>
                            </OverlayTrigger>
                          </div>
                          <div className="info-row">
                            <OverlayTrigger
                              placement="top"
                              overlay={<Tooltip>{tender.userInformation?.userInformation?.phone}</Tooltip>}
                            >
                              <a href={`tel:${tender.userInformation?.userInformation?.phone}`} className="info-link">
                                <FaPhone className="link-icon" />
                                <span>{tender.userInformation?.userInformation?.phone}</span>
                              </a>
                            </OverlayTrigger>
                          </div>
                        </div>
                      </div>

                      {/* Firma Bilgisi */}
                      <div className="info-section">
                        <div className="info-header">
                          <FaBuilding className="info-icon" />
                          <span className="info-label">{t("tender_details.company_information")}</span>
                        </div>
                        <div className="info-body">
                          <div className="info-row">
                            <span className="info-name">
                              {tender.userInformation?.companyInformation?.name}
                            </span>
                          </div>
                          <div className="info-row">
                            <OverlayTrigger
                              placement="top"
                              overlay={<Tooltip>
                                <div className="tooltip-title">Vergi No:</div>
                                {tender.userInformation?.companyInformation?.taxNumber}
                              </Tooltip>}
                            >
                              <div className="info-text">
                                <FaIdCard className="link-icon" />
                                <span>{tender.userInformation?.companyInformation?.taxNumber}</span>
                              </div>
                            </OverlayTrigger>
                          </div>
                          <div className="info-row">
                            <OverlayTrigger
                              placement="top"
                              overlay={<Tooltip>
                                <div className="tooltip-title">Adres:</div>
                                {tender.userInformation?.companyInformation?.address}
                              </Tooltip>}
                            >
                              <div className="info-text">
                                <FaMapMarkerAlt className="link-icon" />
                                <span className="address-text">{tender.userInformation?.companyInformation?.address}</span>
                              </div>
                            </OverlayTrigger>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card.Body>
                  <Card.Footer style={{ display: 'flex', gap: 12, justifyContent: 'flex-end', background: 'inherit', border: 'none', padding: '16px 20px' }}>
                    {tender.state === "ACTIVE" && (
                      <>
                        <Button 
                          variant="success" 
                          className="tender-action-btn"
                          onClick={handleCompleteTender}
                        >
                          <FaCheckCircle /> {t("tender_details.complete_tender")}
                        </Button>
                        <Button 
                          variant="danger" 
                          className="tender-action-btn"
                          onClick={handleCancelTender}
                        >
                          <FaTimesCircle /> {t("tender_details.cancel_tender")}
                        </Button>
                      </>
                    )}
                    {tender.state === "COMPLETED" && (
                      <Alert variant="success" className="mb-0">
                        {tender.acceptedOffer 
                          ? t("tender_details.completed_with_offer_message") 
                          : t("tender_details.completed_message")
                        }
                      </Alert>
                    )}
                    {tender.state === "CANCELLED" && (
                      <Alert variant="danger" className="mb-0">
                        {t("tender_details.cancelled_message")}
                      </Alert>
                    )}
                  </Card.Footer>
                </Card>

                {/* Teklif veren şirketler bilgileri */}
                <Card className="tender-detail-card companies-card">
                  <Card.Header as="h3" style={{ padding: 0, background: 'inherit', border: 'none' }}>
                    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-start', gap: 8, width: '100%', padding: '12px 20px' }}>
                      <FaBuilding className="card-header-icon" />
                      <span style={{ fontWeight: 500, fontSize: 18 }}>
                        {t("tender_details.company_responses")} ({tender.companyResponses})
                      </span>
                    </div>
                  </Card.Header>
                  <Card.Body>
                    {tender.companyResponses > 0 ? (
                      <div className="company-responses-list">
                        {/* Eğer tamamlanmış ve kabul edilmiş teklif varsa */}
                        {tender.state === "COMPLETED" && tender.acceptedOffer && (
                          <div className="accepted-offer">
                            <Alert variant="success" className="accepted-offer-alert">
                              <div className="accepted-offer-header">
                                <FaCheckCircle className="accepted-icon" />
                                <h5>{t("tender_details.accepted_offer_title")}</h5>
                              </div>
                              <div className="company-response-item accepted">
                                <div className="company-info">
                                  <div className="company-logo-placeholder accepted-logo">
                                    {tender.acceptedOffer.logo ? (
                                      <img
                                        src={tender.acceptedOffer.logo}
                                        alt={tender.acceptedOffer.name}
                                        className="company-logo"
                                      />
                                    ) : (
                                      tender.acceptedOffer.name.charAt(0)
                                    )}
                                  </div>
                                  <div className="company-details">
                                    <h5 className="company-name">
                                      {tender.acceptedOffer.name}
                                    </h5>
                                    <div className="company-location">
                                      <FaMapMarkerAlt /> {tender.acceptedOffer.location}
                                    </div>
                                    <div className="accepted-date">
                                      <FaCalendarAlt className="accepted-date-icon" /> 
                                      {t("tender_details.accepted_on")}: {formatDate(tender.acceptedOffer.acceptedDate)}
                                    </div>
                                    {tender.acceptedOffer.rating && (
                                      <div className="company-rating-container">
                                        {renderRating(tender.acceptedOffer.rating)}
                                        <span className="review-count">({tender.acceptedOffer.reviewCount} {t("tender_details.reviews")})</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                                
                                <div className="company-offer">
                                  <div className="offer-amount accepted-amount">
                                    <FaMoneyBillWave className="offer-icon" />
                                    <span className="amount">
                                      {tender.acceptedOffer.offerAmount} TL / {t("tender_details.month")}
                                    </span>
                                  </div>
                                  <div className="offer-details">
                                    {tender.acceptedOffer.offerDetails}
                                  </div>
                                  <div className="offer-actions">
                                    <Button variant="primary" size="sm" className="offer-action-btn">
                                      {t("tender_details.contract_details")}
                                    </Button>
                                  </div>
                                </div>
                              </div>

                              {/* Şirket detayları gösterimi */}
                              <div className="company-additional-info">
                                <h6>{t("tender_details.company_details")}</h6>
                                <Row className="company-stats-info">
                                  <Col md={4}>
                                    <div className="stats-item">
                                      <div className="stat-label">{t("tender_details.tenders_participated")}</div>
                                      <div className="stat-value">
                                        <FaGavel className="stat-icon" /> {tender.acceptedOffer.tendersParticipated}
                                      </div>
                                    </div>
                                  </Col>
                                  <Col md={4}>
                                    <div className="stats-item">
                                      <div className="stat-label">{t("tender_details.tenders_won")}</div>
                                      <div className="stat-value">
                                        <FaMedal className="stat-icon win" /> {tender.acceptedOffer.tendersWon}
                                      </div>
                                    </div>
                                  </Col>
                                  <Col md={4}>
                                    <div className="stats-item">
                                      <div className="stat-label">{t("tender_details.company_founded")}</div>
                                      <div className="stat-value">
                                        <FaBuilding className="stat-icon" /> {tender.acceptedOffer.foundedYear}
                                      </div>
                                    </div>
                                  </Col>
                                </Row>
                                <Row className="company-contact-info">
                                  <Col md={4}>
                                    <div className="contact-item">
                                      <FaPhone className="contact-icon" />
                                      <a href={`tel:${tender.acceptedOffer.phone}`}>{tender.acceptedOffer.phone}</a>
                                    </div>
                                  </Col>
                                  <Col md={4}>
                                    <div className="contact-item">
                                      <FaEnvelope className="contact-icon" />
                                      <a href={`mailto:${tender.acceptedOffer.email}`}>{tender.acceptedOffer.email}</a>
                                    </div>
                                  </Col>
                                  <Col md={4}>
                                    <div className="contact-item">
                                      <FaGlobe className="contact-icon" />
                                      <a href={`https://${tender.acceptedOffer.website}`} target="_blank" rel="noopener noreferrer">
                                        {tender.acceptedOffer.website}
                                      </a>
                                    </div>
                                  </Col>
                                </Row>
                                <div className="company-description">
                                  <p>{tender.acceptedOffer.description}</p>
                                </div>
                              </div>
                            </Alert>
                          </div>
                        )}

                        {/* Diğer teklifler listesi */}
                        {(tender.state !== "COMPLETED" || !tender.acceptedOffer) && 
                          Array.from({ length: tender.companyResponses }).map((_, index) => (
                            <div className="company-response-item" key={index}>
                              <div className="company-info">
                                <div className="company-logo-placeholder">
                                  {String.fromCharCode(65 + index)}
                                </div>
                                <div className="company-details">
                                  <h5 className="company-name">
                                    {t("tender_details.mock_company_name")} {index + 1}
                                  </h5>
                                  <div className="company-location">
                                    <FaMapMarkerAlt /> {t("tender_details.mock_location")}
                                  </div>
                                </div>
                              </div>
                              
                              <div className="company-offer">
                                <div className="offer-amount">
                                  <FaMoneyBillWave className="offer-icon" />
                                  <span className="amount">
                                    {(Math.random() * 1000 + 2000).toFixed(0)} TL / {t("tender_details.month")}
                                  </span>
                                </div>
                                <div className="offer-actions">
                                  <Button variant="outline-primary" size="sm" className="offer-action-btn">
                                    {t("tender_details.view_details")}
                                  </Button>
                                  
                                  {/* Sadece aktif ihalelerde teklif kabul etme butonu göster */}
                                  {tender.state === "ACTIVE" && (
                                    <Button 
                                      variant="success" 
                                      size="sm" 
                                      className="offer-action-btn"
                                      onClick={() => handleAcceptOffer(index)}
                                    >
                                      <FaCheckCircle /> {t("tender_details.accept_offer")}
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))
                        }

                        {/* Aktif ihaleler için süreç kartı */}
                        {tender.state === "ACTIVE" && tender.companyResponses > 1 && (
                          <div className="offer-comparison">
                            <div className="comparison-header">
                              <FaChartLine className="comparison-icon" />
                              <h5>{t("tender_details.offer_comparison")}</h5>
                            </div>
                            <p>{t("tender_details.comparison_text")}</p>
                            <div className="price-range">
                              <div className="price-range-item">
                                <span className="range-label">{t("tender_details.lowest_price")}</span>
                                <span className="range-value">2150 TL</span>
                              </div>
                              <div className="price-range-item">
                                <span className="range-label">{t("tender_details.highest_price")}</span>
                                <span className="range-value">2950 TL</span>
                              </div>
                              <div className="price-range-item">
                                <span className="range-label">{t("tender_details.average_price")}</span>
                                <span className="range-value">2550 TL</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <Alert variant="info">
                        {t("tender_details.no_company_responses")}
                      </Alert>
                    )}
                  </Card.Body>
                </Card>
              </Col>

              {/* Sağ kolon - İhale özeti ve durum bilgisi */}
              <Col lg={4} md={5} sm={12}>
                <div className="sticky-sidebar">
                  <Card className="tender-detail-card summary-card">
                    <Card.Header as="h3">
                      {t("tender_details.tender_summary")}
                    </Card.Header>
                    <Card.Body>
                      <ListGroup variant="flush" className="tender-summary-list">
                        <ListGroup.Item>
                          <span className="summary-label">{t("tender_details.tender_id")}</span>
                          <span className="summary-value" title={tender.tenderId || ''} style={{ display: 'inline-flex', alignItems: 'center', gap: 6 }}>
                            {formatTenderId(tender.tenderId)}
                            {tender.tenderId && (
                              <span
                                style={{ cursor: 'pointer', color: '#666' }}
                                title={t('common.copy') || 'Kopyala'}
                                onClick={() => handleCopyTenderId()}
                              >
                                <FaRegClipboard size={16} />
                              </span>
                            )}
                            {copied && (
                              <span style={{ color: '#28a745', marginLeft: 8, fontSize: 12 }}>{t('common.copied') || 'Kopyalandı!'}</span>
                            )}
                          </span>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <span className="summary-label">{t("tender_details.state")}</span>
                          <span className={`summary-value status-${(tender.tenderStatus?.name || '').toLowerCase()}`}>{getTenderStatusClass(tender.tenderStatus).text}</span>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <span className="summary-label">{t("tender_details.created_by")}</span>
                          <span className="summary-value">{tender.userInformation?.userInformation?.username || '(eksik)'}</span>
                        </ListGroup.Item>
                        <ListGroup.Item>
                          <span className="summary-label">{t("tender_details.company_responses")}</span>
                          <span className="summary-value">{tender.companyResponses}</span>
                        </ListGroup.Item>
                        
                        {tender.state === "ACTIVE" && (
                          <ListGroup.Item>
                            <span className="summary-label">{t("tender_details.remaining_time")}</span>
                            <span className="summary-value remaining-time">
                              {remainingTime}
                            </span>
                          </ListGroup.Item>
                        )}
                        
                        {tender.state === "COMPLETED" && tender.acceptedOffer && (
                          <ListGroup.Item>
                            <span className="summary-label">{t("tender_details.accepted_price")}</span>
                            <span className="summary-value accepted-price">
                              {tender.acceptedOffer.offerAmount} TL / {t("tender_details.month")}
                            </span>
                          </ListGroup.Item>
                        )}
                      </ListGroup>
                    </Card.Body>
                    <Card.Footer>
                      {tender.state === "ACTIVE" && (
                        <>
                          <Button 
                            variant="success" 
                            className="tender-action-btn"
                            onClick={handleCompleteTender}
                          >
                            <FaCheckCircle /> {t("tender_details.complete_tender")}
                          </Button>
                          <Button 
                            variant="danger" 
                            className="tender-action-btn"
                            onClick={handleCancelTender}
                          >
                            <FaTimesCircle /> {t("tender_details.cancel_tender")}
                          </Button>
                        </>
                      )}
                      
                      {tender.state === "COMPLETED" && (
                        <Alert variant="success" className="mb-0">
                          {tender.acceptedOffer 
                            ? t("tender_details.completed_with_offer_message") 
                            : t("tender_details.completed_message")
                          }
                        </Alert>
                      )}
                      
                      {tender.state === "CANCELLED" && (
                        <Alert variant="danger" className="mb-0">
                          {t("tender_details.cancelled_message")}
                        </Alert>
                      )}
                    </Card.Footer>
                  </Card>
                  
                  {/* İşlem Geçmişi Paneli */}
                  <HistoryPanel history={tender.histories || []} t={t} />
                  
                  {/* Aktif ihaleler için süreç kartı */}
                  {tender.state === "ACTIVE" && (
                    <Card className="tender-detail-card process-card">
                      <Card.Header as="h3">
                        {t("tender_details.tender_process")}
                      </Card.Header>
                      <Card.Body>
                        <div className="process-timeline">
                          <div className="process-step completed">
                            <div className="step-indicator">1</div>
                            <div className="step-content">
                              <h5>{t("tender_details.process_step1_title")}</h5>
                              <p>{t("tender_details.process_step1_desc")}</p>
                            </div>
                          </div>
                          <div className="process-step active">
                            <div className="step-indicator">2</div>
                            <div className="step-content">
                              <h5>{t("tender_details.process_step2_title")}</h5>
                              <p>{t("tender_details.process_step2_desc")}</p>
                            </div>
                          </div>
                          <div className="process-step">
                            <div className="step-indicator">3</div>
                            <div className="step-content">
                              <h5>{t("tender_details.process_step3_title")}</h5>
                              <p>{t("tender_details.process_step3_desc")}</p>
                            </div>
                          </div>
                        </div>
                      </Card.Body>
                    </Card>
                  )}
                </div>
              </Col>
            </Row>
          ) : (
            <Alert variant="warning">
              {t("tender_details.tender_not_found")}
            </Alert>
          )}
        </Container>
      </div>
    </Fragment>
  );
};

export default TenderDetailsPage; 