import React, { Fragment } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import i18next from "i18next";
import { Col, Container, Row, Dropdown } from "react-bootstrap";
import {
  FaPhoneAlt,
  FaSignInAlt,
  FaUserAlt,
  FaSearch,
  FaGlobe,
  FaSignOutAlt,
  FaUserCircle,
  FaCar,
  FaCarSide
} from "react-icons/fa";
import { logoutUser } from "../../redux/slices/authSlice";
import { setLanguage } from "../../redux/slices/uiSlice";
import { USER_ROLES } from "../../constants/userRoles";
import MobileMenu from "../../components/MobileMenu";
import Logo from "../../components/Logo";

import "flag-icon-css/css/flag-icons.min.css";
import "./style.css";

const languages = [
  {
    code: "en",
    name: "English",
    country_code: "gb",
  },
  {
    code: "tr",
    name: "Türkçe",
    country_code: "tr",
  },
];

const Header = () => {
  const SubmitHandler = (e) => {
    e.preventDefault();
  };

  const onClick = (e) => {
    e.preventDefault();
  };

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const currentLanguage = useSelector((state) => state.ui.language);

  // Redux store'dan auth state'ini alıyoruz
  const { isAuthenticated, user } = useSelector((state) => state.auth);

  // Dil değişimi için handler fonksiyonu
  const handleLanguageChange = (code) => {
    // Önce localStorage'ı güncelle
    localStorage.setItem('language', code);
    // Sonra Redux store'u güncelle
    dispatch(setLanguage(code));
    // i18next'i güncelle
    i18next.changeLanguage(code);
    // HTML lang attribute'unu güncelle
    document.documentElement.lang = code;
    // Sayfayı yenile
    window.location.reload();
  };

  // Çıkış işlemi için handler
  const handleLogout = () => {
    dispatch(logoutUser());
    navigate('/');
  };

  return (
    <Fragment>
      <section className="gauto-header-top-area">
        <Container>
          <Row>
            <Col md={6}>
              <div className="header-top-left">
                <p>
                  {t("need_help")} <FaPhoneAlt /> {t("call")}: +321 123 45 978
                </p>
              </div>
            </Col>
            <Col md={6}>
              <div className="header-top-right">
                {isAuthenticated ? (
                  // Kullanıcı giriş yapmışsa kullanıcı adını ve çıkış butonunu göster
                  <div className="header-dropdowns">
                    <Dropdown>
                      <Dropdown.Toggle variant="success" className="user-dropdown">
                        <FaUserCircle /> {user?.name || t("header-navigation.user")}
                      </Dropdown.Toggle>
                      <Dropdown.Menu>
                        <Dropdown.Item as={Link} to="/profile">
                          <FaUserAlt /> {t("header-navigation.profile")}
                        </Dropdown.Item>
                        <Dropdown.Item as={Link} to="/my-bookings">
                          <FaCar /> {t("header-navigation.bookings")}
                        </Dropdown.Item>
                        <Dropdown.Divider />
                        <Dropdown.Item onClick={handleLogout}>
                          <FaSignOutAlt /> {t("header-navigation.logout")}
                        </Dropdown.Item>
                      </Dropdown.Menu>
                    </Dropdown>
                    <Dropdown>
                      <Dropdown.Toggle variant="success" id="dropdown-basic">
                        <FaGlobe /> {t("language")}
                      </Dropdown.Toggle>

                      <Dropdown.Menu>
                        {languages.map(({ code, name, country_code }) => (
                          <Dropdown.Item
                            eventKey={name}
                            key={country_code}
                            to="/"
                            onClick={() => handleLanguageChange(code)}
                          >
                            <span
                              className={`flag-icon flag-icon-${country_code}`}
                            ></span>{" "}
                            {name}
                          </Dropdown.Item>
                        ))}
                      </Dropdown.Menu>
                    </Dropdown>
                  </div>
                ) : (
                  // Kullanıcı giriş yapmamışsa login ve register butonlarını göster
                  <>
                    <div className="header-dropdowns">
                      <Link to="/login">
                        <FaSignInAlt />
                        {t("header-navigation.login")}
                      </Link>
                      <Link to="/register">
                        <FaUserAlt />
                        {t("header-navigation.register")}
                      </Link>
                      <Dropdown>
                        <Dropdown.Toggle variant="success" id="dropdown-basic">
                          <FaGlobe /> {t("language")}
                        </Dropdown.Toggle>

                        <Dropdown.Menu>
                          {languages.map(({ code, name, country_code }) => (
                            <Dropdown.Item
                              eventKey={name}
                              key={country_code}
                              to="/"
                              onClick={() => handleLanguageChange(code)}
                            >
                              <span
                                className={`flag-icon flag-icon-${country_code}`}
                              ></span>{" "}
                              {name}
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </>
                )}
              </div>
            </Col>
          </Row>
        </Container>
      </section>
      <header className="gauto-main-header-area">
        <Container>
          <Row>
            <Col md={3}>
              <div className="site-logo">
                <Logo />
              </div>
            </Col>
            <Col lg={6} sm={9}>
              <div className="header-promo">
                <div className="single-header-promo">
                  <div className="header-promo-icon">
                    <FaGlobe className="promo-icon" />
                  </div>
                  <div className="header-promo-info">
                    <h3>{t("header.promo.locations")}</h3>
                    <p>{t("header.promo.nationwide")}</p>
                  </div>
                </div>
                <div className="single-header-promo">
                  <div className="header-promo-icon">
                    <FaCarSide className="promo-icon" />
                  </div>
                  <div className="header-promo-info">
                    <h3>{t("header.promo.long_term")}</h3>
                    <p>{t("header.promo.flexible_plans")}</p>
                  </div>
                </div>
              </div>
            </Col>
            <div className="col-lg-3">
              <div className="header-action">
                <Link to="/contact">
                  <FaPhoneAlt /> {t("request_call")}
                </Link>
              </div>
            </div>
          </Row>
        </Container>
      </header>
      <section className="gauto-mainmenu-area">
        <Container>
          <Row>
            <Col lg={9}>
              <MobileMenu />
              <div className="mainmenu">
                <nav>
                  <ul id="gauto_navigation">
                    <li>
                      <Link to="/home">
                        {t("header-navigation.home")}
                      </Link>
                    </li>
                    <li>
                      <Link to="/about">
                        {t("header-navigation.about")}
                      </Link>
                    </li>

                    {isAuthenticated && user?.roleId === USER_ROLES.CUSTOMER.id && (
                      <li>
                        <Link to="/my-tenders">
                          {t("header-navigation.my_tenders")}
                        </Link>
                      </li>
                    )}
                    <li>
                      <Link to="/contact">
                        {t("header-navigation.contact")}
                      </Link>
                    </li>
                  </ul>
                </nav>
              </div>
            </Col>
            <Col lg={3}>
              <div className="mainmenu-right">
                <div className="header-search-form">
                  <form onSubmit={SubmitHandler}>
                    <input
                      type="search"
                      placeholder={t("key_words")}
                      autoComplete="off"
                      required
                    />
                    <button>
                      <FaSearch />
                    </button>
                  </form>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </Fragment>
  );
};

export default Header;
