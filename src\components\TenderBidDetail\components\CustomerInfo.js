import React from 'react';
import { Card, Alert } from 'react-bootstrap';
import { FaBuilding, FaPhone, FaEnvelope, FaMapMarkerAlt, FaInfoCircle } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';

/**
 * İhaleyi açan müşteri/şirket bilgilerini gösteren bileşen
 * @param {object} props - Bileşen propsları
 * @param {object} props.customer - Müşteri/şirket verileri
 */
const CustomerInfo = ({ customer }) => {
  const { t } = useTranslation();
  
  if (!customer) {
    return (
      <Card className="mini-card">
        <Card.Header className="mini-card-header py-1 px-2">
          <h6 className="mb-0 mini-header-title d-flex align-items-center">
            <FaBuilding className="me-1 small-icon" />
            {t('tender.customer_info', 'Müşteri Bilgisi')}
          </h6>
        </Card.Header>
        <Card.Body className="mini-card-body p-2">
          <Alert variant="warning" className="mb-0 py-1 px-2 small">
            Müşteri bilgisi bulunamadı
          </Alert>
        </Card.Body>
      </Card>
    );
  }
  
  return (
    <Card className="mini-card">
      <Card.Header className="mini-card-header py-1 px-2">
        <h6 className="mb-0 mini-header-title d-flex align-items-center">
          <FaBuilding className="me-1 small-icon" />
          {t('tender.customer_info', 'Müşteri Bilgisi')}
        </h6>
      </Card.Header>
      <Card.Body className="mini-card-body p-2">
        <div className="compact-customer-data">
          <div className="customer-name-compact">{customer.name || customer.companyName}</div>
          
          {customer.address && (
            <div className="customer-detail-item">
              <FaMapMarkerAlt className="customer-icon" />
              <span className="customer-text">{customer.address}</span>
            </div>
          )}
          
          {customer.email && (
            <div className="customer-detail-item">
              <FaEnvelope className="customer-icon" />
              <a href={`mailto:${customer.email}`} className="customer-link">{customer.email}</a>
            </div>
          )}
          
          {customer.phone && (
            <div className="customer-detail-item">
              <FaPhone className="customer-icon" />
              <a href={`tel:${customer.phone}`} className="customer-link">{customer.phone}</a>
            </div>
          )}
          
          {customer.additionalInfo && (
            <div className="customer-detail-item">
              <FaInfoCircle className="customer-icon" />
              <span className="customer-text">{customer.additionalInfo}</span>
            </div>
          )}
        </div>
      </Card.Body>
    </Card>
  );
};

export default CustomerInfo; 