# LongTermRental-Frontend Katkı Kılavuzu

Bu kılavuz, LongTermRental-Frontend projesine katkıda bulunanlar için stil ve kod standartlarını tanımlar.

## CSS ve Stil Standartları

### Renk Kullanımı

Projede renk tutarlılığını sağlamak için aşağıdaki kurallara uyulmalıdır:

1. **Direkt hex kodları kullanmayın.** Tüm renkler global değişkenlerden alınmalıdır.
   ```css
   /* YANLIŞ */
   .element {
     color: #FF6200;
   }
   
   /* DOĞRU */
   .element {
     color: var(--primary-color);
   }
   ```

2. **Renk tanımlamaları merkezi bir noktada yapılır.** Tüm global renk değişkenleri `src/styles/colors.css` dosyasında tanımlanmıştır.

3. **Yeni renk eklenecekse önce renk sistemini inceleyin.** Mevcut renk paleti ve ton değerlerini kullanmaya özen gösterin. Yeni bir renk eklemeden önce bileşen sorumlusuyla görüşün.

4. **Ton varyasyonları için mevcut değişkenleri kullanın.**
   ```css
   /* Ana renk: --primary-color */
   /* Koyu ton: --primary-dark */
   /* Açık ton: --primary-light */
   ```

### Dosya Yapısı

1. Component stillerini component klasörünün içindeki `style.css` dosyasında tutun.
2. Global stiller için `src/styles/` dizinini kullanın.
3. Her CSS dosyasında bölüm başlıkları kullanarak kodun okunabilirliğini artırın:
   ```css
   /* ============== HEADER STYLES ============== */
   ```

## Renk Değişim Talimatları

Müşteri renk değişikliği isteğinde bulunduğunda:

1. Sadece `src/styles/colors.css` dosyasındaki renk değişkenlerini güncelleyin.
2. Değişkenleri güncellemeden önce eski değerleri yorum olarak saklayın:
   ```css
   --primary-color: #FF6200; /* Eski renk: #ec3323 */
   ```
3. Renk değişikliklerini bir test ortamında kontrol edin ve tüm komponentleri gözden geçirin.

## Renk Paleti (Güncel)

| Değişken Adı | Renk Kodu | Kullanım Alanı |
|--------------|-----------|---------------|
| --primary-color | #FF6200 | Ana marka rengi, butonlar, vurgular |
| --primary-dark | #cc4e00 | Hover durumları, gradyanlar |
| --primary-light | #ff8133 | İkincil vurgular, gradyanlar |
| --secondary-color | #001238 | Başlıklar, koyu metin |

## Yardımcı Sınıflar

Renkleri hızlıca uygulamak için yardımcı CSS sınıfları:

```css
.primary-color { color: var(--primary-color); }
.primary-bg { background-color: var(--primary-color); }
.primary-border { border-color: var(--primary-color); }
``` 