/* ===================== TENDER START DETAILS ======================*/
@import "/node_modules/@syncfusion/ej2-base/styles/material.css";
@import "/node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "/node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "/node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "/node_modules/@syncfusion/ej2-react-calendars/styles/material.css";
@import url('../../styles/colors.css');

.gauto-car-tender {
  background: #fbfbfb none repeat scroll 0 0;
  padding: 20px 0;
}

.rental-tag {
  display: inline-block;
  padding: 5px 15px;
  line-height: 20px;
  text-transform: uppercase;
  background: var(--primary-color);
  color: #fff;
  font-weight: 500;
  font-size: 14px;
  border-radius: 3px;
  margin-bottom: 5px;
}

.car-booking-right h3 {
  font-size: 30px;
  color: #001238;
  letter-spacing: 1px;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: block;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 45px;
}

.car-booking-right h4 {
  color: #020202;
  font-size: 18px;
  text-transform: capitalize;
  margin-top: 0px;
  display: inline-block;
}

.car-booking-right h4 span {
  text-transform: capitalize;
  color: var(--primary-color);
  font-size: 16px;
}

.price-rent {
  display: inline-block;
  margin-right: 15px;
}

.car-rating {
  display: inline-block;
}

.car-rating ul {
  display: inline-block;
}

.car-rating ul li {
  display: inline-block;
  margin-right: 1px;
  color: #ffcd00;
}

.car-rating p {
  display: inline-block;
  margin-left: 5px;
  color: #001238;
  text-transform: capitalize;
}

.price-rating {
  margin-bottom: 20px;
}

.car-features ul {
  width: 45%;
  float: left;
  margin-top: 20px;
}

.car-features ul li {
  margin: 5px 0;
}

.car-features ul li svg {
  margin-right: 5px;
  fill: var(--primary-color);
}

.car-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f6f6f6;
  height: 300px;
  color: #aaa;
  border: 2px dashed #ddd;
  border-radius: 8px;
}

.tender-action-btn {
  text-align: right;
}

.gauto-theme-btn {
  background: var(--primary-color) none repeat scroll 0 0;
  border: 2px solid var(--primary-color);
  border-radius: 2px;
  color: #fff;
  display: inline-block;
  font-size: 15px;
  font-weight: 600;
  padding: 10px 30px;
  text-transform: uppercase;
  transition: all 0.4s ease 0s;
  text-decoration: none;
}

.gauto-theme-btn:hover {
  background: transparent;
  color: var(--primary-color);
  transition: all 0.4s ease 0s;
  text-decoration: none;
}

.gauto-btn,
button.gauto-theme-btn {
  margin: 20px;
}

@media (min-width: 768px) and (max-width: 991px) {
  .car-booking-image img {
    width: 100%;
  }
  .car-booking-right {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .car-booking-right {
    margin-top: 30px;
  }
  .car-booking-right h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .car-features ul {
    width: 100%;
  }
}

@media only screen and (min-width: 480px) and (max-width: 767px) {
  .car-booking-right {
    margin-top: 30px;
  }
  .car-booking-right h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .car-features ul {
    width: 50%;
  }
}

.tsd-feature-box {
  background: var(--primary-color);
  text-align: center;
  padding: 40px 0;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.tsd-price-block span.price {
  color: var(--primary-color);
  font-size: 36px;
  font-weight: 700;
  display: block;
  margin-bottom: 10px;
  line-height: 1;
}

.tsd-feature-icon svg {
  fill: var(--primary-color);
  width: 50px;
  height: 50px;
  margin-bottom: 15px;
}

.tsd-cta {
  background: var(--primary-color) none repeat scroll 0 0;
  border: 2px solid var(--primary-color);
  color: #fff;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 600;
  padding: 12px 30px;
  text-transform: uppercase;
  transition: all 0.4s ease 0s;
  display: inline-block;
  margin-top: 15px;
}

.tsd-benefits-item h5 {
  font-size: 18px;
  margin-bottom: 10px;
  color: var(--primary-color);
  font-weight: 600;
} 