import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { userService } from '../../services';

// Async thunk action creators
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (_, { rejectWithValue, getState }) => {
    try {
      // Auth state'inden token'ı al
      const { auth } = getState();
      let token = auth.token;

      // Eğer Redux state'inde token yoksa localStorage'dan almayı dene
      if (!token) {
        token = localStorage.getItem('token');
      }

      if (!token) {
        return rejectWithValue('Oturum açmanız gerekiyor');
      }

      // Önbellekteki profil verilerini kontrol et
      const cachedProfile = userService.getCachedProfile();

      if (cachedProfile) {
        return cachedProfile;
      }

      // Önbellekte veri yoksa API'den getir
      const response = await userService.getUserProfile();

      if (!response.data || !response.data.result) {
        throw new Error('Geçersiz API yanıtı');
      }

      // Profil verilerini önbelleğe al
      userService.cacheProfileData(response.data);

      return response.data;
    } catch (error) {
      // 401 hatası durumunda özel mesaj döndür, logout işlemini yapma
      if (error.response && error.response.status === 401) {
        return rejectWithValue('Oturum süreniz dolmuş olabilir. Lütfen tekrar giriş yapın.');
      }

      return rejectWithValue(
        error.response?.data?.message ||
        error.message ||
        'Profil bilgileri getirilemedi'
      );
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (profileData, { rejectWithValue, getState }) => {
    try {
      // Auth state'inden token'ı al
      const { auth } = getState();
      let token = auth.token;

      // Eğer Redux state'inde token yoksa localStorage'dan almayı dene
      if (!token) {
        token = localStorage.getItem('token');
      }

      if (!token) {
        return rejectWithValue('Oturum açmanız gerekiyor');
      }

      // API'ye gönderilecek veri formatını hazırla
      const requestData = {
        firstName: profileData.firstName || profileData.name?.split(' ')[0] || '',
        lastName: profileData.lastName || profileData.name?.split(' ').slice(1).join(' ') || '',
        email: profileData.email || '',
        phone: profileData.phone || '',
        title: profileData.title || '',
        taxNumber: profileData.taxNumber || '',
        taxOffice: profileData.taxOffice || '',
        address: profileData.address || '',
        companyPhone: profileData.companyPhone || profileData.phone || '',
        companyEmail: profileData.companyEmail || profileData.email || ''
      };

      const response = await userService.updateUserProfile(requestData);

      if (!response.data || !response.data.isSuccess) {
        throw new Error(response.data?.message || 'Profil güncellenemedi');
      }

      // Önbelleği temizle
      userService.clearProfileCache();

      // Güncel profil verilerini getir
      const updatedProfileResponse = await userService.getUserProfile();

      // Yeni profil verilerini önbelleğe al
      userService.cacheProfileData(updatedProfileResponse.data);

      return updatedProfileResponse.data;
    } catch (error) {
      // 401 hatası durumunda özel mesaj döndür, logout işlemini yapma
      if (error.response && error.response.status === 401) {
        return rejectWithValue('Oturum süreniz dolmuş olabilir. Lütfen tekrar giriş yapın.');
      }

      return rejectWithValue(
        error.response?.data?.message ||
        error.message ||
        'Profil güncellenemedi'
      );
    }
  }
);

export const updateUserPassword = createAsyncThunk(
  'user/updatePassword',
  async (passwordData, { rejectWithValue, getState }) => {
    try {
      // Auth state'inden token'ı al
      const { auth } = getState();
      let token = auth.token;

      // Eğer Redux state'inde token yoksa localStorage'dan almayı dene
      if (!token) {
        token = localStorage.getItem('token');
      }

      if (!token) {
        return rejectWithValue('Oturum açmanız gerekiyor');
      }

      const response = await userService.updateUserPassword(passwordData);

      if (!response.data || !response.data.isSuccess) {
        throw new Error(response.data?.message || 'Şifre güncellenemedi');
      }

      return response.data;
    } catch (error) {
      // 401 hatası durumunda özel mesaj döndür, logout işlemini yapma
      if (error.response && error.response.status === 401) {
        return rejectWithValue('Oturum süreniz dolmuş olabilir. Lütfen tekrar giriş yapın.');
      }

      return rejectWithValue(
        error.response?.data?.message ||
        error.message ||
        'Şifre güncellenemedi'
      );
    }
  }
);

// Initial state
const initialState = {
  profile: null,
  loading: false,
  error: null,
  successMessage: null,
  lastFetched: null
};

// User slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    clearProfile: (state) => {
      state.profile = null;
      state.lastFetched = null;
      userService.clearProfileCache();
    }
  },
  extraReducers: (builder) => {
    builder
      // Profil getirme işlemi
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
        state.lastFetched = Date.now();
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Profil bilgileri getirilemedi';
      })

      // Profil güncelleme işlemi
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
        state.lastFetched = Date.now();
        state.successMessage = 'Profil bilgileri başarıyla güncellendi';
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Profil güncellenemedi';
      })

      // Şifre güncelleme işlemi
      .addCase(updateUserPassword.pending, (state) => {
        state.loading = true;
        state.error = null;
        state.successMessage = null;
      })
      .addCase(updateUserPassword.fulfilled, (state) => {
        state.loading = false;
        state.successMessage = 'Şifre başarıyla güncellendi';
      })
      .addCase(updateUserPassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Şifre güncellenemedi';
      });
  }
});

export const { clearError, clearSuccessMessage, clearProfile } = userSlice.actions;

export default userSlice.reducer;
