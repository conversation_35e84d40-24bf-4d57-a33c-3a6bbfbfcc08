import React, { useState, useEffect } from 'react';
import { Card, Button, Alert, Row, Col } from 'react-bootstrap';
import { FaPlus, FaPaperPlane, FaTrash, FaEdit, FaSave } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import BidRow from './BidRow';
import { submitBid } from '../../../services/tenderBidService';

/**
 * Birden fazla teklifi yöneten ana bileşen
 * LocalStorage ile teklifleri kalıcı hale getirir
 */
const MultipleBidManager = ({ tender, existingBid, onBidSubmitted }) => {
  const { t } = useTranslation();
  const [bids, setBids] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // LocalStorage anahtarı
  const storageKey = `tender_bids_${tender?.id}`;

  // Bileşen yüklendiğinde localStorage'dan teklifleri yükle
  useEffect(() => {
    if (tender?.id) {
      const savedBids = localStorage.getItem(storageKey);
      if (savedBids) {
        try {
          const parsedBids = JSON.parse(savedBids);
          setBids(parsedBids);
        } catch (err) {
          console.error('Kaydedilmiş teklifler yüklenirken hata:', err);
          localStorage.removeItem(storageKey);
        }
      }
      
      // Eğer mevcut bir teklif varsa ve henüz kaydedilmemişse ekle
      if (existingBid && bids.length === 0) {
        const initialBid = {
          id: Date.now(),
          ...existingBid,
          isEditing: false,
          isDraft: true
        };
        setBids([initialBid]);
      }
    }
  }, [tender?.id, existingBid, storageKey]);

  // Teklifler değiştiğinde localStorage'a kaydet
  useEffect(() => {
    if (bids.length > 0) {
      localStorage.setItem(storageKey, JSON.stringify(bids));
    }
  }, [bids, storageKey]);

  // Yeni teklif ekleme
  const addNewBid = () => {
    const newBid = {
      id: Date.now(),
      totalAmount: '',
      description: '',
      terms: '',
      vehiclePrices: {},
      isEditing: true,
      isDraft: true,
      createdAt: new Date().toISOString()
    };

    setBids(prev => [...prev, newBid]);
  };

  // Teklif güncelleme
  const updateBid = (bidId, updatedData) => {
    setBids(prev => prev.map(bid => 
      bid.id === bidId 
        ? { ...bid, ...updatedData, updatedAt: new Date().toISOString() }
        : bid
    ));
  };

  // Teklif silme
  const deleteBid = (bidId) => {
    setBids(prev => prev.filter(bid => bid.id !== bidId));
    setSuccess(t('tender.bid.deleted_success', 'Teklif başarıyla silindi'));
    setTimeout(() => setSuccess(null), 3000);
  };

  // Teklif düzenleme modunu açma/kapama
  const toggleEditMode = (bidId) => {
    setBids(prev => prev.map(bid => 
      bid.id === bidId 
        ? { ...bid, isEditing: !bid.isEditing }
        : bid
    ));
  };

  // Teklifi taslak olarak kaydetme
  const saveBidAsDraft = (bidId) => {
    setBids(prev => prev.map(bid => 
      bid.id === bidId 
        ? { ...bid, isDraft: true, isEditing: false, savedAt: new Date().toISOString() }
        : bid
    ));
    setSuccess(t('tender.bid.saved_as_draft', 'Teklif taslak olarak kaydedildi'));
    setTimeout(() => setSuccess(null), 3000);
  };

  // Tüm teklifleri gönderme
  const submitAllBids = async () => {
    const validBids = bids.filter(bid => 
      bid.totalAmount && 
      parseFloat(bid.totalAmount) > 0 &&
      !bid.isEditing
    );

    if (validBids.length === 0) {
      setError(t('tender.bid.no_valid_bids', 'Gönderilecek geçerli teklif bulunamadı'));
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Her teklifi ayrı ayrı gönder
      const submitPromises = validBids.map(async (bid) => {
        const bidData = {
          tenderId: tender.id,
          totalAmount: parseFloat(bid.totalAmount),
          description: bid.description || '',
          terms: bid.terms || '',
          vehicles: tender.requiredVehicles?.map(vehicle => ({
            vehicleId: vehicle.id,
            price: parseFloat(bid.vehiclePrices?.[vehicle.id] || 0)
          })) || []
        };

        return await submitBid(tender.id, bidData);
      });

      const results = await Promise.all(submitPromises);
      
      // Başarılı gönderilen teklifleri kontrol et
      const successfulSubmissions = results.filter(result => result && result.success);
      
      if (successfulSubmissions.length > 0) {
        // Başarılı gönderilen teklifleri localStorage'dan temizle
        localStorage.removeItem(storageKey);
        setBids([]);
        
        onBidSubmitted({
          count: successfulSubmissions.length,
          data: successfulSubmissions
        });
      } else {
        setError(t('tender.bid.submit_error', 'Teklifler gönderilirken bir hata oluştu'));
      }

    } catch (err) {
      console.error('Teklifler gönderilirken hata:', err);
      setError(err.message || t('tender.bid.submit_error', 'Teklifler gönderilirken bir hata oluştu'));
    } finally {
      setLoading(false);
    }
  };

  // Tüm teklifleri temizleme
  const clearAllBids = () => {
    if (window.confirm(t('tender.bid.confirm_clear_all', 'Tüm teklifleri silmek istediğinizden emin misiniz?'))) {
      setBids([]);
      localStorage.removeItem(storageKey);
      setSuccess(t('tender.bid.all_cleared', 'Tüm teklifler temizlendi'));
      setTimeout(() => setSuccess(null), 3000);
    }
  };

  const validBidsCount = bids.filter(bid => 
    bid.totalAmount && 
    parseFloat(bid.totalAmount) > 0 &&
    !bid.isEditing
  ).length;

  return (
    <div className="multiple-bid-manager">
      {/* Başlık ve Kontroller */}
      <Card className="mb-4">
        <Card.Header className="d-flex justify-content-between align-items-center">
          <div>
            <h5 className="mb-0">{t('tender.bid.multiple_bids', 'Çoklu Teklif Yönetimi')}</h5>
            <small className="text-muted">
              {t('tender.bid.multiple_bids_desc', 'Bu ihale için birden fazla teklif oluşturabilirsiniz')}
            </small>
          </div>
          <div className="d-flex gap-2">
            <Button 
              variant="outline-primary" 
              size="sm"
              onClick={addNewBid}
              disabled={loading}
            >
              <FaPlus className="me-1" />
              {t('tender.bid.add_new', 'Yeni Teklif')}
            </Button>
            {bids.length > 0 && (
              <Button 
                variant="outline-danger" 
                size="sm"
                onClick={clearAllBids}
                disabled={loading}
              >
                <FaTrash className="me-1" />
                {t('tender.bid.clear_all', 'Tümünü Temizle')}
              </Button>
            )}
          </div>
        </Card.Header>
      </Card>

      {/* Hata ve Başarı Mesajları */}
      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          {success}
        </Alert>
      )}

      {/* Teklif Listesi */}
      <div className="bids-list">
        {bids.length === 0 ? (
          <Card className="text-center py-5">
            <Card.Body>
              <h6 className="text-muted">{t('tender.bid.no_bids', 'Henüz teklif eklenmemiş')}</h6>
              <p className="text-muted mb-3">
                {t('tender.bid.add_first_bid', 'İlk teklifinizi eklemek için yukarıdaki butonu kullanın')}
              </p>
              <Button variant="primary" onClick={addNewBid}>
                <FaPlus className="me-1" />
                {t('tender.bid.add_first', 'İlk Teklifi Ekle')}
              </Button>
            </Card.Body>
          </Card>
        ) : (
          <div className="bids-container">
            {bids.map((bid, index) => (
              <BidRow
                key={bid.id}
                bid={bid}
                index={index + 1}
                tender={tender}
                onUpdate={(updatedData) => updateBid(bid.id, updatedData)}
                onDelete={() => deleteBid(bid.id)}
                onToggleEdit={() => toggleEditMode(bid.id)}
                onSaveAsDraft={() => saveBidAsDraft(bid.id)}
              />
            ))}
          </div>
        )}
      </div>

      {/* Gönderme Kontrolleri */}
      {bids.length > 0 && (
        <Card className="mt-4">
          <Card.Body>
            <Row className="align-items-center">
              <Col md={8}>
                <h6 className="mb-1">{t('tender.bid.submit_summary', 'Gönderim Özeti')}</h6>
                <p className="text-muted mb-0">
                  {t('tender.bid.valid_bids_count', 'Gönderilmeye hazır teklif sayısı: {{count}}', { count: validBidsCount })}
                </p>
              </Col>
              <Col md={4} className="text-end">
                <Button 
                  variant="success" 
                  onClick={submitAllBids}
                  disabled={loading || validBidsCount === 0}
                  className="w-100"
                >
                  {loading ? (
                    <>
                      <span className="spinner-border spinner-border-sm me-2" />
                      {t('tender.bid.submitting', 'Gönderiliyor...')}
                    </>
                  ) : (
                    <>
                      <FaPaperPlane className="me-2" />
                      {t('tender.bid.submit_all', 'Tüm Teklifleri Gönder')} ({validBidsCount})
                    </>
                  )}
                </Button>
              </Col>
            </Row>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

MultipleBidManager.propTypes = {
  tender: PropTypes.object.isRequired,
  existingBid: PropTypes.object,
  onBidSubmitted: PropTypes.func.isRequired
};

export default MultipleBidManager;
