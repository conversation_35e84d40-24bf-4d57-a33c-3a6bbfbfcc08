import React, { Fragment, useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import MyBookings from "../../components/MyBookings";

const MyBookingsPage = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);
  const { t } = useTranslation();
  
  // <PERSON><PERSON>ş yapmamış kullanıcıları giriş sayfasına yönlendir
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);
  
  return (
    <Fragment>
      <PageTitle
        pageTitle={t("my_bookings.title")}
        pagesub={t("my_bookings.title")}
      />
      <MyBookings />
    </Fragment>
  );
};

export default MyBookingsPage; 