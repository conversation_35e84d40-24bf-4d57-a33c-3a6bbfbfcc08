/* Şirket Profili Sayfası Stilleri */
.company-profile-area {
  padding: 20px 0;
}

.company-profile-area .cp-profile-card {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  transition: box-shadow 0.3s ease;
  overflow: hidden;
  min-height: 200px;
}

.company-profile-area .cp-profile-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.company-profile-area .company-logo-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.company-profile-area .company-logo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #28a745;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.2);
  transition: all 0.3s ease;
}

.company-profile-area .company-logo:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(40, 167, 69, 0.3);
}

.company-profile-area .company-logo-placeholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: 4px solid #dee2e6;
  font-size: 3rem;
  color: #adb5bd;
  transition: all 0.3s ease;
}

.company-profile-area .company-logo-placeholder:hover {
  transform: scale(1.05);
  border-color: #28a745;
}

.company-profile-area .verification-status {
  display: inline-flex;
  align-items: center;
  padding: 0.3rem 0.7rem;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 500;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.company-profile-area .verification-status:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.company-profile-area .verification-status.verified {
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.company-profile-area .verification-status.unverified {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.company-profile-area .company-info-list {
  margin-bottom: 1.5rem;
}

.company-profile-area .company-info-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.company-profile-area .company-info-item:last-child {
  border-bottom: none;
}

.company-profile-area .cp-info-icon {
  margin-right: 1rem;
  min-width: 20px;
  color: #28a745;
}

.company-profile-area .social-media-links {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.company-profile-area .social-media-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f8f9fa;
  color: #6c757d;
  margin: 0 0.5rem;
  transition: all 0.3s ease;
}

.company-profile-area .social-media-links a:hover {
  background-color: #28a745;
  color: #fff;
  transform: translateY(-3px);
}

.company-profile-area .cp-edit-button {
  border-color: #dc3545;
  color: #dc3545;
  font-weight: 500;
  transition: all 0.2s ease;
}

.company-profile-area .cp-edit-button:hover {
  background-color: #dc3545;
  color: #fff;
}

.company-profile-area .cp-edit-button .cp-edit-icon {
  font-size: 0.9rem;
  transition: transform 0.2s ease;
}

.company-profile-area .cp-edit-button:hover .cp-edit-icon {
  transform: rotate(20deg);
}

.company-profile-area .cp-edit-button .cp-edit-icon.cancel {
  color: #6c757d;
}

.company-profile-area .btn-save {
  background-color: #28a745;
  border-color: #28a745;
  font-weight: 500;
  transition: all 0.2s ease;
}

.company-profile-area .btn-save:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

.company-profile-area .save-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
  margin-right: 0.5rem;
}

.company-profile-area .form-input {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.company-profile-area .form-input:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

.company-profile-area .form-textarea {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  padding: 0.5rem 0.75rem;
  resize: vertical;
  min-height: 120px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.company-profile-area .form-textarea:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);
}

.company-profile-area .cp-profile-tabs {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.company-profile-area .cp-profile-tabs .nav-link {
  color: #6c757d;
  font-weight: 500;
  padding: 0.75rem 1rem;
  border: none;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.company-profile-area .cp-profile-tabs .nav-link:hover {
  color: #28a745;
}

.company-profile-area .cp-profile-tabs .nav-link.active {
  color: #28a745;
  border-bottom: none;
  background-color: transparent;
}

.company-profile-area .cp-profile-tabs .nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #28a745;
}

.company-profile-area{
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: #343a40;
  position: relative;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #eee;
}

/* Yeşil çizgi kaldırıldı */
.company-profile-area  {
  display: none;
}

.company-profile-area .cp-info-list-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.company-profile-area .cp-info-list-item:hover {
  background-color: #f2f2f2;
  transform: translateY(-2px);
}

.company-profile-area .service-item {
  margin-bottom: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease;
}

.company-profile-area .service-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.company-profile-area .input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

.company-profile-area .contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.company-profile-area .contact-item {
  padding: 1.25rem;
  border-radius: 0.5rem;
  background-color: #f8f9fa;
  transition: all 0.2s ease;
}

.company-profile-area .contact-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.company-profile-area .contact-icon {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-color: rgba(40, 167, 69, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #28a745;
  font-size: 1.25rem;
}

.company-profile-area .contact-details h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #343a40;
}

.company-profile-area .contact-details p {
  font-size: 0.9rem;
  color: #6c757d;
}

.company-profile-area .empty-data-message {
  text-align: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  margin: 15px 0;
  border: 1px dashed #dee2e6;
}

.company-profile-area .form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.company-profile-area .rental-periods-options {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.company-profile-area .loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 20px;
  border-radius: 8px;
}

.company-profile-area .loading-spinner {
  border: 3px solid rgba(40, 167, 69, 0.2);
  border-top: 3px solid #28a745;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

/* Tedarik bilgileri kartları */
.company-profile-area .supplier-info-card {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.08);
  padding: 1rem;
  height: 100%;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.company-profile-area .supplier-info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.company-profile-area .supplier-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: #28a745;
}

.company-profile-area .supplier-info-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(40, 167, 69, 0.1);
  color: #28a745;
  margin-right: 10px;
}

.company-profile-area .supplier-info-title {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
}

.company-profile-area .supplier-info-content {
  color: #6c757d;
  font-size: 0.95rem;
  margin-top: 0.5rem;
}

.company-profile-area .sub-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #495057;
  border-bottom: none;
  position: relative;
  display: inline-flex;
  align-items: center;
}

.company-profile-area .sub-section-title::after {
  display: none;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Ayarlamalar */
@media (max-width: 768px) {
  .company-profile-area .contact-info {
    grid-template-columns: 1fr;
  }
  
  .company-profile-area .cp-profile-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .company-profile-area .company-logo {
    width: 100px;
    height: 100px;
  }
  
  .company-profile-area .company-logo-placeholder {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }
   
  .company-profile-area .rental-periods-options {
    flex-direction: column;
  }
   
  .company-profile-area .service-item {
    padding: 0.75rem;
  }
}

@media (max-width: 576px) {
  .company-profile-area .cp-profile-tabs {
    flex-wrap: nowrap;
    overflow-x: auto;
    white-space: nowrap;
  }
} 