import React from "react";
import { Link } from "react-router-dom";
import logo1 from "../../img/new-images/Logo/ihaleden-kirala-uzun-dönem-arac-kiralama-logo.png";
import logoWhite from "../../img/new-images/Logo/ihaleden-kirala-uzun-dönem-arac-kiralama-logo-beyaz.png";
import "./style.css";

const Logo = ({ className = "", color = "default" }) => {
  const logoSrc = color === "white" ? logoWhite : logo1;
  return (
    <Link to="/" className={`text-logo ${className}`}>
      <img src={logoSrc} alt="İhaleden Kirala Logo" className="logo1-img" />
    </Link>
  );
};

export default Logo; 