import React from "react";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";

import img2 from "../../img/new-images/Promo/fleet-3-cars-ihaleden-kirala.png";

import "./style.css";

const AboutPromo = () => {
  const { t } = useTranslation();

  return (
    <section className="about-promo-section section_70">
      <Container>
        <Row>
          <Col md={12}>
            <div className="about-promo-text">
              <h3
                dangerouslySetInnerHTML={{
                  __html: t("about_page.proud_title", {
                    interpolation: { escapeValue: false },
                  }),
                }}
              ></h3>
            </div>
          </Col>
        </Row>
        <Row>
          <Col md={12}>
            <div className="about-promo-image">
              <img src={img2} alt="about promo" />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default AboutPromo; 