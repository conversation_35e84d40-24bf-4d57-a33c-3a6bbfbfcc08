/**
 * Formatlama yardımcı fonksiyonları
 */

// <PERSON><PERSON><PERSON> yerelleştirilmiş olarak formatla
export const formatDate = (date, locale = 'tr-TR') => {
  if (!date) return '';
  return new Date(date).toLocaleDateString(locale);
};

// Tarihi detaylı şekilde formatla (gün, ay, yıl)
export const formatDetailedDate = (date, locale = 'tr-TR') => {
  if (!date) return { day: '', month: '', year: '' };
  
  const dateObj = new Date(date);
  
  return {
    day: dateObj.getDate(),
    month: dateObj.toLocaleString(locale, { month: 'long' }),
    year: dateObj.getFullYear()
  };
};

// Para birimini formatla
export const formatCurrency = (amount, currency = 'TRY', locale = 'tr-TR') => {
  if (amount === undefined || amount === null) return '';
  
  return new Intl.NumberFormat(locale, { 
    style: 'currency', 
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

// Kalan süreyi formatlayan fonksiyon
export const formatRemainingTime = (remainingTime) => {
  if (!remainingTime || remainingTime === "Süre doldu") return remainingTime;
  
  // Örnek: "7g 12s 30d 45sn" veya "12s 30d 45sn"
  const parts = remainingTime.split(' ');
  const timeUnits = [];
  
  // Gün, saat, dakika ve saniye değerlerini analiz et
  parts.forEach(part => {
    if (part.includes('g')) {
      timeUnits.push({
        value: part.replace('g', ''),
        label: 'Gün'
      });
    } else if (part.includes('sn')) {
      timeUnits.push({
        value: part.replace('sn', ''),
        label: 'Saniye'
      });
    } else if (part.includes('s')) {
      timeUnits.push({
        value: part.replace('s', ''),
        label: 'Saat'
      });
    } else if (part.includes('d')) {
      timeUnits.push({
        value: part.replace('d', ''),
        label: 'Dakika'
      });
    }
  });
  
  return timeUnits;
};

export default {
  formatDate,
  formatDetailedDate,
  formatCurrency,
  formatRemainingTime
}; 