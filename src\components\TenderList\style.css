@import url('../../styles/colors.css');

.tender-list-area {
  padding: 40px 0;
  background-color: white;
  min-height: 80vh;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  margin: 20px;
}

.tender-list-container {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tender-list-title {
  padding: 25px 30px;
  border-bottom: 1px solid #eaedf2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
}

.tender-list-title h2 {
  font-size: 22px;
  font-weight: 600;
  color: #0c2340;
  margin: 0;
}

.filters-container {
  padding: 20px 30px;
  background-color: white;
  border-radius: 0;
  border-bottom: 1px solid #eaedf2;
}

.tender-empty, 
.tender-loading {
  background-color: white;
  border-radius: 0 0 20px 20px;
  padding: 60px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
}

/* Filtre Container */
.filters-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  padding: 25px;
  margin-bottom: 25px;
  border: none;
  position: relative;
  z-index: 1;
}

/* Advanced List Container */
.advanced-list-container {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 20px;
  border: none;
  position: relative;
  z-index: 1;
}

/* İhale Bulunamadı Ekranı */
.tender-empty {
  text-align: center;
  padding: 80px 30px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: none;
  position: relative;
  z-index: 1;
}

/* Yükleme göstergesi */
.tender-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 250px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border: none;
  position: relative;
  z-index: 1;
}

/* Başlık stilleri */
.tender-header {
  margin-bottom: 30px;
  border-bottom: none;
  padding-bottom: 15px;
  text-align: center;
  position: relative;
}

.tender-header:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

.tender-main-title {
  font-size: 34px;
  color: #0c2340;
  font-weight: 600;
  margin-bottom: 0;
  font-family: "Poppins", sans-serif;
  letter-spacing: -0.5px;
}

.tender-subtitle {
  color: #4a5568;
  font-size: 15px;
  margin-top: 0;
  font-weight: 400;
}

/* Liste görünümü stilleri */
.tender-table-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  margin-top: 10px;
}

.tender-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  background-color: transparent;
  margin-bottom: 0;
}

.tender-table th {
  text-align: left;
  padding: 18px 15px;
  font-weight: 600;
  color: #001238;
  border-bottom: 1px solid #f0f0f0;
  font-family: "Poppins", sans-serif;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 0.5px;
  background-color: #fafafa;
}

.tender-table td {
  padding: 22px 15px;
  vertical-align: middle;
  border-bottom: 1px solid #f0f0f0;
  color: #7c8a97;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tender-row {
  position: relative;
  transition: all 0.3s ease;
  background-color: #fff;
}

.tender-row:hover {
  background-color: #f8f9fa;
  transform: translateY(-1px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
  z-index: 1;
}

.tender-row td:first-child {
  border-left: 4px solid transparent;
}

.tender-row.active td:first-child {
  border-left: 4px solid #198754;
}

.tender-row.completed td:first-child {
  border-left: 4px solid #0d6efd;
}

.tender-row.cancelled td:first-child {
  border-left: 4px solid var(--primary-color);
}

.tender-title {
  font-weight: 600;
  color: #001238;
  margin-bottom: 5px;
  font-size: 15px;
}

.tender-car {
  font-size: 14px;
  color: #6b739c;
  margin-top: 5px;
}

.tender-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tender-spec {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7c8a97;
  font-size: 14px;
}

.tender-spec i, 
.tender-spec svg {
  color: var(--primary-color);
  font-size: 16px;
  min-width: 16px;
}

.tender-responses-count {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
  padding: 6px 12px;
  border-radius: 30px;
  font-weight: 500;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.tender-date {
  color: #7c8a97;
  font-size: 14px;
  line-height: 1.5;
}

.tender-status-area {
  min-width: auto;
  margin-left: 5px;
  display: inline-block;
}

.tender-status {
  font-weight: 600;
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  text-transform: uppercase;
  white-space: nowrap;
  letter-spacing: 0;
  display: inline-block;
  line-height: 12px;
}

.tender-status-active {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
  border: 1px solid rgba(25, 135, 84, 0.2);
}

.tender-status-completed {
  background-color: rgba(13, 110, 253, 0.1);
  color: #0d6efd;
  border: 1px solid rgba(13, 110, 253, 0.2);
}

.tender-status-cancelled {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
  border: 1px solid var(--primary-shadow);
}

.tender-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* App.css ile uyumlu buton stilleri */
.tender-actions .gauto-btn-white {
  color: var(--primary-color);
  background: transparent;
  border: 2px solid var(--primary-color);
  position: relative;
  padding: 8px 12px;
  transition: all 0.3s ease-in-out;
  text-align: center;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  border-radius: 4px;
  margin: 0;
}

.tender-actions .gauto-btn-white:hover {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--primary-shadow);
}

.tender-filter {
  margin-bottom: 20px;
  padding: 16px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03);
}

.tender-filter label {
  font-weight: 500;
  color: #001238;
  margin-right: 15px;
  font-size: 14px;
}

.tender-filter select {
  background-color: #fff;
  border: 2px solid #f0f0ff;
  padding: 8px 15px;
  border-radius: 6px;
  color: #001238;
  font-size: 14px;
  min-width: 150px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.tender-filter select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 2px 8px var(--primary-shadow);
}

/* Kart görünümü stilleri */
.tender-cards-container {
  margin-top: 20px;
}

.tender-card {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border-top: 4px solid #f0f0f0;
}

.tender-card.active {
  border-top-color: #198754;
}

.tender-card.completed {
  border-top-color: #0d6efd;
}

.tender-card.cancelled {
  border-top-color: var(--primary-color);
}

.tender-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.tender-card-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.tender-card-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.tender-card-title h4 {
  font-size: 16px;
  font-weight: 600;
  color: #001238;
  margin: 0;
  padding-right: 10px;
  flex: 1;
}

.tender-car-info {
  margin-top: 5px;
}

.car-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  color: #6b739c;
  font-size: 14px;
  font-weight: 500;
}

.car-badge svg {
  color: var(--primary-color);
}

.tender-card-body {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tender-card-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
  color: #7c8a97;
  font-size: 13px;
}

.info-icon {
  color: var(--primary-color);
}

.tender-card-dates {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.date-label {
  font-size: 13px;
  color: #6b739c;
  font-weight: 500;
}

.dates {
  font-size: 14px;
  color: #001238;
}

.responses-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: var(--primary-transparent);
  color: var(--primary-color);
  padding: 8px 14px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  align-self: flex-start;
}

.tender-card-footer {
  padding: 15px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.details-btn, .edit-btn {
  flex: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.details-btn {
  background-color: #f8f9fa;
  color: #001238;
}

.details-btn:hover {
  background-color: #eff2f7;
  color: var(--primary-color);
}

.edit-btn {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
}

.edit-btn:hover {
  background-color: var(--primary-color);
  color: #fff;
}

/* Responsive düzenlemeler */
@media (max-width: 991px) {
  .tender-card-title h4 {
    font-size: 15px;
  }
  
  .tender-card-info {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 767px) {
  .tender-filter {
    padding: 20px 15px;
  }
  
  .tender-card-footer {
    flex-direction: column;
  }
  
  .tender-card-title {
    flex-direction: column;
    gap: 10px;
  }
  
  .tender-status-area {
    padding-left: 0;
    margin-left: auto;
    margin-top: 0;
  }
  
  .tender-status {
    font-size: 8px;
    padding: 1px 3px;
    line-height: 10px;
  }
}

/* Responsive tasarım */
@media (max-width: 991px) {
  .tender-specs {
    flex-direction: column;
    gap: 5px;
  }
  
  .tender-table th:nth-child(3),
  .tender-table td:nth-child(3) {
    display: none;
  }
  
  .tender-main-title {
    font-size: 26px;
  }
  
  .empty-icon-container {
    width: 80px;
    height: 80px;
  }
  
  .empty-icon {
    font-size: 32px;
  }
}

@media (max-width: 767px) {
  .tender-list-area {
    padding: 40px 0;
  }
  
  .tender-list-area::before {
    height: 140px;
  }
  
  .tender-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    text-align: left;
  }
  
  .tender-header:after {
    left: 0;
    transform: none;
  }
  
  .tender-main-title {
    font-size: 28px;
  }
  
  .tender-table-container {
    overflow-x: auto;
  }
  
  .tender-table {
    min-width: 800px;
  }
  
  .tender-table th:nth-child(4),
  .tender-table td:nth-child(4) {
    display: none;
  }
  
  .tender-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .filter-wrapper {
    margin-top: 15px;
  }
  
  .tender-empty {
    padding: 40px 20px;
  }
  
  .tender-empty h3 {
    font-size: 20px;
  }
  
  .tender-empty p {
    font-size: 14px;
    margin-bottom: 25px;
  }
  
  .new-tender-btn {
    padding: 10px 25px !important;
    font-size: 14px !important;
    min-width: 200px;
  }
}

@media (max-width: 576px) {
  .tender-table th:nth-child(5),
  .tender-table td:nth-child(5) {
    display: none;
  }
  
  .tender-filter {
    padding: 12px 15px;
  }
  
  .filter-wrapper {
    flex-wrap: wrap;
  }
  
  .filter-wrapper label {
    width: 100%;
    margin-bottom: 5px;
  }
  
  .tender-count span {
    width: 100%;
    text-align: center;
  }
  
  .empty-icon-container {
    width: 70px;
    height: 70px;
    margin-bottom: 20px;
  }
  
  .empty-icon {
    font-size: 28px;
  }
}

/* Kompakt kart görünümü stilleri */
.tender-card-compact {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0,0,0,0.04);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
  border-left: 4px solid #f0f0f0;
  position: relative;
}

.tender-card-compact.active {
  border-left-color: #198754;
}

.tender-card-compact.completed {
  border-left-color: #0d6efd;
}

.tender-card-compact.cancelled {
  border-left-color: var(--primary-color);
}

.tender-card-compact:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0,0,0,0.07);
}

.tender-card-left {
  flex: 1;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tender-card-right {
  width: 100px;
  padding: 15px 10px;
  background-color: #fafafa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  border-left: 1px solid #f0f0f0;
}

.tender-card-compact .tender-card-title h4 {
  font-size: 15px;
  font-weight: 600;
  color: #001238;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.compact-car-info {
  font-size: 13px;
  color: #6b739c;
  display: flex;
  align-items: center;
  gap: 5px;
}

.compact-car-info svg {
  color: var(--primary-color);
  font-size: 13px;
}

.compact-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.spec-item {
  font-size: 12px;
  color: #7c8a97;
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #f8f9fa;
  padding: 5px 8px;
  border-radius: 4px;
}

.spec-item svg {
  color: var(--primary-color);
  font-size: 12px;
}

.compact-date {
  font-size: 12px;
  color: #7c8a97;
  text-align: center;
}

.compact-responses {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  color: var(--primary-color);
  background-color: var(--primary-transparent);
  border-radius: 30px;
  padding: 3px 8px;
  font-weight: 500;
}

.compact-actions {
  display: flex;
  gap: 6px;
}

.details-btn-sm, .edit-btn-sm {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: 13px;
}

.details-btn-sm {
  background-color: #f0f0f0;
  color: #001238;
}

.details-btn-sm:hover {
  background-color: #e5e5e5;
  color: var(--primary-color);
}

.edit-btn-sm {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
}

.edit-btn-sm:hover {
  background-color: var(--primary-color);
  color: #fff;
}

/* Mobil için uyarlamalar */
@media (max-width: 767px) {
  .tender-card-compact {
    flex-direction: column;
    border-left: none;
    border-top: 4px solid #f0f0f0;
  }
  
  .tender-card-compact.active {
    border-top-color: #198754;
    border-left: none;
  }
  
  .tender-card-compact.completed {
    border-top-color: #0d6efd;
    border-left: none;
  }
  
  .tender-card-compact.cancelled {
    border-top-color: var(--primary-color);
    border-left: none;
  }
  
  .tender-card-right {
    width: 100%;
    flex-direction: row;
    padding: 10px 15px;
    border-left: none;
    border-top: 1px solid #f0f0f0;
  }
  
  .compact-date, .compact-responses {
    font-size: 11px;
  }
}

/* Gelişmiş Liste Görünümü - İyileştirilmiş */
.advanced-list-container {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-top: 20px;
  border: none;
  position: relative;
  z-index: 1;
  padding: 10px;
}

.advanced-list-container:hover {
  box-shadow: 0 15px 50px rgba(0, 18, 50, 0.1);
}

.list-item {
  display: flex;
  position: relative;
  border-bottom: 1px solid #e9ecef;
  transition: all 0.2s ease;
  padding: 15px 20px;
  margin-bottom: 8px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.list-item:last-child {
  margin-bottom: 0;
}

.list-item:hover {
  background-color: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  z-index: 2;
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: calc(100% - 210px); /* Butonlar için yer açar */
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
}

.tender-title-area {
  flex: 1;
  max-width: 85%;
  padding-right: 15px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.tender-title-area h4 {
  font-size: 16px;
  font-weight: 600;
  color: #0c2340;
  margin: 0;
  line-height: 1.3;
  letter-spacing: -0.3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.car-model {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 10px;
}

.car-model svg {
  color: var(--primary-color);
  font-size: 15px;
  flex-shrink: 0;
}

.list-item-details {
  display: flex;
  gap: 0;
  width: 100%;
}

.details-group {
  flex: 1;
  padding-right: 15px;
  min-width: 0;
  max-width: 33.33%;
}

.details-label {
  font-size: 12px;
  color: #4a5568;
  margin-bottom: 5px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.list-item-specs {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.list-spec {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #0c2340;
  background-color: #f8f9fa;
  padding: 6px 10px;
  border-radius: 6px;
  font-weight: 500;
  border: 1px solid #e9ecef;
  white-space: nowrap;
}

.list-spec svg {
  color: var(--primary-color);
  font-size: 14px;
  flex-shrink: 0;
}

.date-value {
  font-size: 14px;
  color: #0c2340;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.count-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: var(--primary-transparent);
  color: var(--primary-color);
  padding: 8px 14px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid var(--primary-shadow);
  white-space: nowrap;
}

.count-badge svg {
  font-size: 14px;
  flex-shrink: 0;
}

.list-item-actions {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  gap: 10px;
  width: auto;
  justify-content: flex-end;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 8px;
  font-size: 16px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  flex-shrink: 0;
  padding: 0;
}

.action-btn span {
  display: none;
}

.details-btn {
  background-color: #f0f0f0;
  color: #0c2340;
  border: 1px solid #e9ecef;
}

.details-btn:hover {
  background-color: #e0e0e0;
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.08);
}

.edit-btn {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
  border: 1px solid var(--primary-shadow);
}

.edit-btn:hover {
  background-color: var(--primary-color);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--primary-shadow);
}

/* Responsive uyarlamalar */
@media (max-width: 991px) {
  .list-item-details {
    flex-wrap: wrap;
  }
  
  .details-group {
    flex: 1 0 50%;
    max-width: 50%;
    margin-bottom: 15px;
  }
  
  .list-item-content {
    max-width: calc(100% - 200px);
  }
}

@media (max-width: 767px) {
  .list-item {
    flex-direction: column;
    padding: 15px;
  }
  
  .list-item-content {
    max-width: 100%;
  }
  
  .list-item-header {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .tender-title-area {
    max-width: 100%;
    padding-right: 0;
    margin-bottom: 5px;
  }
  
  .tender-status-area {
    padding-left: 0;
    margin-left: auto;
  }
  
  .list-item-actions {
    position: absolute;
    right: 15px;
    top: 20px;
    transform: none;
    margin-top: 0;
    width: auto;
  }
  
  .car-model {
    margin-bottom: 12px;
  }
  
  .details-group {
    flex: 1 0 100%;
    max-width: 100%;
    margin-bottom: 12px;
  }
  
  .status-indicator {
    width: 100%;
    height: 4px;
    top: 0;
    left: 0;
    right: 0;
    bottom: auto;
  }
}

/* Pagination Container */
.pagination-container {
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.pagination-top-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  margin-bottom: 20px;
}

.pagination {
  margin-bottom: 15px;
  display: inline-flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
  height: 38px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.03);
}

.pagination .page-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.pagination .page-item .page-link {
  color: #0c2340;
  border: 1px solid #e9ecef;
  padding: 0;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 38px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  margin: 0 2px;
  background-color: #fff;
}

.pagination .page-item .page-link:hover {
  background-color: #f8f9fa;
  color: var(--primary-color);
  border-color: #e9ecef;
  z-index: 1;
}

.pagination .page-item.active .page-link {
  background-color: var(--primary-color);
  color: #fff;
  border-color: var(--primary-color);
  z-index: 2;
  font-weight: 600;
  box-shadow: 0 2px 5px var(--primary-shadow);
}

.pagination .page-item.disabled .page-link {
  color: #a0aec0;
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.pagination-info {
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
}

.items-select {
  width: auto;
  padding: 7px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  background-color: #f8f9fa;
  cursor: pointer;
  color: #0c2340;
  font-weight: 500;
  transition: all 0.2s ease;
}

.items-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 2px 5px var(--primary-shadow);
}

.items-select:hover {
  background-color: #fff;
  border-color: #cfd8e3;
}

/* Mobil Filtreler ve Pagination */
@media (max-width: 767px) {
  .filters-container {
    padding: 20px 15px;
    margin-top: 20px;
  }
  
  .pagination {
    height: 34px;
  }
  
  .pagination .page-item .page-link {
    min-width: 34px;
    height: 34px;
    font-size: 13px;
  }
  
  .pagination-top-row {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .items-per-page {
    margin-top: 5px;
  }
}

@media (max-width: 575px) {
  .pagination {
    height: 30px;
  }
  
  .pagination .page-item .page-link {
    min-width: 30px;
    height: 30px;
    font-size: 12px;
    margin: 0 1px;
  }
}

.empty-icon-container {
  width: 120px;
  height: 120px;
  background-color: #f7f8fb;
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
}

.empty-icon {
  font-size: 50px;
  color: #91a2b8;
}

.tender-empty h3 {
  font-size: 20px;
  font-weight: 600;
  color: #0c2340;
  margin-bottom: 10px;
}

.tender-empty p {
  color: #6b7a8f;
  margin-bottom: 30px;
  max-width: 500px;
  line-height: 1.6;
}

/* Yeni İhale Başlat Butonu */
.new-tender-btn {
  display: inline-flex;
  align-items: center;
  background-color: #0069d9;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-weight: 500;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-tender-btn:hover {
  background-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 105, 217, 0.2);
}

.new-tender-btn svg {
  margin-right: 8px;
  font-size: 16px;
} 