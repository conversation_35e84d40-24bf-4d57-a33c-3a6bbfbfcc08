{"need_help": "Need Help?", "call": "Call Us", "login": "<PERSON><PERSON>", "register": "Register", "language": "Language", "melbourne_city": "Istanbul, Turkey", "request_call": "Request Call", "researve_now": "Reserve Now!", "header": {"promo": {"locations": "Nationwide Service", "nationwide": "All Major Cities", "long_term": "Long Term Rental", "flexible_plans": "Flexible Contract Plans"}}, "unauthorized": {"title": "Unauthorized Access", "message": "You don't have the necessary permissions to access this page.", "info": "You can log in with a different account or return to the home page.", "dashboard": "Go to Dashboard", "home": "Home Page"}, "common": {"toggle_menu": "Toggle <PERSON>", "close": "Close", "detailed_info": "Detailed Information", "supplier": "Dear Supplier", "welcome": "Welcome", "time": {"hours": "{{count}} hours"}, "duration": {"months": "{{count}} Months"}, "loading": "Loading...", "select": "Select", "cancel": "Cancel", "back": "Back", "download": "Download", "error": "Error", "back_to_list": "Back to List", "sort": "Sort", "yes": "Yes", "no": "No", "copied": "Copied!", "copy": "Copy"}, "dashboard": {"welcome": "Welcome, {{name}}", "subtitle": "You can manage your long-term vehicle rental operations here", "description": "View supplier offers for your long-term rental operations, track your tenders, and create new vehicle rental requests.", "redirecting": "Redirecting...", "tabs": {"my_tenders": "My Tenders", "open_tenders": "Open Tenders", "my_bids": "My Bids", "contracts": "My Contracts", "vehicles": "My Vehicles", "tenders": "Tenders"}, "stats": {"title": "Tender and Bid Status", "my_tenders": "My Active Tenders", "open_tenders": "Open Tenders", "active_bids": "Active Bids", "approved_bids": "Approved Bids", "active": "Active", "completed": "Completed", "contracts": "Contracts", "vehicles": "Total Vehicles", "new_tenders": "New Requests", "pending_tenders": "Pending Offers", "rejected": "Rejected", "approved": "Approved", "active_tenders": "My Active Tenders", "given_bids": "Given Bids", "won_tenders": "Won Tenders", "total_transaction": "Total Transaction", "new": "new", "last_7_days": "in last 7 days", "last_month": "in last month"}, "actions": {"create_tender": "Create <PERSON> Tender", "explore": "Explore Tenders", "reports": "Reports", "help": "Help", "view": "View", "edit": "Edit", "cancel": "Cancel", "bid": "Place Bid"}, "active_tenders": {"title": "My Active Tenders", "no_items": "No active tenders found", "tender_no": "Tender No", "start_date": "Start Date", "offer_count": "Offer Count", "offers": "Offers", "remaining_time": "Remaining Time", "lowest_offer": "Lowest Offer", "vehicle_count": "Vehicle Count", "vehicles": "Vehicles", "view_details": "View Details", "view_all": "View All", "loading": "Loading..."}, "suggested_tenders": {"title": "Suggested Tenders", "no_items": "No suggested tenders found", "view_all": "View More", "match_percentage": "{{percentage}}% Match", "in_days": "days", "bid_button": "Place Bid", "error_loading": "Error loading suggested tenders", "mock_data": {"tender1_title": "Bursa Commercial Vehicle Fleet", "tender2_title": "Antalya Tourism Vehicles"}}, "recent_activities": {"title": "Recent Activities", "no_items": "No activities found yet", "view_all": "View All Activities", "tender_bid": "You placed a bid for tender {{tender_name}}", "tender_created": "You created a new tender: {{tender_name}}", "tender_match": "You won tender {{tender_name}}!", "tender_alert": "{{tender_name}} final bid process is approaching", "hours_ago": "{{hours}} hours ago", "days_ago": "{{days}} days ago", "loading": "Loading..."}, "tender_status": {"active": "Active", "new_offers": "New Offers", "last_24_hours": "Last 24 Hours"}, "tenders": {"fields": {"title": "Tender Title", "type": "Offer Type", "start_date": "Start Date", "end_date": "End Date", "monthly_price": "Monthly Price", "vehicle_count": "Vehicle Count", "vehicle": "vehicle", "price_range": "Price Range", "vehicles": "Vehicle Count", "valid_days": "Validity Period", "rental_period": "Rental Period"}, "actions": {"bid": "Place Bid", "view": "View", "details": "View Details"}}, "start_new_tender": "Start Tender", "start_first_tender": "Start Your First Tender", "my_tenders": "My Tenders", "open_tenders": "Open Tenders", "my_bids": "My Bids", "bids": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected"}}, "tender": {"data_loading_error": "Failed to load tender data.", "error_loading": "An error occurred while loading data.", "not_found": "Tender not found.", "tabs": {"new_requests": "New Requests", "pending_offers": "Pending Offers", "approved_offers": "Approved Offers", "rejected_offers": "Rejected Offers", "details": "Details", "vehicles": "Vehicles", "documents": "Documents", "create_bid": "Create Bid"}, "response_time_warning": "You must respond to customer inquiries within 12 hours. Otherwise, requests will become inactive.", "table": {"request_no": "Request No", "offer_no": "Offer No", "customer": "Customer", "date": "Date", "vehicle_count": "Vehicle Count", "segment": "Segment", "duration": "Duration", "documents": "Documents", "remaining_time": "Remaining Time", "actions": "Actions", "offer_amount": "Offer Amount", "status": "Status", "approval_date": "Approval Date", "rejection_reason": "Rejection Reason"}, "documents": {"complete": "Complete", "incomplete": "Incomplete"}, "actions": {"view_documents": "View Documents", "make_offer": "Make Offer", "create_contract": "Create Contract", "view_details": "View Details"}, "status": {"customer_reviewing": "Customer Reviewing"}, "rejection": {"better_competitor_offer": "Better offer received from competitor"}, "created_at": "Created At", "start_date": "Start Date", "end_date": "End Date", "budget_range": "Budget Range", "detailed_description": "Detailed Description", "additional_information": "Additional Information", "delivery_location": "Delivery Location", "payment_terms": "Payment Terms", "insurance_requirements": "Insurance Requirements"}, "vehicle": {"status": {"available": "Available", "rented": "Rented"}}, "location": {"istanbul": "Istanbul", "ankara": "Ankara", "izmir": "Izmir"}, "advantages": {"header": {"title": "Benefits of Long-Term Car Rental", "description": "Take advantage of long-term car rental benefits by receiving price quotes from various companies through a single request on our online platform."}, "capitalPreservation": {"title": "Capital Preservation", "description": "You can allocate your budget to other resources instead of taking out loans for company vehicles."}, "fixedCost": {"title": "Fixed Cost", "description": "You won't have to deal with additional expenses beyond the amount specified in the contract."}, "maintenanceEase": {"title": "Maintenance Ease", "description": "You won't waste time with insurance, comprehensive coverage, tax payments, and vehicle maintenance."}, "remoteAccess": {"title": "Remote Access", "description": "You can quickly create your rental request through our digital platform without leaving your office."}, "resourceOptimization": {"title": "Resource Optimization", "description": "You can utilize your human resources more efficiently as all operational processes are managed by the fleet company."}, "taxBenefit": {"title": "Tax Benefit", "description": "You can show the full VAT amount as deductible tax in rental transactions."}, "fastDelivery": {"title": "Fast Delivery", "description": "You can receive your vehicles immediately following the quick evaluation and approval processes in the digital environment."}, "financialFlexibility": {"title": "Financial Flexibility", "description": "You can achieve better liquidity in your financial statements with long-term rental."}, "riskManagement": {"title": "Risk Management", "description": "You can transfer risks such as vehicle depreciation and unexpected costs to the rental company."}, "cta": "Get a Quote Now"}, "hero": {"title": "Next Generation Car Rental!", "subtitle": "Start an auction, find the best price, rent a car.", "buttonText": "Start Auction", "customer": {"title": "Welcome {{name}}", "subtitle": "Find the best price, rent a car.", "buttonText": "Start Auction"}, "company": {"title": "Welcome, {{name}}", "subtitle": "Track long-term car rental tenders, manage your offers, and control your fleet.", "description": "Track long-term car rental tenders, manage your offers, and control your fleet."}, "visitor": {"title": "Don't Buy Your Car, Rent It Long-Term!", "subtitle": "Get price quotes from reliable car rental companies registered on our platform with a single request, completely online without leaving your office. Focus on your business with financial and operational advantages, don't deal with vehicle details.", "aboutButton": "More Information", "registerButton": "Register Now"}}, "header-navigation": {"home": "Home", "about": "About", "service": "Services", "all_service": "All Services", "service_details": "Service Details", "cars": "Cars", "car_listing": "Car Listing", "car_booking": "Car Booking", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register", "profile": "My Profile", "user": "User", "logout": "Logout", "my_tenders": "My Tenders", "bookings": "My Bookings"}, "promo": {"title": "New Generation Car Rental with Tender Rental", "subtitle": "Learn how tender rental works", "cta_button": "Detailed Information", "advantage_1_title": "Financial Benefit", "advantage_1_text": "Invest your capital in your business instead of buying vehicles, and better manage your budget with fixed rental payment plans.", "advantage_2_title": "Operational Ease", "advantage_2_text": "All processes such as vehicle taxes, insurance, and maintenance are managed by the rental company, you just use the vehicle.", "advantage_3_title": "Fully Digital Process", "advantage_3_text": "Start a tender completely online without leaving your office, and select the most suitable offer.", "advantage_4_title": "Tax Advantage", "advantage_4_text": "You can show the entire VAT amount as a deduction in vehicle rental expenses.", "advantage_5_title": "Wide Vehicle Selection", "advantage_5_text": "Choose the most suitable one for your needs from vehicles of different brands, models, and segments.", "advantage_6_title": "Transparent Pricing", "advantage_6_text": "Through offers from multiple suppliers on our platform, you reach the most competitive prices in the market."}, "findcar": {"brand": "Brand", "model": "Model", "fuel_type": "Fuel Type", "transmission": "Transmission", "condition": "Vehicle Condition", "rental_period": "Rental Period", "month": "Month", "select": "Select", "search": "SEARCH VEHICLE", "searching": "Searching..."}, "hero_slide_subtitle": "for rent $70 per day", "hero_slide_title": "Reserved Now & Get 50% Off", "search_best_car": "Search Your Best Cars here.", "from_address": "From Address", "to_address": "To Address", "SelectCar": "Select Car", "ac_car": "AC Car", "non_ac_car": "Non AC Car", "journey_date": "Journey Date", "journey_time": "Journey Time", "find_car": "find car", "about_us_title": "About Us", "welcome_title": "Welcome to Ren<PERSON> from Tender", "about_text": "At Rent from Tender, we bring an innovative approach to the long-term car rental sector, offering individual companies and professionals a practical and transparent rental experience in a completely digital environment. With a single request, we help you find the most suitable option by receiving price offers from trusted car rental companies registered on our platform.", "trusted_name": "We are a trusted platform", "deal_brands": "We offer numerous brands and models", "larger_stocks": "We have a wide supplier network", "worldwide_location": "We provide services across Turkey", "president": "President", "see_our": "see our", "latest_service": "Latest Services", "city_transfer": "City transfer", "air_transfer": "Air Transfer", "hospital_transfer": "Hospital transfer", "wedding_ceremony": "wedding ceremony", "city_tour": "Whole City Tour", "baggage_transport": "Baggage transport", "promo_text": "Do You Want To Earn With Us? So Don't be Late.", "promo_btn": "Become a driver", "day": "Day", "model": "Model", "rental": "rental", "rating": "rating", "automatic": "Automatic", "rent_car": "Rent Car", "details": "Details", "come_with": "Come with", "hot_offers": "Hot offers", "some_words": "Some words", "testimonial": "testimonial", "customer": "Customer", "experts": "experts", "our_members": "our Members", "year_experience": "year experience", "more_member": "More Members", "partner_location_text": "With Over {{partner_count}} Partners Locations", "need_any_help": "need any help?", "latest": "latest", "our_blog": "our blog", "btn_gallery": "More Pictures", "key_words": "Keywords...", "by_category": "By Category", "top_selling": "top selling", "price": "Price", "quantity": "Quantity", "add_to_cart": "add to cart", "products": "products", "related_products": "related products", "comments": "Comments", "leave_comment": "Leave a comment", "post_comment": "post comment", "must_signin": "Your must sing-in to make or comment a post", "c_name": "Name", "c_email": "Email", "blog-title": {"blog-1": "if Your Car's bettery down.", "blog-2": "How often is a taxi used?", "blog-3": "The best ways to pay Drivers"}, "footer": {"head_office": "Head office", "phone": "Phone", "email": "Email", "office_time": "Office Time", "quick_links": "Quick Links", "about_us": "About us", "our_service": "Our Services", "case_studies": "Case Studies", "contact_us": "Contact Us", "testimonials": "Testimonials", "privacy": "Privacy Policy", "latest_news": "latest News", "newsletter": "newsletter", "recent_post": "Recent post"}, "about_page": {"subtitle": "About Us", "title": "Your Digital Partner in Long-Term Car Rental", "proud_title": "We are proud of our business. <span>Start a Tender Now!</span>", "how_it_works_title": "How Rent from Tender Works?", "how_it_works_subtitle": "Long-Term Car Rental in 4 Steps", "step_1_title": "Register and Start a Tender", "step_1_text": "Register on our platform, upload the required documents (Findeks, tax plate), and start a tender by specifying your vehicle preferences.", "step_2_title": "Receive Offers", "step_2_text": "Receive offers from rental companies on our platform within 12 hours. Each supplier submits price quotes according to the criteria you set.", "step_3_title": "<PERSON><PERSON> the Best Offer", "step_3_text": "Evaluate the offers within 24 hours and choose the offer that best suits you. When you approve the offer, you can see the rental company's information.", "step_4_title": "Contract and Delivery", "step_4_text": "Complete the contract process with your chosen supplier and receive your vehicle. The entire process is smoothly conducted through our digital platform."}, "service_details_page": {"discount_text": "40% off only for new members", "reserve": "Reserve Now!", "download": "download", "faq_text": "Get more information"}, "car_booking": {"personal_information": "Personal Information", "first_name": "Your Full Name", "last_name": "Your Last Name", "email": "Your Email Address", "phn": "Your Phone Number", "booking_details": "Booking Details", "payment_method": "payment Method", "bank_transfer": "Direct Bank Transfer", "check_payment": "Cheque Payment", "credit_card": "Credit Card", "payment_text": "Make your payment directly into our bank account. Please use your Order ID as the payment reference.order won't be shipped until the funds have cleared.", "car_model": "Mercedes S-class", "car_price": "$50.00", "car_description": "This stylish Mercedes S-class offers premium comfort for your long journeys. With modern technology features and spacious interior, your travels will be even more enjoyable.", "fuel_consumption": "20kmpl", "engine_type": "V-6 Cylinder"}, "cart": {"shopping_cart": "Shopping Cart", "preview": "Preview", "product": "Product", "price": "Price", "quantity": "Quantity", "total": "Total", "action": "Action", "clear_cart": "clear cart", "update_cart": "update cart", "summury": "Order Summury", "subtotal": "Cart Subtotal", "shipping": "Shipping and Handling", "free_shipping": "Free Shipping", "order_total": "Order Total", "proceed": "Proceed to checkout"}, "checkout_page": {"billing_details": "Billing Details", "f_name": "First Name", "l_name": "Last Name", "country": "Country", "address": "Address", "town": "Town / City", "email": "Email Address", "mobile": "Mobile Number", "note": "Order Note"}, "blog_page": {"read_more": "read more", "comments": "Comments", "page": "Page", "by_category": "By Category", "by_tags": "By Tags", "recent_post": "Recent post", "archive": "Archive"}, "error_page": {"back_home": "back to home", "error": "Page Not Found", "desc": "The page you are looking for might have been removed had its name changed or temporarily unavailable."}, "login_page": {"singin": "sign in", "user_email": "Username or Email", "password": "Password", "f_password": "forgot password?", "keep": "Keep Me Signed In", "btn": "Login Now!", "need_account": "Do you need an account?", "loading": "Loading..."}, "register_page": {"singup": "sign Up", "username": "Username", "email": "Email", "password": "Password", "c_password": "Confirm Password", "terms": "accept terms & condition", "register_now": "Register now", "have_account": "Already have an account?"}, "contact_page": {"get_touch": "Get in touch", "name": "Your Name", "email": "Email Address", "subject": "Subject", "phone": "Phone Number", "msg": "Write your message here...", "send": "Send Message", "info_title": "Contact information", "info_email": "Email Us", "info_call": "Call us", "info_follow": "Follow us:"}, "tenders_page": {"title": "My Tenders", "subtitle": "My Tender List", "ihale_main_title": "My Tenders", "user_subtitle": "User", "empty_tenders": "No Tenders Found Yet", "empty_message": "You can simplify your vehicle rental process by starting a new tender.", "start_new_tender": "Start New Tender", "duration": "Rental Period", "month": "Month", "tender_info": "Tender Information", "tender_date": "Tender Date", "tender_state": "Status", "actions": "Actions", "details": "Details", "edit": "Edit", "tender_count": "tenders listed", "state_filter": "Status Filter", "all": "All", "active": "Active", "completed": "Completed", "cancelled": "Cancelled", "properties": "Properties", "offers": "Offers", "loading": "Loading...", "search": "Search", "search_placeholder": "Search tenders, brand or model...", "sort_by": "Sort By", "newest": "Newest", "oldest": "Oldest", "most_offers": "Most Offers", "specs": "Specifications", "start_date": "Start Date", "start_tender": "Start Tender", "vehicle_count": "Vehicle Quantity", "minimum_offer": "Minimum Offer", "no_offer": "No Offer", "end_date": "End Date", "no_image": "No Image", "status": "Status", "brand": "Brand", "model": "Model", "tender_status": "Tender Status", "ascending": "Ascending", "descending": "Descending", "default_sort": "<PERSON><PERSON><PERSON>", "sort_start_date": "By Start Date", "sort_offer_count": "By Offer Count", "sort_minimum_offer": "By Minimum Offer", "draft": "Draft", "review": "Review", "live": "Live", "relive": "Relive", "reject": "Rejected", "expired": "Expired"}, "tender_details": {"title": "Tender Details", "subtitle": "View Tender Information", "back": "Go Back", "loading": "Loading tender details...", "fuel_type": "Fuel Type", "transmission": "Transmission", "rental_period": "Rental Period", "month": "Month", "tender_period": "Tender Period", "start_date": "Start Date", "end_date": "End Date", "description": "Description", "mock_description": "This vehicle tender was created to meet your long-term corporate vehicle rental needs. During the tender process, offers are collected from different rental companies based on the selected vehicle model and features. During the tender period, you can review the offers submitted by companies and choose the one that suits you best.", "company_responses": "Company Offers", "mock_company_name": "Rental Company", "mock_location": "Istanbul, Turkey", "view_details": "View Details", "accept_offer": "Accept Offer", "no_company_responses": "There are no companies that have responded to this tender yet.", "tender_summary": "Tender Summary", "tender_id": "Tender No", "state": "Status", "created_by": "Created By", "user": "User", "remaining_time": "Remaining Time", "complete_tender": "Complete Tender", "cancel_tender": "<PERSON>cel <PERSON>der", "completed_message": "This tender has been successfully completed. You can view the selected offer details.", "cancelled_message": "This tender has been cancelled. You can try again by starting a new tender.", "tender_not_found": "Tender not found or you don't have permission to view this tender.", "expired": "Expired", "days": "days", "hours": "hours", "accepted_offer_title": "Accepted Offer", "accepted_on": "Acceptance Date", "accepted_price": "Accepted Price", "contract_details": "Contract Details", "completed_with_offer_message": "This tender has been completed and an offer has been accepted. You can see the details of the accepted offer above.", "offer_comparison": "Offer Comparison", "comparison_text": "Below is a price comparison of the received offers. You can view the details of each offer by clicking the 'View Details' button for the respective company.", "lowest_price": "Lowest Price", "highest_price": "Highest Price", "average_price": "Average Price", "tender_process": "Tender Process", "process_step1_title": "Tender Initiated", "process_step1_desc": "The tender was successfully initiated and notifications were sent to companies.", "process_step2_title": "Collecting Offers", "process_step2_desc": "Offers are being collected from companies. Offers may be updated during the tender period.", "process_step3_title": "Offer Evaluation", "process_step3_desc": "You can evaluate the offers and choose the one that suits you best.", "company_details": "Company Details", "reviews": "reviews", "contact_info": "Contact Information", "company_type": "Company Type", "company_founded": "Founded Year", "rental_terms": "Rental Terms", "tenders_participated": "Tenders Participated", "tenders_won": "Tenders Won", "usage_status": "Usage Status", "body_type": "Body Type", "km_limit": "KM Limit", "city": "City", "usage_purpose": "Usage Purpose", "declaration_limit": "Declaration Limit", "maintenance_responsible": "Maintenance Responsible", "has_winter_tire": "Has Winter Tire?", "user_and_company": "User and Company Information", "username": "Username", "company_name": "Company Name", "history_panel_title": "History Log", "minutes": "minutes", "seconds": "seconds", "not_started": "Not Started Yet"}, "my_bookings": {"title": "My Bookings", "subtitle": "My Vehicle Booking History", "empty_title": "You don't have any bookings yet.", "empty_message": "You can browse our vehicle list to make a car reservation.", "view_cars": "View Vehicles", "vehicle": "Vehicle", "date_range": "Date Range", "route": "Route", "amount": "Amount", "status": "Status", "actions": "Actions", "status_active": "Active", "status_completed": "Completed", "status_cancelled": "Cancelled", "view_details": "View Details", "cancel_booking": "Cancel Booking"}, "profile_page": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Your Account Information", "customer_subtitle": "Customer Account Information", "supplier_subtitle": "Supplier Account Information", "admin_subtitle": "Administrator Account Information", "user_profile": "User Profile", "user_info": "User Information", "supplier_info": "Supplier Information", "admin_info": "Admin Information", "verified": "Verified", "not_verified": "Not Verified", "account_status": "Account Status", "account_created": "Account Created Date", "last_login": "Last Login Date", "personal_info": "Personal Information", "personal_info_desc": "Update your personal information. Your email address cannot be changed as it is your account identifier.", "business_info": "Business Information", "business_info_desc": "You can update your company and tax information.", "address_info": "Address Information", "address_title": "Address Information", "security": "Security", "security_info": "You can manage your security settings and account verification information here.", "first_name": "First Name", "last_name": "Last Name", "name": "Your Name", "email": "Email", "email_info": "Your email address cannot be changed.", "phone": "Phone", "phone_info": "Enter a phone number you actively use.", "address": "Address", "city": "City", "district": "District", "birth_date": "Birth Date", "company_name": "Company Name", "tax_number": "Tax Number", "tax_number_info": "Tax number must be 10 or 11 digits.", "tax_office": "Tax Office", "tax_office_info": "Enter the name of your tax office.", "company_phone": "Company Phone", "company_phone_info": "Your company's contact phone number.", "company_email": "Company Email", "company_email_info": "Your company's corporate email address.", "title_info": "Enter your company title.", "edit": "Edit", "cancel": "Cancel", "save_changes": "Save Changes", "saving": "Saving...", "account_actions": "Account Actions", "my_bookings": "My Bookings", "favorites": "Favorites", "change_password": "Change Password", "password_description": "For your security, it is recommended to change your password regularly.", "current_password": "Current Password", "current_password_info": "Enter your current password for account security.", "new_password": "New Password", "password_requirements": "Your password must be at least 8 characters and include uppercase, lowercase letters, and numbers.", "confirm_password": "Confirm Password", "password_changed": "Password Changed", "password_change_success": "Your password has been successfully changed.", "delete_account": "Delete Account", "delete_account_description": "Contact customer service to delete your account.", "contact_support": "Contact Support", "verification_required": "Your account is not verified yet. You need to complete the verification process.", "verify_now": "Verify Now", "verify_account": "Verify Account", "profile_title": "Profile Information", "supplier_profile_info": "You can manage your supplier account details and company information here.", "verified_account": "Verified Account", "verification_title": "Account Verification", "updateSuccess": "Profile information successfully updated.", "updateError": "An error occurred while updating profile information.", "email_cannot_change": "Email address cannot be changed.", "verification_status": "Verification Status", "account_type": "Account Type", "security_title": "Account Security", "edit_profile": "Edit Profile", "userNotFound": "User not found"}, "verification_page": {"title": "Account Verification", "subtitle": "Account Verification Process", "verification_process": "Verification Process", "why_verify": "Why is Account Verification Required?", "verification_description": "You need to verify your account to perform transactions on our platform. This process is necessary to provide a secure environment and prevent fraud.", "tax_document": "Tax Certificate", "tax_document_description": "Please upload your current tax certificate in PDF format. It's important that the document is clear and legible.", "select_tax_document": "Select Tax Certificate", "pdf_only": "Only PDF files are accepted.", "file_too_large": "File size must be less than 5MB.", "tax_document_required": "Tax certificate is required.", "findex_report": "Findeks Report", "findex_description": "Your Findeks report will be used to assess your creditworthiness. By giving your consent below, you authorize a Findeks inquiry.", "findex_warning": "If you consent, our system will automatically perform a Findeks inquiry process. Your credit score will be queried for this process.", "findex_consent": "I authorize <PERSON><PERSON><PERSON> inquiry and agree to have my credit score queried.", "findex_check_required": "Findeks consent is required.", "submit_verification": "Submit Verification", "processing": "Processing...", "back_to_profile": "Back to Profile", "already_verified": "Your Account is Already Verified", "verified_account_description": "Your account has been successfully verified. You can now use all the features on our platform.", "success_message": "Verification process completed successfully. Your account has been approved."}, "seo": {"default_title": "Long Term Car Rental | LongTermRental", "default_description": "Long term car rental, fleet rental and car tender system. Find the best priced vehicles.", "default_keywords": "long term car rental, fleet rental, car rental, tender system", "og_title": "Long Term Car Rental Platform | LongTermRental", "og_description": "Start long term car rental tenders with our corporate car rental platform, evaluate offers and rent a car at the best price.", "home_title": "Home | Long Term Car Rental", "home_description": "Long term car rental and tender system for corporate companies. Rent your car with competitive offers.", "home_keywords": "long term car rental, corporate car rental, car tender, fleet rental", "about_title": "About Us | Long Term Car Rental", "about_description": "Information about our corporate long term car rental platform and our services.", "about_keywords": "about us, long term car rental, fleet rental, corporate services", "contact_title": "Contact | Long Term Car Rental", "contact_description": "Contact our long term car rental platform. Our address, phone number and contact form.", "contact_keywords": "contact, car rental, customer service, support", "cars_title": "Car List | Long Term Car Rental", "cars_description": "View, filter and find the most suitable cars for long term rental.", "cars_keywords": "car list, long term car rental, fleet vehicles, corporate vehicles", "service_title": "Our Services | Long Term Car Rental", "service_description": "Services and advantages offered by the long term car rental platform.", "service_keywords": "car rental services, fleet management, maintenance services, insurance", "login_title": "Login | Long Term Car Rental", "login_description": "Log in to your account and take advantage of the long term car rental platform.", "login_keywords": "login, account, member login, car rental", "register_title": "Register | Long Term Car Rental", "register_description": "Register to the long term car rental platform and take advantage of the benefits.", "register_keywords": "register, new account, membership, car rental", "profile_title": "My Profile | Long Term Car Rental", "profile_description": "View and edit your profile information, track your reservations.", "profile_keywords": "profile, account management, personal information, car rental account", "error_title": "Page Not Found | Long Term Car Rental", "error_description": "The page you are looking for could not be found. Return to the home page or get help.", "error_keywords": "error, 404, page not found, error page"}, "tender_process_detail": {"page_title": "Tender Process", "page_subtitle": "Detailed Information", "main_title": "Our Tender Process", "main_description": "In our long-term vehicle rental service, we complete the tender process in 4 basic steps. You can find a detailed explanation of each step below.", "step1_title": "Price Quote Request", "step1_description": "You can start the process by creating your tender request through our online platform. Completing the form on our platform completely is important for our companies to offer you the most suitable offer. Submit your request by specifying the vehicle type, quantity, duration and other important criteria in the form.", "step1_feature1": "Automatic notification is sent to all suppliers at the time of request", "step1_feature2": "Offers are prepared according to the specifications you indicate in the request form", "step1_feature3": "The offer period begins as soon as your request enters the system", "step2_title": "Suppliers' Offer Process", "step2_description": "After your request is approved, supplier companies enter their most competitive offers into the system <strong>within 12 hours</strong> according to your request. During this period, the preparation and presentation of offers to you is completed.", "step2_note_title": "Offer Period: 12 Hours", "step2_note_description": "The 12-hour period granted to suppliers allows company representatives to determine the best price for the vehicle you requested and enter it into the system. During this period, your request appears in \"Pending Quote Request\" status.", "step3_title": "Customer's Offer Evaluation Process", "step3_description": "After all offers are entered into the system, you have <strong>24 hours</strong> for your evaluation. During this period, you can compare the received offers and choose the one that best suits you.", "phases_title": "Offer Process Phases:", "phase1_title": "Request Quote", "phase1_description": "In the first phase, you create your quote request.", "phase2_title": "Pending Quote Request", "phase2_description": "Your request waits in this status while suppliers prepare their offers.", "phase3_title": "Incoming Price Quote", "phase3_description": "Offers from suppliers are displayed and compared at this stage.", "phase4_title": "Approved Offer", "phase4_description": "The offer you select transitions to this status and processes are initiated.", "supplier_info_title": "Visibility of Supplier Information", "supplier_info_feature1": "You cannot see supplier company names during the evaluation process", "supplier_info_feature2": "Suppliers are evaluated with a star rating system (number of vehicles, model diversity, etc.)", "supplier_info_feature3": "Supplier information becomes visible when you select an offer", "step4_title": "Finalization Process", "step4_description": "When you select the offer that suits you, the system automatically sends notification to the relevant supplier company. After this stage, the contract process begins and the supplier company contacts you.", "step4_feature1": "The offer you selected is immediately communicated to the supplier company", "step4_feature2": "A representative from the supplier company contacts you", "step4_feature3": "Contract details and vehicle delivery process are planned", "step4_feature4": "When the tender is concluded, it transitions to \"Approved\" status in the system", "help_title": "Still Have Questions?", "help_description": "You can contact our customer service to get more information about the tender process or to send your questions.", "contact_button": "Contact Us"}, "tender_process": {"title": "Tender Process", "step1_title": "Price Quote Request", "step1_description": "Start the process by creating your tender request. The request is forwarded to all suppliers on the platform.", "step2_title": "Suppliers' Offer Process", "step2_description": "Suppliers submit their offers to the system within <strong>12 hours</strong>. It is important to be patient during this process.", "step3_title": "Offer Evaluation", "step3_description": "You can evaluate the received offers within <strong>24 hours</strong>. Supplier names are kept confidential, only their star ratings are shown.", "step4_title": "Finalization", "step4_description": "When you select the offer that suits you, you are connected with the relevant supplier and proceed to the contract phase."}, "tender_start": {"page_title": "Tender Operations", "page_subtitle": "Start and Manage Tender", "alert_title": "Attention!", "alert_message": "You need to log in to start the tender. If you do not log in, your tender record will be stored temporarily.", "tab_new_tender": "Start New Tender", "tab_my_tenders": "My Tenders", "card_title": "Tender Information", "info_button_aria": "Information about the tender process", "intro_text": "Please fill in all the necessary information completely to create a tender. Fill in all fields as much as possible in detail so that companies can offer you the most suitable offer.", "usage_purpose": "Usage Purpose", "vehicle_condition": "Vehicle Condition", "transmission": "Transmission Type", "fuel_type": "Fuel Type", "body_type": "Body Type", "delivery_city": "Delivery City", "model_year": "Model Year", "quantity": "Quantity", "brand": "Brand", "model": "Model", "payment_method": "Payment Method", "declaration_limit": "Declaration Limit Option", "mileage_limit": "Mileage Limit (Yearly)", "rental_period": "Rental Period", "maintenance_responsible": "Maintenance Responsible", "has_winter_tire": "I want Winter Tire", "select": "Select", "save_info": "Save Information", "start_tender": "Start Tender", "user_id_note": "Note: If the session is not open or there is no user information, userId is set to null and all tenders are displayed.", "save_success_message": "Tender information successfully saved. You can click the button below to start the tender.", "start_success_message": "Tender start process successfully completed!", "continue": "Continue", "back": "Back", "continue_summary": "Continue to Summary", "summary_title": "Tender Summary", "vehicle_info": "Vehicle Information", "rental_info": "Rental Information", "tender_rules": "Tender Rules", "rules_content": "Before starting the tender, please carefully read the following rules: The tender duration is 12 hours. During this time, suppliers will submit their offers. After starting the tender, you will have 24 hours to evaluate the incoming offers. During the tender process, the identities of suppliers will be kept confidential. You will only see suppliers with their performance scores. When you accept an offer, the supplier information will be shared with you and the contract phase will begin.", "accept_rules": "I accept the tender rules", "year": "Year", "step_form_title": "Vehicle Information", "step_form_desc": "Enter vehicle information for the tender", "step_details_title": "Vehicle Details", "step_details_desc": "Review vehicle details and companies", "step_summary_title": "Summary and Confirmation", "step_summary_desc": "Check and confirm tender information"}, "tender_start_details": {"marka": "Brand", "model": "Model", "model_yili": "Model Year", "vites": "Transmission", "yakit_tipi": "Fuel Type", "kiralama_suresi": "Rental Period", "sehir": "City", "ilce": "District", "ay": "Month", "gorsel_yok": "No Image Available", "ihale": "<PERSON>der", "fiyat_ihale_sonucu": "Price Will Be Determined By Tender Result", "ihale_aciklamasi": "Tender Description", "ihale_aciklamasi_metin": "month long-term vehicle rental tender", "body_type": "Body Type"}, "button": {"book_now": "Book Now"}, "pagination": {"showing": "Showing", "of": "of", "items": "items", "items_per_page": "Items per page"}, "validation": {"password_required": "Current password is required", "new_password_required": "New password is required", "password_length": "Password must be between 8-20 characters", "password_complexity": "Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character", "passwords_not_match": "Passwords do not match", "required_field": "This field is required", "invalid_email": "Please enter a valid email address", "min_length": "This field must be at least {{min}} characters long", "max_length": "This field must be no more than {{max}} characters long"}, "available_tenders_page": {"title": "Available Tenders", "subtitle": "View tenders created by other users and place your bid.", "search": "Search", "search_placeholder": "Search tenders, brand or model...", "sort_by": "Sort By", "sort_created_date": "By Created Date", "sort_start_date": "By Start Date", "sort_offer_count": "By Offer Count", "ascending": "Ascending", "descending": "Descending", "details": "Details", "bid": "Place Bid", "vehicle_count": "Vehicle Quantity", "offers": "Offers", "start_date": "Start Date", "end_date": "End Date", "empty_tenders": "No Available Tenders", "empty_message": "There are no available tenders at the moment. Please check again later."}, "tender_bid_page": {"title": "Place Bid", "subtitle": "Submit your bid for this tender.", "fuel_type": "Fuel Type", "gear_type": "Gear Type", "usage_status": "Usage Status", "body_type": "Body Type", "km_limit": "KM Limit", "city": "City", "rental_period": "Rental Period", "declaration_limit": "Declaration Limit", "maintenance_responsible": "Maintenance Responsible", "has_winter_tire": "Winter Tire", "start_date": "Start Date", "end_date": "End Date", "bid_form_title": "Create Your Bid", "bid_amount": "<PERSON><PERSON> (₺)", "note": "Note (optional)", "submit_bid": "Submit Bid", "success_message": "Your bid has been submitted successfully!", "vehicle_year": "Vehicle Year", "vehicle_km": "Vehicle KM", "add_bid_row": "Add Bid Row"}}