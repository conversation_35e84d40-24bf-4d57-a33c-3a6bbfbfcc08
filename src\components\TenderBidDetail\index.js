import React, { useState } from 'react';
import { Container, Tab, Nav, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { submitBid } from '../../services/tenderBidService';
import PropTypes from 'prop-types';

// Bileşenler
import TenderHeader from './components/TenderHeader';
import TenderTabs from './components/TenderTabs';
import TenderDetailContent from './components/TenderDetailContent';
import TenderVehicleList from './TenderVehicleList';
import TenderDocumentList from './TenderDocumentList';
import TenderBidForm from './TenderBidForm';
import MultipleBidManager from './components/MultipleBidManager';

// Hook'lar
import useTenderData from './hooks/useTenderData';

// Stiller
import './style.css';

/**
 * İhale detayların<PERSON>ö<PERSON>ü<PERSON>üleyen ve teklif oluşturma/güncelleme imkanı sunan bileşen
 * Bu bileşen, müşteri tarafından oluşturulan bir ihaleyi görüntüler ve servis sağlayıcılara teklif oluşturma formu sunar
 */
const TenderBidDetail = ({ tenderId }) => {
  const params = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const id = tenderId || params.id;
  const { tender, company, userBid, isLoading, error } = useTenderData(id);

  const [activeTab, setActiveTab] = useState('details');
  const [bidSubmitted, setBidSubmitted] = useState(false);

  // Teklif gönderme işlemi
  const handleBidSubmitted = (bidData) => {
    submitBid(id, bidData)
      .then(response => {
        if (response.success) {
          setBidSubmitted(true);
          alert(t('Teklifiniz başarıyla oluşturuldu!'));
        } else {
          alert(t('Teklif gönderilirken bir hata oluştu: ') + response.message);
        }
      })
      .catch(error => {
        console.error("Teklif gönderme hatası:", error);
        alert(t('Teklif gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.'));
      });
  };

  // Teklifi görüntüle
  const handleViewBid = (bid) => {
    navigate(`/bids/${bid.id}`);
  };

  // Geri dön
  const handleBack = () => {
    navigate('/tenders');
  };

  // Sekmeyi değiştir
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  return (
    <div className="tender-bid-detail">
      <Container>
        <div className="compact-container">
          {isLoading ? (
        <div className="text-center py-3">
          <Spinner animation="border" role="status" variant="primary" />
          <p className="mt-2">{t('common.loading')}</p>
        </div>
      ) : error ? (
        <Alert variant="danger">
          <Alert.Heading>{t('common.error')}</Alert.Heading>
          <p>{error}</p>
          <hr />
          <div className="mt-2">
            <Button variant="outline-danger" onClick={handleBack} className="me-2" size="sm">
              <i className="bi bi-arrow-left me-1"></i>
              {t('common.go_back')}
            </Button>
            <Button variant="primary" onClick={() => window.location.reload()} size="sm">
              <i className="bi bi-arrow-clockwise me-1"></i>
              Sayfayı Yenile
            </Button>
          </div>
        </Alert>
      ) : !tender ? (
        <Alert variant="warning">
          <Alert.Heading>Veri bulunamadı</Alert.Heading>
          <p>İhale verisi yüklenemedi. Lütfen sayfayı yenileyin veya geçerli bir ihale ID'si girdiğinizden emin olun.</p>
          <div className="mt-2">
            <Button variant="primary" onClick={() => window.location.reload()} className="me-2" size="sm">
              <i className="bi bi-arrow-clockwise me-1"></i>
              Sayfayı Yenile
            </Button>
            <Button variant="outline-primary" onClick={handleBack} size="sm">
              <i className="bi bi-arrow-left me-1"></i>
              İhalelere Dön
            </Button>
          </div>
        </Alert>
      ) : (
        <div className="tender-compact-container">
          {/* İhale Başlık Alanı */}
          <TenderHeader tender={tender} />

          {/* Sekme Menüsü */}
          <div className="tender-simple-tabs">
            <Tab.Container id="tender-tabs" activeKey={activeTab} onSelect={handleTabChange}>
              <TenderTabs
                activeKey={activeTab}
                onSelect={handleTabChange}
                tender={tender}
              />

              <div className="tender-compact-body">
                <Tab.Content>
                  {/* Detay Sekmesi */}
                  <Tab.Pane eventKey="details">
                    <TenderDetailContent
                      tender={tender}
                      company={company}
                      onTabChange={handleTabChange}
                    />
                  </Tab.Pane>

                  {/* Araçlar Sekmesi */}
                  <Tab.Pane eventKey="vehicles">
                    <TenderVehicleList vehicles={tender?.requiredVehicles || []} />
                  </Tab.Pane>

                  {/* Dokümanlar Sekmesi */}
                  <Tab.Pane eventKey="documents">
                    <TenderDocumentList documents={tender?.documents || []} />
                  </Tab.Pane>

                  {/* Teklif Oluştur Sekmesi */}
                  <Tab.Pane eventKey="createBid">
                    {bidSubmitted ? (
                      <div className="text-center p-3">
                        <div className="mb-3">
                          <i className="fa fa-check-circle text-success" style={{ fontSize: '36px' }}></i>
                        </div>
                        <h4 className="mb-2">Teklifiniz Başarıyla Gönderildi!</h4>
                        <p>Teklifiniz şu anda değerlendirme aşamasındadır.
                          İlgili müşteri sizinle irtibata geçecektir.</p>
                        <button
                          className="btn btn-primary mt-2"
                          onClick={() => {
                            setBidSubmitted(false);
                            navigate(`/tender-bids/${tender?.id}`);
                          }}
                        >
                          İhale Sayfasına Dön
                        </button>
                      </div>
                    ) : (
                      <div className="bid-form-container mt-3">
                        <MultipleBidManager
                          tender={{
                            ...tender,
                            company: company || {},
                            customer: tender.customer
                          }}
                          existingBid={userBid}
                          onBidSubmitted={handleBidSubmitted}
                        />
                      </div>
                    )}
                  </Tab.Pane>
                </Tab.Content>
              </div>
            </Tab.Container>
          </div>
        </div>
      )}
        </div>
      </Container>
    </div>
  );
};

// PropTypes tanımlamaları
TenderBidDetail.propTypes = {
  tenderId: PropTypes.string
};

export default TenderBidDetail;