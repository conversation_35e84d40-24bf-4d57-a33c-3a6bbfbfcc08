import api, { useMockAPI } from './api';
import { USER_ROLES } from '../constants/userRoles';

// ----- Gerçek API Çağrıları ----- //

// Giriş işlemi
const login = (credentials) => api.post('/Auth', credentials);

// Kayıt işlemi
const register = (userData) => api.post('/Auth/Register', userData);

// Çıkış işlemi
const logout = () => {
  try {
    // API'ye çıkış isteği gönder
    return api.post('/Auth/Logout');
  } catch (error) {
    // API çağrısı başarısız olsa bile localStorage'ı temizle
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    return Promise.resolve({ data: { success: true } });
  }
};

// Kullanıcı profil bilgilerini getirme
const getProfile = () => api.get('/Auth/Me');

// <PERSON><PERSON>re güncelleme
const updatePassword = (passwordData) => api.put('/Auth/Password', passwordData);

// Profil bilgilerini güncelleme
const updateProfile = (profileData) => api.put('/Auth/Profile', profileData);

// Kullanıcı hesabı doğrulama
const verifyAccount = (verificationData) => api.post('/Auth/Verify', verificationData);

// ----- Mock API Çağrıları ----- //

// Mock giriş işlevi
const mockLogin = (credentials) => {
  return new Promise((resolve, reject) => {
    // API çağrısını simüle etmek için gecikme
    setTimeout(() => {
      try {
        // Bazı doğrulama kontrolleri
        if (!credentials.email || !credentials.password) {
          return reject({ response: { data: { message: 'E-posta ve şifre gereklidir' } } });
        }

        // Sabit kullanıcılar tanımı
        const mockUsers = [
          {
            email: '<EMAIL>',
            password: 'customer123',
            id: 101,
            name: 'Test Müşteri',
            roleId: USER_ROLES.CUSTOMER.id,
            isVerified: true,
            createdAt: new Date().toISOString()
          },
          {
            email: '<EMAIL>',
            password: 'company123',
            id: 102,
            name: 'Test Şirket',
            roleId: USER_ROLES.CUSTOMER.id,
            isVerified: true,
            createdAt: new Date().toISOString()
          },
          {
            email: '<EMAIL>',
            password: 'admin123',
            id: 1,
            name: 'Sistem Yöneticisi',
            roleId: USER_ROLES.ADMIN.id,
            isVerified: true,
            createdAt: new Date().toISOString()
          }
        ];

        // Kullanıcıyı bul
        const user = mockUsers.find(user => user.email === credentials.email && user.password === credentials.password);

        if (!user) {
          return reject({ response: { data: { message: 'Geçersiz e-posta veya şifre. Tanımlı kullanıcılar: <EMAIL>/customer123, <EMAIL>/company123 veya <EMAIL>/admin123' } } });
        }

        // Kullanıcı bilgilerini şifre olmadan döndür
        const { password, ...userWithoutPassword } = user;

        // Başarılı yanıt
        resolve({
          data: {
            user: userWithoutPassword,
            token: 'mock-jwt-token-' + Math.random().toString(36).substring(2, 15),
          }
        });
      } catch (error) {
        reject({ response: { data: { message: 'Giriş işlemi başarısız oldu' } } });
      }
    }, 800); // 800ms gecikme
  });
};

// Mock kayıt işlevi
const mockRegister = (userData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Doğrulama kontrolleri
        if (!userData.email || !userData.password || !userData.name) {
          return reject({
            response: { data: { message: 'Tüm alanları doldurmanız gerekiyor' } }
          });
        }

        // Rol kontrolü, varsayılan CUSTOMER
        const roleId = userData.roleId || USER_ROLES.CUSTOMER.id;

        // Başarılı yanıt
        resolve({
          data: {
            user: {
              id: Math.floor(Math.random() * 1000) + 2,
              name: userData.name,
              email: userData.email,
              roleId: roleId,
              isVerified: false, // Yeni kayıt olanlarda doğrulama yapılmamış
              createdAt: new Date().toISOString(),
            },
            token: 'mock-jwt-token-' + Math.random().toString(36).substring(2, 15),
          }
        });
      } catch (error) {
        reject({ response: { data: { message: 'Kayıt işlemi başarısız oldu' } } });
      }
    }, 800);
  });
};

// Mock çıkış işlevi
const mockLogout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ data: { success: true } });
    }, 500);
  });
};

// Mock profil getirme işlevi
const mockGetProfile = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');

      if (!storedUser) {
        return reject({
          response: { data: { message: 'Kullanıcı bulunamadı' } }
        });
      }

      resolve({ data: JSON.parse(storedUser) });
    }, 600);
  });
};

// Mock şifre güncelleme işlevi
const mockUpdatePassword = (passwordData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (!passwordData.currentPassword || !passwordData.newPassword) {
        return reject({
          response: { data: { message: 'Tüm şifre alanları gereklidir' } }
        });
      }

      resolve({ data: { success: true, message: 'Şifre başarıyla güncellendi' } });
    }, 700);
  });
};

// Mock profil güncelleme işlevi
const mockUpdateProfile = (profileData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');

        if (!storedUser) {
          return reject({
            response: { data: { message: 'Kullanıcı bulunamadı' } }
          });
        }

        const updatedUser = { ...JSON.parse(storedUser), ...profileData };
        localStorage.setItem('user', JSON.stringify(updatedUser));

        resolve({ data: updatedUser });
      } catch (error) {
        reject({
          response: { data: { message: 'Profil güncellenirken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock hesap doğrulama işlevi
const mockVerifyAccount = (verificationData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        const storedUser = localStorage.getItem('user') || sessionStorage.getItem('user');

        if (!storedUser) {
          return reject({
            response: { data: { message: 'Kullanıcı bulunamadı' } }
          });
        }

        // Vergi levhası belge kontrolü
        if (!verificationData.taxDocument) {
          return reject({
            response: { data: { message: 'Vergi levhası yüklemesi gereklidir' } }
          });
        }

        // Başarılı doğrulama
        const user = JSON.parse(storedUser);
        const verifiedUser = {
          ...user,
          isVerified: true,
          verificationDate: new Date().toISOString(),
          verificationDocuments: {
            taxDocument: verificationData.taxDocument,
            findexStatus: 'verified'
          }
        };

        localStorage.setItem('user', JSON.stringify(verifiedUser));

        resolve({ data: verifiedUser });
      } catch (error) {
        reject({
          response: { data: { message: 'Doğrulama işlemi sırasında bir hata oluştu' } }
        });
      }
    }, 1000);
  });
};

// Kullanılacak servisleri belirleme (mock mu gerçek mi)
const authService = {
  login: useMockAPI ? mockLogin : login,
  register: useMockAPI ? mockRegister : register,
  logout: useMockAPI ? mockLogout : logout,
  getProfile: useMockAPI ? mockGetProfile : getProfile,
  updatePassword: useMockAPI ? mockUpdatePassword : updatePassword,
  updateProfile: useMockAPI ? mockUpdateProfile : updateProfile,
  verifyAccount: useMockAPI ? mockVerifyAccount : verifyAccount,
};

export default authService;