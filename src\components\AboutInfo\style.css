@import url('../../styles/colors.css');

/* ===================== ABOUT INFO ======================*/
:root {
  --app-grey: #f5f5f5;
}

.about-info-area {
  position: relative;
  padding: 70px 0;
}

.about-info-left h4 {
  font-size: 20px;
  color: var(--primary-color);
  margin-bottom: 10px;
  display: block;
  font-weight: 500;
  text-transform: capitalize;
  font-family: "Rubik", sans-serif;
}

.about-info-left h3 {
  font-size: 36px;
  color: #001238;
  letter-spacing: 1px;
  margin-bottom: 15px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: inline-block;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 45px;
}

.about-info-left p span {
  color: #111;
  text-transform: capitalize;
}

.about-info-left p {
  margin: 0 0 10px;
}

.about-list {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.about-list li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 25px;
  color: #565656;
}

.about-list li i {
  position: absolute;
  left: 0;
  top: 2px;
  color: var(--primary-color);
}

.about-page-call {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 30px;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.page-call-icon {
  font-size: 50px;
  margin-right: 30px;
  color: var(--primary-color);
}

.call-info p {
  text-transform: capitalize;
  color: var(--primary-color);
  font-size: 17px;
  margin-bottom: 5px;
}

.call-info h4 {
  color: #001238;
  font-size: 20px;
  letter-spacing: 2px;
}

.call-info h4 a {
  color: #001238;
}

.about-info-right img {
  max-width: 100%;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

@media (min-width: 768px) and (max-width: 991px) {
  .about-info-left h3 {
    font-size: 30px;
  }
  .about-info-right {
    margin-top: 30px;
  }
  .about-info-right img {
    width: 100%;
  }
}

@media (max-width: 767px) {
  .about-info-left h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .about-info-right {
    margin-top: 30px;
  }
} 