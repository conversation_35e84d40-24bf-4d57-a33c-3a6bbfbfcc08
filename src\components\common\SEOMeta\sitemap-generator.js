/**
 * Sitemap.xml oluşturucu
 * Build aşamasında çalıştırılarak otomatik sitemap.xml oluşturur
 */
const fs = require('fs');
const path = require('path');

// Site yapılandırması
const config = {
  siteUrl: 'https://www.ihaledenkirala.com',
  lastModified: new Date().toISOString(),
  changeFrequency: {
    home: 'daily',
    about: 'monthly',
    blog: 'weekly',
    blogPost: 'monthly',
    contact: 'monthly',
    service: 'weekly',
    cars: 'daily',
    tender: 'daily',
    other: 'monthly'
  },
  priority: {
    home: '1.0',
    about: '0.8',
    blog: '0.9',
    blogPost: '0.7',
    contact: '0.8',
    service: '0.9',
    cars: '0.9',
    tender: '0.9',
    other: '0.5'
  },
  languages: ['tr', 'en'], // Dil kodları
};

// Dinamik olarak sitemap için URL'leri oluşturur
const generateSitemapUrls = () => {
  // Statik sayfaların listesi
  const staticPages = [
    { url: '', type: 'home' },
    { url: 'about', type: 'about' },
    { url: 'contact', type: 'contact' },
    { url: 'service', type: 'service' },
    { url: 'car-listing', type: 'cars' },
    { url: 'blog', type: 'blog' },
    { url: 'tender-start', type: 'tender' },
  ];

  // Her dil için URL'leri oluştur
  let urls = [];
  config.languages.forEach(lang => {
    const langPrefix = lang === 'tr' ? '' : `/${lang}`;
    
    staticPages.forEach(page => {
      const pageUrl = page.url === '' ? '' : `/${page.url}`;
      urls.push({
        loc: `${config.siteUrl}${langPrefix}${pageUrl}`,
        lastmod: config.lastModified,
        changefreq: config.changeFrequency[page.type],
        priority: config.priority[page.type],
        alternates: config.languages.map(alternateLang => {
          const altLangPrefix = alternateLang === 'tr' ? '' : `/${alternateLang}`;
          return {
            hreflang: alternateLang === 'tr' ? 'tr-TR' : 'en-US',
            href: `${config.siteUrl}${altLangPrefix}${pageUrl}`
          };
        })
      });
    });
  });

  return urls;
};

// Sitemap XML formatını oluşturur
const generateSitemapXml = () => {
  const urls = generateSitemapUrls();
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n';
  xml += '  xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';
  
  urls.forEach(url => {
    xml += '  <url>\n';
    xml += `    <loc>${url.loc}</loc>\n`;
    xml += `    <lastmod>${url.lastmod}</lastmod>\n`;
    xml += `    <changefreq>${url.changefreq}</changefreq>\n`;
    xml += `    <priority>${url.priority}</priority>\n`;
    
    // Alternatif dil URL'leri
    url.alternates.forEach(alt => {
      xml += `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />\n`;
    });
    
    xml += '  </url>\n';
  });
  
  xml += '</urlset>';
  return xml;
};

// Sitemap.xml dosyasını oluştur
const createSitemapFile = () => {
  const sitemapContent = generateSitemapXml();
  const outputDir = path.join(process.cwd(), 'public');
  const outputPath = path.join(outputDir, 'sitemap.xml');
  
  try {
    fs.writeFileSync(outputPath, sitemapContent, 'utf8');
  } catch (err) {

  }
};

// Doğrudan çalıştırıldığında sitemap oluştur
if (require.main === module) {
  createSitemapFile();
}

module.exports = {
  createSitemapFile,
  generateSitemapXml
}; 