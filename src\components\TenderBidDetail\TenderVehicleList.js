import React from 'react';
import { Row, Col, Card } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';

/**
 * İhale için gerekli araçların listesini gösteren bileşen
 */
const TenderVehicleList = ({ vehicles = [] }) => {
  const { t } = useTranslation();

  // Araç özellik dizisini formatlayan yardımcı fonksiyon
  const formatFeatures = (features) => {
    if (!features || !Array.isArray(features) || features.length === 0) {
      return t("Özellik belirtilmemiş");
    }
    return features.join(', ');
  };

  return (
    <div className="vehicle-list">
      {vehicles.length === 0 ? (
        <div className="alert alert-info">
          {t("Bu ihale için herhangi bir araç talebi bulunmamaktadır.")}
        </div>
      ) : (
        <Row>
          {vehicles.map((vehicle, index) => (
            <Col md={6} lg={4} key={index} className="mb-4">
              <div className="vehicle-card">
                <div className="vehicle-header">
                  <h5 className="mb-0">{vehicle.type || t("Araç")} #{index + 1}</h5>
                </div>

                <div className="vehicle-image">
                  {vehicle.image ? (
                    <img src={vehicle.image} alt={vehicle.type} className="vehicle-img" />
                  ) : (
                    <div className="vehicle-placeholder">
                      <i className="bi bi-truck fs-1 text-muted"></i>
                      <p className="placeholder-text">{t('tender.vehicle_image_placeholder', 'Görsel Yok')}</p>
                    </div>
                  )}
                </div>

                <div className="vehicle-details">
                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Araç Tipi")}</div>
                    <div className="vehicle-info-value">{vehicle.type || t("Belirtilmemiş")}</div>
                  </div>

                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Marka")}</div>
                    <div className="vehicle-info-value">{vehicle.brand || t("Belirtilmemiş")}</div>
                  </div>

                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Model")}</div>
                    <div className="vehicle-info-value">{vehicle.model || t("Belirtilmemiş")}</div>
                  </div>

                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Yılı")}</div>
                    <div className="vehicle-info-value">{vehicle.year || t("Belirtilmemiş")}</div>
                  </div>

                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Kapasite")}</div>
                    <div className="vehicle-info-value">{vehicle.capacity || t("Belirtilmemiş")}</div>
                  </div>

                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Adet")}</div>
                    <div className="vehicle-info-value">{vehicle.quantity || 1}</div>
                  </div>

                  <div className="vehicle-info-item">
                    <div className="vehicle-info-label">{t("Özellikler")}</div>
                    <div className="vehicle-info-value">{formatFeatures(vehicle.features)}</div>
                  </div>

                  {vehicle.additionalRequirements && (
                    <div className="vehicle-info-item">
                      <div className="vehicle-info-label">{t("Ek Gereksinimler")}</div>
                      <div className="vehicle-info-value">{vehicle.additionalRequirements}</div>
                    </div>
                  )}
                </div>
              </div>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

export default TenderVehicleList;