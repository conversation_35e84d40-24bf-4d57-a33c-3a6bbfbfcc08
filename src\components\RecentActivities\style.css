.recent-activities-section {
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recent-activities-section .section-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.recent-activities-section .section-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  overflow-y: auto;
  flex-grow: 1;
  max-height: 300px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  flex-shrink: 0;
}

.activity-item:hover {
  background-color: #f8f9fa;
}

.activity-icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.activity-icon {
  font-size: 20px;
  color: #666;
}

.activity-icon.tender {
  color: #0066cc;
}

.activity-icon.bid {
  color: #ff6700;
}

.activity-icon.match {
  color: #43a047;
}

.activity-icon.alert {
  color: #f44336;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 0.95rem;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.activity-date {
  font-size: 0.8rem;
  color: #888;
}

.view-all-container {
  display: flex;
  justify-content: center;
  margin-top: 15px;
  flex-shrink: 0;
}

.view-all-container .view-all-button {
  background: none;
  border: none;
  color: #0066cc;
  font-size: 0.9rem;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-all-container .view-all-button:hover {
  background-color: #f0f7ff;
}

.loading-indicator,
.error-message,
.no-items-message {
  padding: 20px;
  text-align: center;
  color: #666;
  font-size: 0.9rem;
} 