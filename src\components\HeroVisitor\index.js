import React from "react";
import { useTranslation } from "react-i18next";
import { Col, Container, Row } from "react-bootstrap";
import { Link } from "react-router-dom";
import { 
  FaInfoCircle, 
  FaUserPlus, 
  FaMoneyBillWave,
  FaTools,
  FaLaptop,
  FaPercentage,
  FaArrowRight
} from "react-icons/fa";

import "./style.css";

const HeroVisitor = () => {
  const { t } = useTranslation();
  
  // Translation anahtarlarından metinleri al
  const heroTitle = t("hero.visitor.title");
  const heroDescription = t("hero.visitor.subtitle");
  const aboutButtonText = t("hero.visitor.aboutButton");
  const registerButtonText = t("hero.visitor.registerButton");

  // Promo metinlerinden feature textleri al
  const financialBenefit = t("promo.advantage_1_title");
  const operationalEase = t("promo.advantage_2_title");
  const fullyDigitalProcess = t("promo.advantage_3_title");
  const taxAdvantage = t("promo.advantage_4_title");

  const advantages = [
    { icon: <FaMoneyBillWave />, title: financialBenefit },
    { icon: <FaTools />, title: operationalEase },
    { icon: <FaLaptop />, title: fullyDigitalProcess },
    { icon: <FaPercentage />, title: taxAdvantage }
  ];
  
  // Butonlar için klavye işleyicisi
  const handleKeyDown = (e, path) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      window.location.href = path;
    }
  };
  
  // Avantaj kartı tıklama yönlendirmesi
  const handleCardClick = () => {
    window.location.href = '/about#advantages';
  };

  return (
    <section className="gauto-hero-area visitor-hero">
      <Container>
        <Row className="align-items-center">
          <Col lg={7} md={10} sm={12}>
            <div className="hero-content">
              <h1 className="hero-title">{heroTitle}</h1>
              <p className="hero-subtitle">{heroDescription}</p>
              
              {/* Hero butonları */}
              <div className="hero-buttons">
                <Link 
                  to="/about" 
                  className="gauto-btn-white" 
                  aria-label={aboutButtonText} 
                  tabIndex="0"
                  onKeyDown={(e) => handleKeyDown(e, '/about')}
                >
                  <FaInfoCircle className="me-2" />
                  {aboutButtonText}
                </Link>
                <Link 
                  to="/register" 
                  className="gauto-btn" 
                  aria-label={registerButtonText} 
                  tabIndex="0"
                  onKeyDown={(e) => handleKeyDown(e, '/register')}
                >
                  <FaUserPlus className="me-2" />
                  {registerButtonText}
                </Link>
              </div>
            </div>
          </Col>
          <Col lg={5} className="d-none d-lg-block">
            <div className="hero-image-container">
              <div className="hero-decoration"></div>
              <div className="hero-advantages-panel">
                <div className="hero-features-title">
                  <span className="hero-features-subtitle">{t("promo.subtitle")}</span>
                </div>
                
                <div className="advantages-grid">
                  {advantages.map((advantage, index) => (
                    <div 
                      className="advantage-card" 
                      key={index}
                      onClick={handleCardClick}
                      onKeyDown={(e) => e.key === 'Enter' && handleCardClick()}
                      tabIndex="0"
                      role="button"
                      aria-label={`Avantaj: ${advantage.title}`}
                    >
                      <div className="advantage-icon">
                        {advantage.icon}
                      </div>
                      <div className="advantage-title">
                        {advantage.title}
                      </div>
                    </div>
                  ))}
                </div>
                
                <a 
                  href="/ihale-sureci-detay" 
                  className="gauto-btn panel-cta-button"
                  aria-label={t("promo.cta_button")}
                  tabIndex="0"
                  onClick={(e) => {
                    e.preventDefault();
                    window.location.href = "/ihale-sureci-detay";
                  }}
                  onKeyDown={(e) => handleKeyDown(e, '/ihale-sureci-detay')}
                >
                  {t("promo.cta_button")}
                  <FaArrowRight className="ms-2" />
                </a>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default HeroVisitor; 