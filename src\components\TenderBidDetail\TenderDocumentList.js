import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Button } from 'react-bootstrap';
import { FaDownload, FaFileAlt, FaFilePdf, FaFileExcel, FaFileImage, FaFileWord } from 'react-icons/fa';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';

/**
 * <PERSON>hale dokümanlarını görüntüleyen bileşen
 */
const TenderDocumentList = ({ documents = [] }) => {
  const { t } = useTranslation();

  // Dosya türüne göre ikon seçen yardımcı fonksiyon
  const getFileIcon = (fileName) => {
    if (!fileName) return <FaFileAlt className="text-primary fs-3" />;
    
    const extension = fileName.split('.').pop().toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return <FaFilePdf className="text-danger fs-3" />;
      case 'xls':
      case 'xlsx':
        return <FaFileExcel className="text-success fs-3" />;
      case 'doc':
      case 'docx':
        return <FaFileWord className="text-primary fs-3" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
        return <FaFileImage className="text-info fs-3" />;
      default:
        return <FaFileAlt className="text-primary fs-3" />;
    }
  };

  // Dosya boyutunu formatlayan yardımcı fonksiyon
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
  };

  // Tarihi formatlayan yardımcı fonksiyon
  const formatDate = (dateString) => {
    if (!dateString) return '';
    
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('tr-TR', options);
  };

  return (
    <div className="documents-list">
      {documents.length === 0 ? (
        <div className="alert alert-info">
          {t("Bu ihale için herhangi bir doküman bulunmamaktadır.")}
        </div>
      ) : (
        <Row>
          {documents.map((document, index) => (
            <Col md={6} key={index} className="mb-3">
              <Card className="document-card h-100">
                <Card.Body>
                  <div className="d-flex">
                    <div className="document-icon me-3">
                      {getFileIcon(document.name)}
                    </div>
                    <div className="flex-grow-1">
                      <h5 className="document-name mb-2">{document.name}</h5>
                      <div className="document-meta text-muted small">
                        <span>{formatFileSize(document.size)}</span>
                        {document.uploadDate && (
                          <>
                            <span className="mx-2">•</span>
                            <span>{formatDate(document.uploadDate)}</span>
                          </>
                        )}
                      </div>
                      <div className="mt-3">
                        <Button 
                          variant="outline-primary" 
                          size="sm" 
                          href={document.url || '#'} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          disabled={!document.url}
                        >
                          <FaDownload className="me-2" />
                          {t("İndir")}
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );
};

TenderDocumentList.propTypes = {
  documents: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.number.isRequired,
      name: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired,
      size: PropTypes.string,
      uploadDate: PropTypes.string
    })
  )
};

export default TenderDocumentList; 