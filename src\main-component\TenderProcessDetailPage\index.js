import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, Card } from "react-bootstrap";
import { 
  FaFileAlt, 
  FaUserClock, 
  FaStar, 
  FaRegStar, 
  FaArrowRight, 
  FaHandshake, 
  FaEye, 
  FaEyeSlash,
  FaCheck
} from "react-icons/fa";

import PageTitle from "../../components/PageTitle";
import SectionTitle from "../../components/common/SectionTitle";

import "./style.css";

const TenderProcessDetailPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("tender_process_detail.page_title")}
        pagesub={t("tender_process_detail.page_subtitle")}
      />
      
      <section className="tender-process-detail-section section_70">
        <Container>
          <Row className="mb-5">
            <Col lg={12}>
              <SectionTitle
                title={t("tender_process_detail.main_title")}
                description={t("tender_process_detail.main_description")}
                alignment="center"
              />
            </Col>
          </Row>
          
          <Row className="process-timeline">
            <Col lg={12}>
              <div className="timeline-wrapper">
                <div className="timeline-item">
                  <div className="timeline-dot">
                    <FaFileAlt />
                  </div>
                  <div className="timeline-content">
                    <Card>
                      <Card.Header>
                        <h4>{t("tender_process_detail.step1_title")}</h4>
                      </Card.Header>
                      <Card.Body>
                        <p>
                          {t("tender_process_detail.step1_description")}
                        </p>
                        <ul className="process-features">
                          <li><FaCheck /> {t("tender_process_detail.step1_feature1")}</li>
                          <li><FaCheck /> {t("tender_process_detail.step1_feature2")}</li>
                          <li><FaCheck /> {t("tender_process_detail.step1_feature3")}</li>
                        </ul>
                      </Card.Body>
                    </Card>
                  </div>
                </div>
                
                <div className="timeline-item">
                  <div className="timeline-dot">
                    <FaUserClock />
                  </div>
                  <div className="timeline-content">
                    <Card>
                      <Card.Header>
                        <h4>{t("tender_process_detail.step2_title")}</h4>
                      </Card.Header>
                      <Card.Body>
                        <p dangerouslySetInnerHTML={{ __html: t("tender_process_detail.step2_description") }}></p>
                        <div className="process-note">
                          <h5><FaUserClock /> {t("tender_process_detail.step2_note_title")}</h5>
                          <p>
                            {t("tender_process_detail.step2_note_description")}
                          </p>
                        </div>
                      </Card.Body>
                    </Card>
                  </div>
                </div>
                
                <div className="timeline-item">
                  <div className="timeline-dot">
                    <div className="rating-stars">
                      <FaStar />
                    </div>
                  </div>
                  <div className="timeline-content">
                    <Card>
                      <Card.Header>
                        <h4>{t("tender_process_detail.step3_title")}</h4>
                      </Card.Header>
                      <Card.Body>
                        <p dangerouslySetInnerHTML={{ __html: t("tender_process_detail.step3_description") }}></p>
                        
                        <div className="tender-phases">
                          <h5>{t("tender_process_detail.phases_title")}</h5>
                          <div className="phase-item">
                            <div className="phase-icon">1</div>
                            <div className="phase-content">
                              <strong>{t("tender_process_detail.phase1_title")}</strong>
                              <p>{t("tender_process_detail.phase1_description")}</p>
                            </div>
                          </div>
                          <div className="phase-item">
                            <div className="phase-icon">2</div>
                            <div className="phase-content">
                              <strong>{t("tender_process_detail.phase2_title")}</strong>
                              <p>{t("tender_process_detail.phase2_description")}</p>
                            </div>
                          </div>
                          <div className="phase-item">
                            <div className="phase-icon">3</div>
                            <div className="phase-content">
                              <strong>{t("tender_process_detail.phase3_title")}</strong>
                              <p>{t("tender_process_detail.phase3_description")}</p>
                            </div>
                          </div>
                          <div className="phase-item">
                            <div className="phase-icon">4</div>
                            <div className="phase-content">
                              <strong>{t("tender_process_detail.phase4_title")}</strong>
                              <p>{t("tender_process_detail.phase4_description")}</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="supplier-info">
                          <h5><FaEyeSlash /> {t("tender_process_detail.supplier_info_title")}</h5>
                          <ul className="process-features">
                            <li><FaEyeSlash /> {t("tender_process_detail.supplier_info_feature1")}</li>
                            <li><FaStar /> {t("tender_process_detail.supplier_info_feature2")}</li>
                            <li><FaEye /> {t("tender_process_detail.supplier_info_feature3")}</li>
                          </ul>
                        </div>
                      </Card.Body>
                    </Card>
                  </div>
                </div>
                
                <div className="timeline-item">
                  <div className="timeline-dot">
                    <FaHandshake />
                  </div>
                  <div className="timeline-content">
                    <Card>
                      <Card.Header>
                        <h4>{t("tender_process_detail.step4_title")}</h4>
                      </Card.Header>
                      <Card.Body>
                        <p>
                          {t("tender_process_detail.step4_description")}
                        </p>
                        <ul className="process-features">
                          <li><FaCheck /> {t("tender_process_detail.step4_feature1")}</li>
                          <li><FaCheck /> {t("tender_process_detail.step4_feature2")}</li>
                          <li><FaCheck /> {t("tender_process_detail.step4_feature3")}</li>
                          <li><FaCheck /> {t("tender_process_detail.step4_feature4")}</li>
                        </ul>
                      </Card.Body>
                    </Card>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
          
          <Row className="mt-5">
            <Col lg={12} className="text-center">
              <div className="process-help">
                <h4>{t("tender_process_detail.help_title")}</h4>
                <p>{t("tender_process_detail.help_description")}</p>
                <a href="/contact" className="gauto-theme-btn">
                  {t("tender_process_detail.contact_button")} <FaArrowRight />
                </a>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </Fragment>
  );
};

export default TenderProcessDetailPage; 