import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Container, Row, Col, Spinner, Alert } from "react-bootstrap";
import {
  FaCar,
  FaCogs,
  FaTachometerAlt,
  FaAngleDoubleRight,
  FaFilter,
} from "react-icons/fa";
import {
  DatePickerComponent,
  TimePickerComponent,
} from "@syncfusion/ej2-react-calendars";
import { fetchVehicles, setFilters, clearFilters } from "../../redux/slices/vehiclesSlice";

import img2 from "../../img/nissan-offer.png";
import img3 from "../../img/audi-offer.png";
import img4 from "../../img/bmw-offer.png";
import img5 from "../../img/toyota-offer-2.png";
import img6 from "../../img/marcedes-offer.png";

import "./style.css";

const CarList = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  const { vehicles, loading, error, filters } = useSelector((state) => state.vehicles);
  const [formData, setFormData] = useState({
    fromAddress: "",
    toAddress: "",
    carType: "",
    journeyDate: "",
    journeyTime: "",
    brand: "",
  });
  
  useEffect(() => {
    // İlk yüklemede araçları getir
    dispatch(fetchVehicles(filters));
  }, [dispatch, filters]);
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  const handleFilterChange = (e) => {
    e.preventDefault();
    const brand = e.target.textContent.split('(')[0].trim();
    dispatch(setFilters({ brand: brand === 'All Brands' ? '' : brand.toLowerCase() }));
  };
  
  const handleDateChange = (e) => {
    if (e.value) {
      setFormData({
        ...formData,
        journeyDate: e.value.toISOString().split('T')[0]
      });
    }
  };
  
  const handleTimeChange = (e) => {
    if (e.value) {
      setFormData({
        ...formData,
        journeyTime: e.value.toTimeString().split(' ')[0]
      });
    }
  };

  const SubmitHandler = (e) => {
    e.preventDefault();
    // Uygulanan filtrelere göre araçları getir
    dispatch(fetchVehicles({
      ...filters,
      fromAddress: formData.fromAddress,
      toAddress: formData.toAddress,
      carType: formData.carType,
      journeyDate: formData.journeyDate,
      journeyTime: formData.journeyTime
    }));
  };

  const onClick = (e) => {
    e.preventDefault();
  };
  
  const resetFilters = () => {
    dispatch(clearFilters());
    setFormData({
      fromAddress: "",
      toAddress: "",
      carType: "",
      journeyDate: "",
      journeyTime: "",
      brand: "",
    });
  };

  return (
    <section className="gauto-car-listing section_70">
      <Container>
        <Row>
          <Col lg={4}>
            <div className="car-list-left">
              <div className="sidebar-widget">
                <div className="filter-header d-flex justify-content-between align-items-center mb-3">
                  <h4><FaFilter /> Filtreler</h4>
                  <button 
                    onClick={resetFilters} 
                    className="btn btn-sm btn-outline-secondary"
                  >
                    Filtreleri Sıfırla
                  </button>
                </div>
                <form onSubmit={SubmitHandler}>
                  <p>
                    <input 
                      type="text" 
                      name="fromAddress"
                      value={formData.fromAddress}
                      onChange={handleInputChange}
                      placeholder={t("from_address")} 
                    />
                  </p>
                  <p>
                    <input 
                      type="text"
                      name="toAddress"
                      value={formData.toAddress}
                      onChange={handleInputChange} 
                      placeholder={t("to_address")} 
                    />
                  </p>
                  <p>
                    <select
                      name="carType"
                      value={formData.carType}
                      onChange={handleInputChange}
                    >
                      <option value="">Tüm Araç Tipleri</option>
                      <option value="ac">{t("ac_car")}</option>
                      <option value="non_ac">{t("non_ac_car")}</option>
                      <option value="suv">SUV</option>
                      <option value="sedan">Sedan</option>
                      <option value="sports">Spor</option>
                    </select>
                  </p>
                  <p>
                    <DatePickerComponent
                      id="datepicker"
                      placeholder={t("journey_date")}
                      onChange={handleDateChange}
                    ></DatePickerComponent>
                  </p>
                  <p>
                    <TimePickerComponent
                      id="timepicker"
                      placeholder={t("journey_time")}
                      onChange={handleTimeChange}
                    ></TimePickerComponent>
                  </p>
                  <p>
                    <button type="submit" className="gauto-theme-btn" disabled={loading}>
                      {loading ? "Aranıyor..." : t("find_car")}
                    </button>
                  </p>
                </form>
              </div>
              <div className="sidebar-widget">
                <h3>Markalar</h3>
                <ul className="service-menu">
                  <li className={filters.brand === '' ? 'active' : ''}>
                    <Link to="#" onClick={handleFilterChange}>
                      All Brands<span>({vehicles.length})</span>
                    </Link>
                  </li>
                  <li className={filters.brand === 'toyota' ? 'active' : ''}>
                    <Link to="#" onClick={handleFilterChange}>
                      Toyota<span>({vehicles.filter(v => v.brand.toLowerCase() === 'toyota').length})</span>
                    </Link>
                  </li>
                  <li className={filters.brand === 'nissan' ? 'active' : ''}>
                    <Link to="#" onClick={handleFilterChange}>
                      nissan<span>({vehicles.filter(v => v.brand.toLowerCase() === 'nissan').length})</span>
                    </Link>
                  </li>
                  <li className={filters.brand === 'audi' ? 'active' : ''}>
                    <Link to="#" onClick={handleFilterChange}>
                      Audi<span>({vehicles.filter(v => v.brand.toLowerCase() === 'audi').length})</span>
                    </Link>
                  </li>
                  <li className={filters.brand === 'bmw' ? 'active' : ''}>
                    <Link to="#" onClick={handleFilterChange}>
                      BMW<span>({vehicles.filter(v => v.brand.toLowerCase() === 'bmw').length})</span>
                    </Link>
                  </li>
                  <li className={filters.brand === 'mercedes' ? 'active' : ''}>
                    <Link to="#" onClick={handleFilterChange}>
                      mercedes<span>({vehicles.filter(v => v.brand.toLowerCase() === 'mercedes').length})</span>
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </Col>
          <Col lg={8}>
            <div className="car-listing-right">
              {loading ? (
                <div className="text-center my-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-2">Araçlar yükleniyor...</p>
                </div>
              ) : error ? (
                <Alert variant="danger">{error}</Alert>
              ) : vehicles.length === 0 ? (
                <Alert variant="warning">
                  Bu kriterlere uygun araç bulunamadı. Lütfen filtrelerinizi değiştirin.
                </Alert>
              ) : (
                <div className="car-grid-list">
                  {vehicles.map((vehicle, index) => (
                    <Row key={vehicle.id} className={index > 0 ? 'mt-4' : ''}>
                      <Col md={12}>
                        <div className="single-offers">
                          <div className="offer-image">
                            <Link to={`/car-booking?id=${vehicle.id}`}>
                              <img src={vehicle.image} alt={`${vehicle.brand} ${vehicle.model}`} />
                            </Link>
                          </div>
                          <div className="offer-text">
                            <Link to={`/car-booking?id=${vehicle.id}`}>
                              <h3>{vehicle.brand} {vehicle.model}</h3>
                            </Link>
                            <h4>
                              ${vehicle.price}.00<span>/ {t("day")}</span>
                            </h4>
                            <ul>
                              <li>
                                <FaCar />
                                {t("model")}: {vehicle.year}
                              </li>
                              <li>
                                <FaCogs />
                                {vehicle.features[0]}
                              </li>
                              <li>
                                <FaTachometerAlt />
                                {vehicle.features[1]}
                              </li>
                            </ul>
                            <div className="offer-action">
                              <Link 
                                to={`/car-booking?id=${vehicle.id}`} 
                                className="offer-btn-1"
                              >
                                {t("rent_car")}
                              </Link>
                              <Link 
                                to={`/car-booking?id=${vehicle.id}`} 
                                className="offer-btn-2"
                              >
                                {t("details")}
                              </Link>
                            </div>
                          </div>
                        </div>
                      </Col>
                    </Row>
                  ))}
                </div>
              )}
              
              {vehicles.length > 0 && (
                <div className="pagination-box-row">
                  <p>Sayfa 1 - {vehicles.length} sonuç gösteriliyor</p>
                  <ul className="pagination">
                    <li className="active"><Link to="#" onClick={onClick}>1</Link></li>
                    <li><Link to="#" onClick={onClick}>2</Link></li>
                    <li><Link to="#" onClick={onClick}>3</Link></li>
                    <li><Link to="#" onClick={onClick}><FaAngleDoubleRight /></Link></li>
                  </ul>
                </div>
              )}
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default CarList;
