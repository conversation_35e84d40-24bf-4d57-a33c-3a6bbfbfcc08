import React from 'react';
import { Badge, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { formatDate, formatCurrency } from '../utils/formatters';
import { getStatusColor } from '../utils/statusHelpers';
import { FaMoneyBillWave, FaHourglassHalf, FaTimesCircle, FaCheckCircle, FaFileAlt } from 'react-icons/fa';

/**
 * İhaleye verilen teklifleri listeleyen tablo bileşeni
 * @param {object} props - Bileşen propsları
 * @param {array} props.bids - Teklifler listesi
 */
const TenderBidsTable = ({ bids = [] }) => {
  const { t } = useTranslation();

  // Teklif durumuna göre icon belirle
  const getStatusIcon = (status) => {
    if (!status) return null;
    
    switch (status.toUpperCase()) {
      case "SUBMITTED":
        return <FaMoneyBillWave className="me-1" />;
      case "PENDING":
        return <FaHourglassHalf className="me-1" />;
      case "REJECTED":
        return <FaTimesCircle className="me-1" />;
      case "APPROVED":
        return <FaCheckCircle className="me-1" />;
      case "DRAFT":
        return <FaFileAlt className="me-1" />;
      default:
        return null;
    }
  };
  
  // Teklif durumunu metne çevir
  const getStatusText = (status) => {
    if (!status) return '';
    
    switch (status?.toUpperCase()) {
      case "SUBMITTED": return 'Gönderildi';
      case "PENDING": return 'Beklemede';
      case "APPROVED": return 'Onaylandı';
      case "REJECTED": return 'Reddedildi';
      case "DRAFT": return 'Taslak';
      default: return status;
    }
  };

  if (!bids || bids.length === 0) {
    return (
      <Alert variant="info">
        {t('tender.no_bids_yet', 'Henüz teklif bulunmuyor')}
      </Alert>
    );
  }

  return (
    <div className="table-responsive">
      <table className="table table-hover">
        <thead>
          <tr>
            <th>{t('tender.bid.company', 'Şirket')}</th>
            <th>{t('tender.bid.amount', 'Tutar')}</th>
            <th>{t('tender.bid.status', 'Durum')}</th>
            <th>{t('tender.bid.date', 'Tarih')}</th>
          </tr>
        </thead>
        <tbody>
          {bids.map(bid => (
            <tr key={bid.id}>
              <td>{bid.company || bid.companyName}</td>
              <td>{formatCurrency(bid.amount)}</td>
              <td>
                <Badge bg={getStatusColor(bid.status)}>
                  {getStatusIcon(bid.status)} {bid.statusText || getStatusText(bid.status)}
                </Badge>
              </td>
              <td>{formatDate(bid.createdAt)}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TenderBidsTable; 