import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { FaCarAlt, FaFileContract, FaUsers, FaHandshake } from 'react-icons/fa';
import './style.css';

// API servisi import edeceğiz
import { useMockAPI } from '../../services/api';

const RecentActivities = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { user } = useSelector(state => state.auth);
  
  const [activities, setActivities] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // API'dan veri çekme işlemi
  useEffect(() => {
    const fetchRecentActivities = async () => {
      setIsLoading(true);
      try {
        if (useMockAPI) {
          // Mock veri kullan
          setTimeout(() => {
            setActivities([
              {
                id: 1,
                type: 'tender_bid',
                title: 'Ankara Merkez SUV ihalesi için teklif verdiniz',
                date: '2 saat önce',
                icon: 'bid',
                link: '/tender/45'
              },
              {
                id: 2,
                type: 'tender_created',
                title: 'Yeni ihale oluşturdunuz: Ankara Bölgesi 5 Araç',
                date: '1 gün önce',
                icon: 'tender',
                link: '/tender/23'
              },
              {
                id: 3,
                type: 'tender_match',
                title: 'İzmir Minivan ihalesi kazandınız!',
                date: '2 gün önce',
                icon: 'match',
                link: '/tender/34'
              },
              {
                id: 4,
                type: 'contract_signed',
                title: 'İstanbul Bölgesi Nakliyat\'ın son teklif süreci yaklaşıyor',
                date: '4 gün önce',
                icon: 'alert',
                link: '/tender/12'
              }
            ]);
            setIsLoading(false);
          }, 800);
        } else {
          // Gerçek API çağrısı yapılacak
          // const response = await getRecentActivities(user.id);
          // setActivities(response.data.activities);
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Son aktiviteler getirilirken hata:', err);
        setError(t('errors.failed_to_fetch'));
        setIsLoading(false);
      }
    };

    if (useMockAPI) {
      // Mock API aktifse, kullanıcı ID'sinden bağımsız olarak mock verileri yükle
      fetchRecentActivities();
    } else if (user?.id) {
      // Mock API aktif değilse ve kullanıcı ID'si varsa verileri getir
      fetchRecentActivities();
    } else {
      setIsLoading(false);
    }
  }, [user, t, useMockAPI]);

  // İlgili sayfaya yönlendirme
  const handleActivityClick = (link) => {
    navigate(link);
  };
  
  // İkon bileşeni seçici
  const getIconForActivity = (type) => {
    switch (type) {
      case 'tender':
        return <FaFileContract className="activity-icon tender" />;
      case 'bid':
        return <FaHandshake className="activity-icon bid" />;
      case 'match':
        return <FaUsers className="activity-icon match" />;
      case 'alert':
        return <FaCarAlt className="activity-icon alert" />;
      default:
        return <FaFileContract className="activity-icon" />;
    }
  };

  // Tüm aktiviteleri görüntüleme
  const handleViewAllActivities = () => {
    navigate('/activities');
  };
  
  return (
    <div className="recent-activities-section">
      <div className="section-header">
        <h3>{t('dashboard.recent_activities.title')}</h3>
      </div>
      
      <div className="activities-list">
        {isLoading ? (
          <div className="loading-indicator">{t('dashboard.recent_activities.loading')}</div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : activities.length === 0 ? (
          <div className="no-items-message">{t('dashboard.recent_activities.no_items')}</div>
        ) : (
          <>
            {activities.map(activity => (
              <div 
                key={activity.id} 
                className="activity-item"
                onClick={() => handleActivityClick(activity.link)}
              >
                <div className="activity-icon-container">
                  {getIconForActivity(activity.icon)}
                </div>
                <div className="activity-content">
                  <div className="activity-title">{activity.title}</div>
                  <div className="activity-date">{activity.date}</div>
                </div>
              </div>
            ))}
            
            <div className="view-all-container">
              <button 
                className="view-all-button"
                onClick={handleViewAllActivities}
              >
                {t('dashboard.recent_activities.view_all')}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RecentActivities; 