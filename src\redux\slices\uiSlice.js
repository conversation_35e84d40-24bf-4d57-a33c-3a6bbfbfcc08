import { createSlice } from '@reduxjs/toolkit';
import i18next from 'i18next';

// Initial State
const initialState = {
  notifications: [],
  modals: {
    isLoginModalOpen: false,
    isRegisterModalOpen: false,
    isFilterModalOpen: false,
    isBidModalOpen: false,
  },
  theme: localStorage.getItem('theme') || 'light',
  language: localStorage.getItem('language') || 'tr',
  searchQuery: '',
};

// UI Slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Notification actions
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        ...action.payload,
        createdAt: new Date().toISOString(),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      );
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Modal actions
    openModal: (state, action) => {
      state.modals[action.payload] = true;
    },
    closeModal: (state, action) => {
      state.modals[action.payload] = false;
    },
    
    // Theme actions
    setTheme: (state, action) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    
    // Language actions
    setLanguage: (state, action) => {
      const newLang = action.payload;
      state.language = newLang;
      localStorage.setItem('language', newLang);
      i18next.changeLanguage(newLang);
      document.documentElement.lang = newLang;
    },
    
    // Search actions
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
  },
});

export const {
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  setTheme,
  setLanguage,
  setSearchQuery,
} = uiSlice.actions;

export default uiSlice.reducer; 