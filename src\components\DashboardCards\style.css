@import url('../../styles/colors.css');

.dashboard-cards-section {
  background-color: #f8f9fa;
  padding-top: 30px;
  padding-bottom: 50px;
}

.dashboard-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.07);
  transition: all 0.4s ease;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.dashboard-card:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0.05) 100%);
  z-index: -1;
  transition: opacity 0.4s ease;
  opacity: 0;
}

.dashboard-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px var(--primary-shadow);
}

.dashboard-card:hover:before {
  opacity: 1;
}

.dashboard-card:after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  transform: scaleX(0);
  transform-origin: 0 0;
  transition: transform 0.4s ease;
}

.dashboard-card:hover:after {
  transform: scaleX(1);
}

.dashboard-card .card-body {
  padding: 25px 20px;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #ffffff !important;
  color: var(--primary-color);
  box-shadow: 0 6px 15px var(--primary-shadow);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.icon-container svg {
  font-size: 30px;
  color: var(--primary-color) !important;
}

.count {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.35rem;
  color: #333;
  background: linear-gradient(90deg, #001238, #001f63);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -1px;
}

.label {
  font-size: 1rem;
  color: #6c757d;
  font-weight: 500;
  position: relative;
  display: inline-block;
}

.label:after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  transform: translateX(-50%);
}

.chart-card {
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
  border: none;
  overflow: hidden;
}

.chart-card .card-header {
  background: linear-gradient(90deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 20px 25px;
}

.chart-card .card-header h5 {
  font-weight: 700;
  color: #001238;
  position: relative;
  display: inline-block;
  padding-left: 15px;
}

.chart-card .card-header h5:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 20px;
  background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
  border-radius: 3px;
}

.chart-card .card-body {
  padding: 25px;
  background-color: #ffffff;
}

.dashboard-heading {
  margin-bottom: 20px !important;
}

.trend-indicator {
  margin-top: 15px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.trend-up {
  color: #28a745;
}

.trend-down {
  color: #dc3545;
}

.trend-value {
  font-weight: 600;
}

.mini-chart {
  width: 100%;
  height: 200px;
}

.custom-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.tooltip-label {
  color: #fff;
  font-weight: 600;
  margin-bottom: 5px;
  text-align: left;
  display: block;
  font-size: 13px;
}

.custom-tooltip p {
  color: #fff;
  text-align: left;
  margin: 0;
}

.chart-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 15px;
}

.chart-tab {
  padding: 10px 20px;
  border: 1px solid #ced4da;
  border-radius: 30px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.chart-tab:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.chart-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

@media (max-width: 991px) {
  .chart-tabs {
    flex-wrap: wrap;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .count {
    font-size: 2rem;
  }
  
  .icon-container {
    width: 60px;
    height: 60px;
  }
} 