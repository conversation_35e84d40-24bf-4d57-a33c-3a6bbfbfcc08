import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, Alert } from "react-bootstrap";
import { <PERSON>a<PERSON><PERSON>, <PERSON>aLock, FaUser } from "react-icons/fa";
import { loginUser, clearError, manualLogin } from "../../redux/slices/authSlice";
import { USER_ROLES } from "../../constants/userRoles";

import "./style.css";

const Login = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Yönlendirme bilgisi
  const from = location.state?.from || "/dashboard";

  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false
  });

  const { loading, error, isAuthenticated, user, token } = useSelector((state) => state.auth);

  // Debug bilgisi
  useEffect(() => {
  }, [isAuthenticated, user, loading, error, from]);

  // Kullanıcı giriş yapmışsa istenen sayfaya yönlendir
  useEffect(() => {
    if (isAuthenticated && user) {
      navigate(from);
    }
  }, [isAuthenticated, user, navigate, from]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    dispatch(loginUser({
      username: formData.username,
      password: formData.password,
      rememberMe: formData.rememberMe
    }));
  };

  // Test için hızlı giriş
  const handleQuickLogin = (userType) => {
    if (userType === 'customer') {
      dispatch(manualLogin({
        user: {
          id: 101,
          name: 'Test Müşteri',
          email: '<EMAIL>',
          roleId: USER_ROLES.CUSTOMER.id,
          isVerified: true
        },
        token: 'mock-jwt-token-customer'
      }));
    } else if (userType === 'cs') {
      dispatch(manualLogin({
        user: {
          id: 102,
          name: 'Test Müşteri Hizmetleri',
          email: '<EMAIL>',
          roleId: USER_ROLES.CUSTOMER_SERVICE.id,
          isVerified: true
        },
        token: 'mock-jwt-token-cs'
      }));
    } else if (userType === 'admin') {
      dispatch(manualLogin({
        user: {
          id: 1,
          name: 'Admin Kullanıcı',
          email: '<EMAIL>',
          roleId: USER_ROLES.ADMIN.id,
          isVerified: true
        },
        token: 'mock-jwt-token-admin'
      }));
    }
  };

  const handleForgotPassword = (e) => {
    e.preventDefault();
    // Şifre sıfırlama işlemleri için
  };

  // Hata göstergesi kapatıldığında hata state'ini temizle
  const handleErrorClose = () => {
    dispatch(clearError());
  };

  return (
    <section className="gauto-login-area section_70">
      <Container>
        <Row>
          <Col md={12}>
            <div className="login-box">
              <div className="login-page-heading">
                <FaKey />
                <h3>{t("login_page.singin")}</h3>
              </div>

              {error && (
                <Alert variant="danger" dismissible onClose={handleErrorClose}>
                  {error}
                </Alert>
              )}

              {isAuthenticated && (
                <Alert variant="success">
                  {t("login_page.success", { name: user?.name })}
                </Alert>
              )}

              <form onSubmit={handleSubmit}>
                <div className="account-form-group">
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    placeholder={t("login_page.username")}
                    required
                  />
                  <FaUser />
                </div>

                <div className="account-form-group">
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder={t("login_page.password")}
                    required
                  />
                  <FaLock />
                </div>

                <div className="remember-row">
                  <p className="lost-pass">
                    <Link to="/" onClick={handleForgotPassword}>
                      {t("login_page.f_password")}
                    </Link>
                  </p>
                  <p className="checkbox remember">
                    <input
                      className="checkbox-spin"
                      type="checkbox"
                      id="rememberMe"
                      name="rememberMe"
                      checked={formData.rememberMe}
                      onChange={handleChange}
                    />
                    <label htmlFor="rememberMe">
                      <span />
                      {t("login_page.keep")}
                    </label>
                  </p>
                </div>

                <div className="login-btn-row">
                  <button
                    type="submit"
                    className="gauto-theme-btn"
                    disabled={loading}
                  >
                    {loading ? t("login_page.loading") : t("login_page.btn")}
                  </button>
                </div>
              </form>

              <div className="login-sign-up">
                <Link to="/register">{t("login_page.need_account")}</Link>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default Login;
