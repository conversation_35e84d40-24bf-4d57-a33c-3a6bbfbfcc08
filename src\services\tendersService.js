import api, { useMockAPI } from './api';
import { USER_ROLES } from '../constants/userRoles';

// ----- Gerçek API Çağrıları ----- //

// Tüm teklifleri getir (filtreleme dahil)
const getTenders = (filters = {}) => api.get('/tenders', { params: filters });

// Belirli bir teklifin detaylarını getir
const getTenderById = (id) => api.get(`/tenders/${id}`);

// Yeni teklif oluştur (USER rolüne sahip kullanıcılar)
const createTender = (tenderData) => api.post('/tenders', tenderData);

// Teklifi güncelle (sadece teklifi oluşturan USER)
const updateTender = (id, tenderData) => api.put(`/tenders/${id}`, tenderData);

// Teklifi sil (sadece teklifi oluşturan USER veya ADMIN)
const deleteTender = (id) => api.delete(`/tenders/${id}`);

// Teklife başvur (USER rolündeki kullanıcılar)
const applyToTender = (id, applicationData) => api.post(`/tenders/${id}/apply`, applicationData);

// Kullanıcının başvurularını getir (USER)
const getUserApplications = () => api.get('/applications');

// Bir teklife yapılan başvuruları getir (teklifin sahibi olan USER)
const getTenderApplications = (tenderId) => api.get(`/tenders/${tenderId}/applications`);

// Bir başvuruyu onayla/reddet (teklifin sahibi olan USER)
const respondToApplication = (tenderId, applicationId, responseData) =>
  api.put(`/tenders/${tenderId}/applications/${applicationId}/respond`, responseData);

// ----- Mock API Çağrıları ----- //

// Mock teklif verileri
let mockTenders = [
  {
    id: 1,
    companyId: 2, // COMPANY rolüne sahip kullanıcı ID'si
    companyName: 'ABC Rent A Car',
    title: 'Yıllık Kiralama Fırsatı - SUV',
    description: 'Şirketimizden yıllık kiralama yapacak müşterilerimiz için özel indirimli fiyatlar sunuyoruz. SUV model araçlar için geçerlidir.',
    vehicleType: 'SUV',
    minRentalPeriod: 12, // ay cinsinden
    maxRentalPeriod: 24, // ay cinsinden
    pricePerMonth: 4500,
    priceUnit: 'TL',
    availableCount: 5,
    features: ['Kasko Dahil', 'Bakım Dahil', 'Yol Yardım Dahil'],
    location: 'İstanbul',
    status: 'ACTIVE', // ACTIVE, INACTIVE, EXPIRED
    validUntil: '2023-12-31',
    createdAt: '2023-03-01T10:00:00Z',
    updatedAt: '2023-03-01T10:00:00Z'
  },
  {
    id: 2,
    companyId: 3,
    companyName: 'XYZ Fleet',
    title: 'Kurumsal Şirketler İçin Sedan Kiralama',
    description: 'Kurumsal şirketler için uzun dönem araç kiralama fırsatı. Minimum 10 araçlık filo kiralama durumunda özel fiyat.',
    vehicleType: 'Sedan',
    minRentalPeriod: 6,
    maxRentalPeriod: 36,
    pricePerMonth: 3800,
    priceUnit: 'TL',
    availableCount: 20,
    features: ['Kasko Dahil', 'Bakım Dahil', 'Yakıt Hariç', '7/24 Destek'],
    location: 'Ankara',
    status: 'ACTIVE',
    validUntil: '2023-09-30',
    createdAt: '2023-01-15T09:30:00Z',
    updatedAt: '2023-01-15T09:30:00Z'
  },
  {
    id: 3,
    companyId: 2,
    companyName: 'ABC Rent A Car',
    title: 'Ekonomik Araç Uzun Dönem Kiralama',
    description: 'Ekonomik sınıf araçlar için uzun dönem kiralama fırsatı. Yeni mezun ve öğrencilere özel indirim.',
    vehicleType: 'Hatchback',
    minRentalPeriod: 3,
    maxRentalPeriod: 12,
    pricePerMonth: 2500,
    priceUnit: 'TL',
    availableCount: 8,
    features: ['Kasko Dahil', 'Bakım Dahil', 'KDV Dahil'],
    location: 'İzmir',
    status: 'ACTIVE',
    validUntil: '2023-11-15',
    createdAt: '2023-02-10T14:20:00Z',
    updatedAt: '2023-02-10T14:20:00Z'
  }
];

// Mock başvuru verileri
let mockApplications = [
  {
    id: 1,
    tenderId: 1,
    userId: 1, // CUSTOMER rolüne sahip kullanıcı ID'si
    userName: 'Ahmet Yılmaz',
    userEmail: '<EMAIL>',
    requestedPeriod: 12,
    requestedCount: 1,
    message: 'Şirketim için 1 adet SUV araç kiralamak istiyorum. Aracı İstanbul içinde kullanacağım.',
    status: 'PENDING', // PENDING, APPROVED, REJECTED
    responseMessage: null,
    createdAt: '2023-03-05T13:45:00Z',
    updatedAt: '2023-03-05T13:45:00Z'
  },
  {
    id: 2,
    tenderId: 2,
    userId: 1,
    userName: 'Ahmet Yılmaz',
    userEmail: '<EMAIL>',
    requestedPeriod: 24,
    requestedCount: 15,
    message: 'Şirketimiz için 15 adet sedan araç kiralamak istiyoruz. Detayları görüşmek üzere iletişime geçerseniz sevinirim.',
    status: 'APPROVED',
    responseMessage: 'Talebiniz onaylandı. Detayları görüşmek üzere sizinle iletişime geçeceğiz.',
    createdAt: '2023-02-20T10:30:00Z',
    updatedAt: '2023-02-22T15:20:00Z'
  },
  {
    id: 3,
    tenderId: 3,
    userId: 4,
    userName: 'Ayşe Demir',
    userEmail: '<EMAIL>',
    requestedPeriod: 6,
    requestedCount: 1,
    message: 'Yeni mezunum ve 6 aylık bir kiralama düşünüyorum. Öğrenci indirimi mevcut mu?',
    status: 'REJECTED',
    responseMessage: 'Üzgünüz, şu anda tüm araçlarımız kiralanmış durumda.',
    createdAt: '2023-02-15T09:15:00Z',
    updatedAt: '2023-02-16T11:40:00Z'
  }
];

// İlerleyen zamanda kullanılmak üzere otomatik ID oluşturma
let nextTenderId = mockTenders.length + 1;
let nextApplicationId = mockApplications.length + 1;

// Mock teklifleri getirme ve filtreleme
const mockGetTenders = (filters = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredTenders = [...mockTenders];

      // Filtreleme işlemleri
      if (filters.companyId) {
        filteredTenders = filteredTenders.filter(
          tender => tender.companyId === parseInt(filters.companyId)
        );
      }

      if (filters.vehicleType) {
        filteredTenders = filteredTenders.filter(
          tender => tender.vehicleType.toLowerCase() === filters.vehicleType.toLowerCase()
        );
      }

      if (filters.location) {
        filteredTenders = filteredTenders.filter(
          tender => tender.location.toLowerCase().includes(filters.location.toLowerCase())
        );
      }

      if (filters.minPrice) {
        filteredTenders = filteredTenders.filter(
          tender => tender.pricePerMonth >= parseInt(filters.minPrice)
        );
      }

      if (filters.maxPrice) {
        filteredTenders = filteredTenders.filter(
          tender => tender.pricePerMonth <= parseInt(filters.maxPrice)
        );
      }

      if (filters.status) {
        filteredTenders = filteredTenders.filter(
          tender => tender.status.toLowerCase() === filters.status.toLowerCase()
        );
      } else {
        // Varsayılan olarak sadece aktif teklifleri göster
        filteredTenders = filteredTenders.filter(
          tender => tender.status === 'ACTIVE'
        );
      }

      // Teklifleri sıralama (varsayılan: tarihe göre yeniden eskiye)
      if (filters.sort) {
        switch (filters.sort) {
          case 'price_asc':
            filteredTenders.sort((a, b) => a.pricePerMonth - b.pricePerMonth);
            break;
          case 'price_desc':
            filteredTenders.sort((a, b) => b.pricePerMonth - a.pricePerMonth);
            break;
          case 'date_asc':
            filteredTenders.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
            break;
          case 'date_desc':
            filteredTenders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            break;
          default:
            filteredTenders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        }
      } else {
        filteredTenders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
      }

      resolve({
        data: {
          tenders: filteredTenders,
          total: filteredTenders.length
        }
      });
    }, 800);
  });
};

// Mock belirli bir teklifin detaylarını getirme
const mockGetTenderById = (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const tender = mockTenders.find(t => t.id === parseInt(id));

      if (!tender) {
        return reject({
          response: { data: { message: 'Teklif bulunamadı' } }
        });
      }

      resolve({ data: tender });
    }, 600);
  });
};

// Mock teklif oluşturma
const mockCreateTender = (tenderData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Tüm CUSTOMER rolüne sahip kullanıcılar teklif oluşturabilir
        if (user.roleId !== USER_ROLES.CUSTOMER.id && user.roleId !== USER_ROLES.ADMIN.id) {
          return reject({
            response: { data: { message: 'Teklif oluşturma yetkiniz bulunmamaktadır' } }
          });
        }

        // Yeni teklif oluştur
        const newTender = {
          id: nextTenderId++,
          companyId: user.id,
          companyName: user.name || 'Şirket',
          title: tenderData.title,
          description: tenderData.description,
          vehicleType: tenderData.vehicleType,
          minRentalPeriod: tenderData.minRentalPeriod,
          maxRentalPeriod: tenderData.maxRentalPeriod,
          pricePerMonth: tenderData.pricePerMonth,
          priceUnit: tenderData.priceUnit || 'TL',
          availableCount: tenderData.availableCount,
          features: tenderData.features || [],
          location: tenderData.location,
          status: 'ACTIVE',
          validUntil: tenderData.validUntil,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Teklifi mock veritabanına ekle
        mockTenders.push(newTender);

        resolve({ data: newTender });
      } catch (error) {
        reject({
          response: { data: { message: 'Teklif oluşturulurken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock teklifi güncelleme
const mockUpdateTender = (id, tenderData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Teklifi bul
        const tenderIndex = mockTenders.findIndex(t => t.id === parseInt(id));

        if (tenderIndex === -1) {
          return reject({
            response: { data: { message: 'Teklif bulunamadı' } }
          });
        }

        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Teklifi yalnızca oluşturan kullanıcı güncelleyebilir
        if ((user.roleId !== USER_ROLES.CUSTOMER.id && user.roleId !== USER_ROLES.ADMIN.id) ||
            (user.roleId === USER_ROLES.CUSTOMER.id && mockTenders[tenderIndex].companyId !== user.id)) {
          return reject({
            response: { data: { message: 'Bu teklifi güncelleme yetkiniz bulunmamaktadır' } }
          });
        }

        // Teklifi güncelle
        mockTenders[tenderIndex] = {
          ...mockTenders[tenderIndex],
          ...tenderData,
          updatedAt: new Date().toISOString()
        };

        resolve({ data: mockTenders[tenderIndex] });
      } catch (error) {
        reject({
          response: { data: { message: 'Teklif güncellenirken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock teklifi silme
const mockDeleteTender = (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Teklifi bul
        const tenderIndex = mockTenders.findIndex(t => t.id === parseInt(id));

        if (tenderIndex === -1) {
          return reject({
            response: { data: { message: 'Teklif bulunamadı' } }
          });
        }

        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Teklifi yalnızca oluşturan kullanıcı veya admin silebilir
        if ((user.roleId !== USER_ROLES.CUSTOMER.id || mockTenders[tenderIndex].companyId !== user.id) &&
            user.roleId !== USER_ROLES.ADMIN.id) {
          return reject({
            response: { data: { message: 'Bu teklifi silme yetkiniz bulunmamaktadır' } }
          });
        }

        // Teklifi sil
        const deletedTender = mockTenders[tenderIndex];
        mockTenders.splice(tenderIndex, 1);

        resolve({
          data: {
            success: true,
            message: 'Teklif başarıyla silindi',
            tender: deletedTender
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Teklif silinirken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock teklife başvurma
const mockApplyToTender = (id, applicationData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Teklifi bul
        const tender = mockTenders.find(t => t.id === parseInt(id));

        if (!tender) {
          return reject({
            response: { data: { message: 'Teklif bulunamadı' } }
          });
        }

        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Tüm CUSTOMER rolüne sahip kullanıcılar tekliflere başvurabilir
        if (user.roleId !== USER_ROLES.CUSTOMER.id) {
          return reject({
            response: { data: { message: 'Tekliflere başvurma yetkiniz bulunmamaktadır' } }
          });
        }

        // Kullanıcının daha önce bu teklife başvurmuş olup olmadığını kontrol et
        const existingApplication = mockApplications.find(
          a => a.tenderId === parseInt(id) && a.userId === user.id
        );

        if (existingApplication) {
          return reject({
            response: { data: { message: 'Bu teklife zaten başvurdunuz' } }
          });
        }

        // Yeni başvuru oluştur
        const newApplication = {
          id: nextApplicationId++,
          tenderId: parseInt(id),
          userId: user.id,
          userName: user.name,
          userEmail: user.email,
          requestedPeriod: applicationData.requestedPeriod,
          requestedCount: applicationData.requestedCount || 1,
          message: applicationData.message,
          status: 'PENDING',
          responseMessage: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Başvuruyu mock veritabanına ekle
        mockApplications.push(newApplication);

        resolve({ data: newApplication });
      } catch (error) {
        reject({
          response: { data: { message: 'Teklife başvurulurken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock kullanıcının başvurularını getirme
const mockGetUserApplications = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Kullanıcının başvurularını filtrele
        const userApplications = mockApplications.filter(app => app.userId === user.id);

        // Teklif detaylarını ekle
        const applicationsWithTenders = userApplications.map(app => {
          const tender = mockTenders.find(t => t.id === app.tenderId);
          return {
            ...app,
            tenderDetails: tender || null
          };
        });

        resolve({
          data: {
            applications: applicationsWithTenders,
            total: applicationsWithTenders.length
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Başvurular getirilirken bir hata oluştu' } }
        });
      }
    }, 700);
  });
};

// Mock bir teklife yapılan başvuruları getirme
const mockGetTenderApplications = (tenderId) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Teklifi bul
        const tender = mockTenders.find(t => t.id === parseInt(tenderId));

        if (!tender) {
          return reject({
            response: { data: { message: 'Teklif bulunamadı' } }
          });
        }

        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Yalnızca teklifi oluşturan kullanıcı veya admin başvuruları görebilir
        if (
          (user.roleId !== USER_ROLES.CUSTOMER.id || tender.companyId !== user.id) &&
          user.roleId !== USER_ROLES.ADMIN.id
        ) {
          return reject({
            response: { data: { message: 'Bu teklif için başvuruları görüntüleme yetkiniz bulunmamaktadır' } }
          });
        }

        // Teklife ait başvuruları filtrele
        const tenderApplications = mockApplications.filter(app => app.tenderId === parseInt(tenderId));

        resolve({
          data: {
            applications: tenderApplications,
            total: tenderApplications.length
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Teklif başvuruları getirilirken bir hata oluştu' } }
        });
      }
    }, 700);
  });
};

// Mock bir başvuruyu yanıtlama (onaylama/reddetme)
const mockRespondToApplication = (tenderId, applicationId, responseData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Teklifi bul
        const tender = mockTenders.find(t => t.id === parseInt(tenderId));

        if (!tender) {
          return reject({
            response: { data: { message: 'Teklif bulunamadı' } }
          });
        }

        // Başvuruyu bul
        const applicationIndex = mockApplications.findIndex(
          app => app.id === parseInt(applicationId) && app.tenderId === parseInt(tenderId)
        );

        if (applicationIndex === -1) {
          return reject({
            response: { data: { message: 'Başvuru bulunamadı' } }
          });
        }

        // Kullanıcı bilgilerini al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Yalnızca teklifi oluşturan kullanıcı veya admin başvuruları yanıtlayabilir
        if (
          (user.roleId !== USER_ROLES.CUSTOMER.id || tender.companyId !== user.id) &&
          user.roleId !== USER_ROLES.ADMIN.id
        ) {
          return reject({
            response: { data: { message: 'Bu başvuruyu yanıtlama yetkiniz bulunmamaktadır' } }
          });
        }

        // Başvuruyu güncelle
        mockApplications[applicationIndex] = {
          ...mockApplications[applicationIndex],
          status: responseData.status, // APPROVED veya REJECTED
          responseMessage: responseData.responseMessage,
          updatedAt: new Date().toISOString()
        };

        resolve({
          data: {
            success: true,
            application: mockApplications[applicationIndex]
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Başvuru yanıtlanırken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

/**
 * Belirli bir şirkete ait ihaleleri kategorilerine göre getiren fonksiyon
 * @param {number} companyId - Şirket ID'si
 * @returns {Promise} - İhale kategorileri (yeni, bekleyen, onaylanan, reddedilen)
 */
export const getTendersByCompany = (companyId) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Şu anki zamanı alıyoruz
      const now = new Date();

      // Test amaçlı örnek ihale verileri
      const mockCompanyTenders = [
        // Yeni İhaleler (henüz teklif verilmemiş)
        {
          id: 'TND-2023-001',
          customerId: 101,
          customerName: 'ABC Holding A.Ş.',
          startDate: '12.06.2023',
          vehicleCount: 5,
          segment: 'B',
          duration: '24 ay',
          documents: true,
          remainingTime: '11 saat',
          status: 'NEW'
        },
        {
          id: 'TND-2023-002',
          customerId: 102,
          customerName: 'XYZ Teknoloji Ltd.',
          startDate: '12.06.2023',
          vehicleCount: 3,
          segment: 'C',
          duration: '36 ay',
          documents: true,
          remainingTime: '8 saat',
          status: 'NEW'
        },
        {
          id: 'TND-2023-003',
          customerId: 103,
          customerName: '123 Lojistik A.Ş.',
          startDate: '13.06.2023',
          vehicleCount: 10,
          segment: 'D',
          duration: '48 ay',
          documents: true,
          remainingTime: '12 saat',
          status: 'NEW'
        },

        // Bekleyen İhaleler (teklif verilmiş, müşteri yanıtı bekleniyor)
        {
          id: 'TND-2023-004',
          customerId: 104,
          customerName: 'DEF Holding A.Ş.',
          startDate: '10.06.2023',
          vehicleCount: 8,
          segment: 'C',
          duration: '36 ay',
          offerAmount: '240.000 ₺',
          status: 'PENDING',
          statusText: 'Müşteri İnceliyor'
        },
        {
          id: 'TND-2023-005',
          customerId: 105,
          customerName: 'LMN Teknoloji Ltd.',
          startDate: '11.06.2023',
          vehicleCount: 2,
          segment: 'B',
          duration: '24 ay',
          offerAmount: '120.000 ₺',
          status: 'PENDING',
          statusText: 'Müşteri İnceliyor'
        },
        {
          id: 'TND-2023-006',
          customerId: 106,
          customerName: '456 Lojistik A.Ş.',
          startDate: '09.06.2023',
          vehicleCount: 12,
          segment: 'E',
          duration: '48 ay',
          offerAmount: '720.000 ₺',
          status: 'PENDING',
          statusText: 'Müşteri İnceliyor'
        },

        // Reddedilen İhaleler
        {
          id: 'TND-2023-009',
          customerId: 109,
          customerName: 'STU Holding A.Ş.',
          startDate: '05.06.2023',
          vehicleCount: 7,
          segment: 'D',
          duration: '36 ay',
          offerAmount: '378.000 ₺',
          status: 'REJECTED',
          rejectionReason: 'Rakip teklif daha iyi olduğu için reddedildi'
        },

        // Onaylanan İhaleler
        {
          id: 'TND-2023-010',
          customerId: 110,
          customerName: 'VWX Holding A.Ş.',
          startDate: '03.06.2023',
          vehicleCount: 9,
          segment: 'E',
          duration: '48 ay',
          offerAmount: '648.000 ₺',
          status: 'APPROVED',
          approvalDate: '05.06.2023'
        },
        {
          id: 'TND-2023-011',
          customerId: 111,
          customerName: 'YZA Teknoloji Ltd.',
          startDate: '02.06.2023',
          vehicleCount: 3,
          segment: 'C',
          duration: '24 ay',
          offerAmount: '108.000 ₺',
          status: 'APPROVED',
          approvalDate: '04.06.2023'
        }
      ];

      // İhaleleri durumlarına göre kategorilere ayırıyoruz
      const categorizedTenders = {
        newTenders: mockCompanyTenders.filter(tender => tender.status === 'NEW'),
        pendingTenders: mockCompanyTenders.filter(tender => tender.status === 'PENDING'),
        rejectedTenders: mockCompanyTenders.filter(tender => tender.status === 'REJECTED'),
        approvedTenders: mockCompanyTenders.filter(tender => tender.status === 'APPROVED')
      };

      resolve({
        success: true,
        message: 'Şirket ihaleleri başarıyla getirildi',
        data: categorizedTenders
      });
    }, 1500); // Servis çağrısı simülasyonu için 1.5 saniye gecikme
  });
};

// Kullanılacak servisleri belirleme (mock mu gerçek mi)
const tendersService = {
  getTenders: useMockAPI ? mockGetTenders : getTenders,
  getTenderById: useMockAPI ? mockGetTenderById : getTenderById,
  createTender: useMockAPI ? mockCreateTender : createTender,
  updateTender: useMockAPI ? mockUpdateTender : updateTender,
  deleteTender: useMockAPI ? mockDeleteTender : deleteTender,
  applyToTender: useMockAPI ? mockApplyToTender : applyToTender,
  getUserApplications: useMockAPI ? mockGetUserApplications : getUserApplications,
  getTenderApplications: useMockAPI ? mockGetTenderApplications : getTenderApplications,
  respondToApplication: useMockAPI ? mockRespondToApplication : respondToApplication,
};

export default tendersService;