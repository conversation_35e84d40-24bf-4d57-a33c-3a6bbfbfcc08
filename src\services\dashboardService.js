import api, { useMockAPI } from './api';

// Dashboard kartlarını getiren servis
const getDashboardCards = async () => {
  if (useMockAPI) {
    // Mock veri döndür (gerekirse buraya eklenebilir)
    return {
      result: {
        activeTenders: 3,
        lastWeekIncreaseActiveTenders: 2,
        completedTenders: 0,
        lastWeekIncreaseCompletedTenders: 0,
        totalProcess: 0,
        lastMonthIncreaseTotalProcess: 0,
        wonTenders: 0,
        lastWeekIncreaseWonTenders: 0
      },
      isSuccess: true,
      message: null,
      errorCode: null
    };
  }
  const response = await api.get('/Dashboard/GetDashboardCards');
  return response.data;
};

const dashboardService = {
  getDashboardCards
};

export default dashboardService; 