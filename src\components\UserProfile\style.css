@import url('../../styles/colors.css');

/* User Profile Page Styles */
.user-profile-area {
  padding: 20px 0;
  background-color: var(--gray-50);
}

.profile-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
}

.profile-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.profile-card .card-header {
  background-color: var(--primary-color);
  color: var(--white);
  padding: 15px 20px;
  border-bottom: none;
}

.profile-card .card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.profile-card .card-body {
  padding: 30px;
}

/* <PERSON><PERSON><PERSON>ı<PERSON><PERSON> düzenlem<PERSON> butonu */
.edit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  padding: 0;
  transition: all 0.3s ease;
  background: transparent;
  border: none;
  position: relative;
  z-index: 5;
  cursor: pointer;
}

.edit-icon {
  font-size: 18px;
  color: #ffffff;
  transition: all 0.3s ease;
  z-index: 10;
  display: inline-block;
}

.edit-icon.cancel {
  color: #ffffff;
}

/* İkonlar için animasyonlar */
@keyframes slide-right {
  0% {
    transform: translateX(-2px);
  }
  100% {
    transform: translateX(2px);
  }
}

@keyframes subtle-rotate {
  0% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(10deg);
  }
}

/* İkonları durumuna göre animasyonlar */
.profile-card .card-header .edit-button svg {
  fill: #ffffff;
  stroke: #ffffff;
  font-size: 18px;
  transition: all 0.3s ease;
}

/* Düzenleme butonu hover */
.profile-card .card-header .edit-button:hover svg {
  transform: scale(1.1);
}

/* Düzenleme (kalem) hover animasyonu */
.profile-card .card-header .edit-button:hover svg[data-icon="edit"] {
  animation: subtle-rotate 0.6s ease-in-out alternate infinite;
}

/* İptal butonu hover */
.profile-card .card-header .edit-button:hover svg[data-icon="times"] {
  transform: rotate(90deg);
  transition-duration: 0.4s;
}

/* Düzenleme butonu aktif durumu */
.profile-card .card-header .edit-button:active svg {
  transform: scale(0.9);
  transition-duration: 0.1s;
}

.save-button {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  padding: 8px 15px;
  border-radius: 4px;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.2s ease;
}

.save-button:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.profile-actions {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 30px;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.profile-actions:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.profile-actions h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--gray-800);
  position: relative;
  padding-bottom: 0.5rem;
}

.profile-actions h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.action-buttons .btn {
  position: relative;
  display: inline-block;
  padding: 12px 25px;
  font-weight: 500;
  text-transform: uppercase;
  font-size: 14px;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  z-index: 1;
}

.action-buttons .btn-outline-primary {
  color: var(--gray-800);
  border-color: var(--gray-300);
  background-color: transparent;
}

.action-buttons .btn-outline-primary:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: var(--primary-transparent);
  transform: translateY(-3px);
}

.action-buttons .btn-outline-danger {
  color: var(--danger);
  border-color: var(--danger);
  background-color: transparent;
}

.action-buttons .btn-outline-danger:hover {
  color: var(--white);
  background-color: var(--danger);
  transform: translateY(-3px);
}

.action-buttons .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -20px;
  width: 0;
  height: 100%;
  background-color: var(--primary-transparent);
  transform: skewX(30deg);
  transition: all 0.4s ease;
  z-index: -1;
}

.action-buttons .btn:hover::before {
  width: 160%;
}

.action-buttons .btn-outline-danger:hover::before {
  background-color: transparent;
}

.form-label {
  font-weight: 500;
  color: var(--gray-800);
  display: flex;
  align-items: center;
}

.form-control {
  border-radius: 0.375rem;
  border: 1px solid var(--gray-300);
  padding: 0.5rem 0.75rem;
  transition: all 0.2s ease-in-out;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem var(--primary-transparent);
}

.form-control:disabled {
  background-color: var(--gray-100);
  opacity: 0.9;
}

/* Newly added style definitions */
.verification-badge {
  padding: 0.3rem 0.7rem;
  border-radius: 30px;
  font-size: 0.85rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  transition: all 0.3s ease;
}

.verification-badge:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.verification-badge.verified {
  background-color: var(--soft-success);
  color: var(--success);
}

.verification-badge.unverified {
  background-color: var(--soft-warning);
  color: var(--warning);
}

.verification-alert {
  display: flex;
  align-items: center;
  border-left: 4px solid var(--warning);
  margin-bottom: 20px;
  padding: 1rem;
  background-color: var(--soft-warning);
  border-radius: 0.25rem;
}

.profile-tabs .nav-link {
  color: var(--gray-600);
  font-weight: 500;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.profile-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: transparent;
  border-bottom: 3px solid var(--primary-color);
}

.profile-tabs .nav-link:hover:not(.active) {
  color: var(--primary-color);
  background-color: var(--primary-transparent);
}

.profile-tabs .tab-content {
  padding: 20px 0;
}

/* Security Tab Styles */
.security-section {
  padding: 10px 0;
}

.section-title {
  color: var(--gray-800);
  font-weight: 600;
  position: relative;
  padding-bottom: 10px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

.security-card {
  border-radius: 10px;
  border: 1px solid var(--gray-200);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
}

.security-card:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.security-card-title {
  color: var(--gray-800);
  font-weight: 600;
  display: flex;
  align-items: center;
}

.security-icon {
  color: var(--primary-color);
}

.security-action-btn {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.security-action-btn:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

.security-form-label {
  color: var(--gray-700);
  font-weight: 500;
}

.security-form-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem var(--primary-transparent);
}

.security-cancel-btn {
  transition: all 0.3s ease;
}

.security-cancel-btn:hover {
  background-color: var(--gray-200);
}

.security-save-btn {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transition: all 0.3s ease;
}

.security-save-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.success-alert {
  background-color: var(--soft-success);
  color: var(--success);
  border-color: var(--success);
}

.verification-card .verification-btn {
  background-color: var(--warning);
  border-color: var(--warning);
  color: white;
  transition: all 0.3s ease;
}

.verification-card .verification-btn:hover {
  background-color: #e0a800;
  transform: translateY(-2px);
}

.verification-badge {
  padding: 8px 12px;
  font-size: 0.85rem;
  background-color: var(--success);
}

.danger-card {
  border-left: 4px solid var(--danger);
}

.danger-title {
  color: var(--danger);
}

.danger-icon {
  color: var(--danger);
}

.danger-btn {
  border-color: var(--danger);
  color: var(--danger);
  transition: all 0.3s ease;
}

.danger-btn:hover {
  background-color: var(--danger);
  color: white;
  transform: translateY(-2px);
}

/* User type specific styles */
.customer-profile .card-header {
  background-color: var(--primary-color);
}

.supplier-profile .card-header {
  background-color: var(--primary-color);
}

.admin-profile .card-header {
  background-color: var(--success);
}

.social-media-links {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.social-media-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--gray-100);
  color: var(--gray-600);
  margin: 0 0.5rem;
  transition: all 0.3s ease;
}

.social-media-links a:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-3px);
}


.save-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-top-color: #fff;
  animation: spin 0.8s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .btn {
    width: 100%;
    margin-bottom: 10px;
  }

  .profile-tabs .nav-link {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .profile-actions {
    padding: 20px;
  }

  .profile-card .card-body {
    padding: 20px;
  }
}

.profile-header {
  position: relative;
  padding: 20px;
}

.profile-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
}

.profile-avatar {
  position: relative;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--white);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  border: 3px solid var(--white);
  margin-bottom: 15px;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 32px;
  font-weight: 600;
}

.profile-info {
  text-align: center;
  width: 100%;
}

.profile-info p {
  margin-bottom: 10px;
  opacity: 0.85;
}

.user-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-right: 8px;
}

.verified-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: var(--success);
  color: white;
  border-radius: 50%;
  font-size: 10px;
  transform: translateY(-2px);
}

.profile-name-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

/* Responsive styles for profile summary */
@media (min-width: 768px) {
  .profile-avatar-container {
    flex-direction: row;
    text-align: left;
    justify-content: flex-start;
  }
  
  .profile-info {
    margin-left: 20px;
    text-align: left;
  }
  
  .profile-name-container {
    justify-content: flex-start;
  }
}

.profile-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(var(--primary-color-rgb), 0.15);
}

.profile-btn-primary:hover, .profile-btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.25);
}

.profile-btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(var(--primary-color-rgb), 0.15);
}

.profile-btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(var(--secondary-color-rgb), 0.15);
}

.profile-btn-secondary:hover, .profile-btn-secondary:focus {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--secondary-color-rgb), 0.25);
}

.profile-btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(var(--secondary-color-rgb), 0.15);
}

.profile-btn-outline-primary {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.profile-btn-outline-primary:hover, .profile-btn-outline-primary:focus {
  background-color: var(--soft-primary);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.15);
}

.profile-btn-outline-primary:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(var(--primary-color-rgb), 0.1);
}

.profile-btn-outline-secondary {
  background-color: transparent;
  border: 2px solid var(--secondary-color);
  color: var(--secondary-color);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.profile-btn-outline-secondary:hover, .profile-btn-outline-secondary:focus {
  background-color: var(--gray-100);
  border-color: var(--secondary-color);
  color: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--secondary-color-rgb), 0.15);
}

.profile-btn-outline-secondary:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(var(--secondary-color-rgb), 0.1);
}

.profile-btn-warning {
  background-color: var(--warning);
  border-color: var(--warning);
  color: var(--gray-900);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.25);
}

.profile-btn-warning:hover, .profile-btn-warning:focus {
  background-color: #e6ac00;
  border-color: #e6ac00;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 193, 7, 0.35);
}

.profile-btn-warning:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(255, 193, 7, 0.25);
}

.profile-btn-outline-danger {
  background-color: transparent;
  border: 2px solid var(--danger);
  color: var(--danger);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.profile-btn-outline-danger:hover, .profile-btn-outline-danger:focus {
  background-color: var(--soft-danger);
  border-color: var(--danger);
  color: var(--danger);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.15);
}

.profile-btn-outline-danger:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(220, 53, 69, 0.1);
}

.profile-btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.profile-btn-lg {
  padding: 0.75rem 1.25rem;
  font-size: 1.125rem;
}

.profile-btn-block {
  width: 100%;
}

.profile-btn-icon-only {
  width: 38px;
  height: 38px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.profile-btn-icon-only.profile-btn-sm {
  width: 32px;
  height: 32px;
}

.profile-btn-icon-only.profile-btn-lg {
  width: 48px;
  height: 48px;
}

/* Buton gruplama */
.profile-btn-group {
  display: flex;
  gap: 0.5rem;
}

/* Profile summary and sidebar styles - New Design */
.profile-summary-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
  background-color: var(--primary-color);
  color: var(--white);
  margin-bottom: 20px;
}

.profile-summary-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-3px);
}

.profile-summary-card .card-body {
  padding: 0;
}

.profile-header {
  position: relative;
  padding: 20px;
}

.profile-avatar {
  position: relative;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--white);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  border: 3px solid var(--white);
  margin-bottom: 15px;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: 32px;
  font-weight: 600;
}

.profile-info {
  text-align: center;
  width: 100%;
}

.profile-info p {
  margin-bottom: 10px;
  opacity: 0.85;
}

/* Hero bileşenindeki buton tasarımı */
.gauto-btn-white {
  background-color: #fff;
  color: #333;
  padding: 10px 20px;
  border-radius: 3px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  margin: 0;
}

.gauto-btn-white:hover {
  background-color: #f5f5f5;
  color: var(--primary-color);
}

/* Profile sayfası için özel buton stilleri */
.profile-white-btn {
  background-color: #fff;
  color: #333;
  border: 2px solid var(--gray-300);
  padding: 8px 15px;
  border-radius: 3px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  gap: 5px;
}

.profile-white-btn:hover {
  background-color: #f5f5f5;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.profile-white-btn.btn-sm {
  padding: 5px 10px;
  font-size: 0.875rem;
}

.profile-primary-btn {
  background-color: var(--primary-color);
  color: #fff;
  border: 2px solid var(--primary-color);
  padding: 8px 15px;
  border-radius: 3px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  gap: 5px;
}

.profile-primary-btn:hover {
  background-color: var(--primary-dark);
  color: #fff;
}

.profile-primary-btn.btn-sm {
  padding: 5px 10px;
  font-size: 0.875rem;
}