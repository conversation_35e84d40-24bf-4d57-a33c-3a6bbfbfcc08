import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import AboutPageList from "../../components/AboutPage";
import Promo from "../../components/Promo";
import Advantages from "../../components/Advantages";

const AboutPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.about")}
        pagesub={t("header-navigation.about")}
      />
      <AboutPageList />
      <Promo />
      <Advantages />
    </Fragment>
  );
};
export default AboutPage;
