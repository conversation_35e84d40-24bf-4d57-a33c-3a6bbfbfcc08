import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import PageTitle from "../../components/PageTitle";
import UserProfile from "../../components/UserProfile";
import { USER_ROLES } from "../../constants/userRoles";

const ProfilePage = () => {
  const { t } = useTranslation();
  const { user } = useSelector((state) => state.auth);

  // Kullanıcı bilgilerini kontrol et
  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("profile_page.title")}
        pagesub={t("profile_page.subtitle")}
      />
      <UserProfile user={user} />
    </Fragment>
  );
};

export default ProfilePage; 