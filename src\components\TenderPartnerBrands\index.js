import React, { useEffect, useState } from "react";
import { Contain<PERSON>, <PERSON>, <PERSON>, Badge, OverlayTrigger, Toolt<PERSON> } from "react-bootstrap";
import Slider from "react-slick";
import { useTranslation } from "react-i18next";
import { 
  <PERSON>a<PERSON><PERSON><PERSON>, 
  Fa<PERSON><PERSON>ner, 
  FaTrophy, 
  FaGavel, 
  FaMedal, 
  FaStar, 
  FaHandshake 
} from "react-icons/fa";

import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./style.css";

const TenderPartnerBrands = ({ carId }) => {
  const { t } = useTranslation();
  const [partners, setPartners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Slider ayarları
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 5,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 4000,
    pauseOnHover: true,
    swipeToSlide: true,
    centerPadding: "0px",
    arrows: false,
    rtl: false,
    cssEase: "ease-out",
    draggable: true,
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          dots: false
        }
      }
    ]
  };

  // Bu fonksiyon servis çağrısını simüle eder
  const fetchPartnerBrands = async (carId) => {
    setLoading(true);
    try {
      // Gerçek uygulamada bir API çağrısı yapılacak
      // Örnek: const response = await api.get(`/tender/partners?carId=${carId}`);
      
      // Simüle edilmiş veri
      await new Promise(resolve => setTimeout(resolve, 1000)); // 1 saniye gecikme
      
      const mockPartners = [
        {
          id: 1,
          name: "RentGo",
          logo: "https://scontent-ist1-1.xx.fbcdn.net/v/t39.30808-6/482244032_1054487450045965_8228701877837674381_n.jpg?_nc_cat=102&ccb=1-7&_nc_sid=6ee11a&_nc_ohc=2ljayepr5a0Q7kNvgE8efMW&_nc_oc=AdjpreVsM0Q__zb38zVg-ItgO8bgOu-xwfrZqfyW5Sk2H9eYE1PBpI2U60mh6tyD2Us&_nc_zt=23&_nc_ht=scontent-ist1-1.xx&_nc_gid=2KkwlcsH5KWHb31-LPLI4w&oh=00_AYFHsbNDcUy8tNwMmDynwzAoBDH8KyI9-nDEyd-BOk7PQA&oe=67DE46E4",
          featured: true,
          tendersParticipated: 183,
          tendersWon: 97,
          rating: 4.7,
          foundedYear: 2007
        },
        {
          id: 2,
          name: "Zeplin Car Rental",
          logo: "https://play-lh.googleusercontent.com/m5r9nPMT67oV9SfQYVfPOJhyCIaRuNlpYYSkZ_Ri2xLjLmjnqBsI6Pf7_CY3d4fiD3Y",
          tendersParticipated: 142,
          tendersWon: 68,
          rating: 4.2,
          foundedYear: 2001
        },
        {
          id: 3,
          name: "Garenta",
          logo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQD6OphsahkvWg8MXdLyIbDzjsXLVL2Y3LR3w&s",
          tendersParticipated: 98,
          tendersWon: 45,
          rating: 4.0,
          foundedYear: 2014
        },
        {
          id: 4,
          name: "Enterprise Türkiye",
          logo: "https://is1-ssl.mzstatic.com/image/thumb/Purple221/v4/99/73/7d/99737d94-9eab-f4de-1b00-c620cc771870/AppIcon-0-0-1x_U007emarketing-0-7-0-85-220.png/230x0w.webp",
          tendersParticipated: 156,
          tendersWon: 89,
          rating: 4.8,
          foundedYear: 2014
        },
        {
          id: 5,
          name: "Sixt Türkiye",
          logo: "https://media.licdn.com/dms/image/v2/D4D0BAQEnBpp4lvZ3ow/company-logo_200_200/company-logo_200_200/0/1707307795317/sixttr_logo?e=1750291200&v=beta&t=MGDq2F0yj3WhLW_aot8yCusXIXZ3sGCnclJHhVVii5k",
          featured: true,
          tendersParticipated: 167,
          tendersWon: 92,
          rating: 4.9,
          foundedYear: 1912
        },
        {
          id: 6,
          name: "Hertz Türkiye",
          logo: "https://static.hertz.com.tr/Static/MediaFiles/Campaign/22_300/hertz-com-thumbnail.jpg",
          tendersParticipated: 134,
          tendersWon: 76,
          rating: 4.5,
          foundedYear: 1918
        },
        {
          id: 7,
          name: "Avis Türkiye",
          logo: "https://play-lh.googleusercontent.com/YPo8CrBD8j3z5HwBJKVLyhbL0CHrhgvTjuLadCBe5v6BlfPGbKwkUkYt2O73zDqs_RI",
          tendersParticipated: 145,
          tendersWon: 83,
          rating: 4.6,
          foundedYear: 1946
        },
        {
          id: 8,
          name: "Budget Türkiye",
          logo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRpzhMSDrYKxg6ZsT-WgALxEJbllXhHcvxZRg&s",
          tendersParticipated: 129,
          tendersWon: 65,
          rating: 4.3,
          foundedYear: 1958
        },
        {
          id: 9,
          name: "Europcar Türkiye",
          logo: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQFy2MBdqORlfg6ALJ_AordvyNJc_XajnhmlA&s",
          tendersParticipated: 118,
          tendersWon: 59,
          rating: 4.4,
          foundedYear: 1949
        }
      ];
      
      setPartners(mockPartners);
      setLoading(false);
    } catch (err) {
      console.error("Partner markaları getirme hatası:", err);
      setError(t("Firma bilgileri yüklenirken bir hata oluştu."));
      setLoading(false);
    }
  };

  useEffect(() => {
    if (carId) {
      fetchPartnerBrands(carId);
    } else {
      // Eğer carId yoksa, varsayılan olarak tüm partnerları getir
      fetchPartnerBrands("all");
    }
  }, [carId]);

  // Yıldız derecelendirme bileşeni
  const RatingStars = ({ rating }) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    
    return (
      <div className="partner-rating">
        {[...Array(fullStars)].map((_, i) => (
          <FaStar key={i} className="star-filled" />
        ))}
        {hasHalfStar && <FaStar className="star-half" />}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="partner-brands-loading">
        <FaSpinner className="fa-spin" />
        <p>{t("Firmalar yükleniyor...")}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="partner-brands-error">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <section className="partner-brands-section">
      <Container>
        <div className="partner-brands-heading">
          <h3>{t("Bu Araca Teklif Verebilecek Firmalar")}</h3>
          <p>{t("Araç için ihaleye katılabilecek ve en iyi teklifleri sunma potansiyeli olan firmalar")}</p>
        </div>
        
        {partners.length > 0 ? (
          <Slider {...sliderSettings} className="partner-brands-slider">
            {partners.map(partner => (
              <div key={partner.id} className="partner-brand-item">
                <div className="partner-card">
                  {partner.featured && (
                    <div className="featured-badge">
                      <FaTrophy />
                    </div>
                  )}
                  
                  <div className="partner-card-header">
                    <div className="partner-logo-wrapper">
                      {partner.logo ? (
                        <img 
                          src={partner.logo} 
                          alt={partner.name} 
                          className="partner-logo" 
                        />
                      ) : (
                        <div className="partner-logo-placeholder">
                          <FaBuilding size={35} />
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="partner-card-body">
                    <h4 className="partner-name">{partner.name}</h4>
                    
                    <div className="partner-stats">
                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>{t("Katıldığı İhale Sayısı")}</Tooltip>}
                      >
                        <div className="stat-item">
                          <FaGavel className="stat-icon" />
                          <div className="stat-content">
                            <span className="stat-value">{partner.tendersParticipated}</span>
                          </div>
                        </div>
                      </OverlayTrigger>
                      
                      <OverlayTrigger
                        placement="top"
                        overlay={<Tooltip>{t("Kazandığı İhale Sayısı")}</Tooltip>}
                      >
                        <div className="stat-item">
                          <FaMedal className="stat-icon win" />
                          <div className="stat-content">
                            <span className="stat-value">{partner.tendersWon}</span>
                          </div>
                        </div>
                      </OverlayTrigger>
                    </div>
                    
                    <div className="partner-footer">
                      <div className="since">
                        <FaHandshake className="since-icon" />
                        <span>{t("Kuruluş")}: {partner.foundedYear}</span>
                      </div>
                      <RatingStars rating={partner.rating} />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        ) : (
          <div className="no-partners-found">
            <p>{t("Bu araca teklif verebilecek firma bulunamadı.")}</p>
          </div>
        )}
      </Container>
    </section>
  );
};

export default TenderPartnerBrands; 