import React from 'react';
import { Badge } from 'react-bootstrap';
import { FaMoneyBillWave, FaHourglassHalf, FaTimesCircle, FaCheckCircle, FaFileAlt } from 'react-icons/fa';
import { getStatusClass, getStatusText } from '../utils/statusHelpers';

/**
 * <PERSON>hale veya teklif durumunu gösteren rozet bileşeni
 * @param {object} props - Bileşen propsları
 * @param {string} props.status - Durum metni (active, pending, completed, vs.)
 * @param {string} props.className - Ekstra CSS sınıfı
 */
const StatusBadge = ({ status, className = '' }) => {
  // Durum ikonunu belirle
  const getStatusIcon = (status) => {
    if (!status) return null;
    
    switch (status.toUpperCase()) {
      case "SUBMITTED":
      case "ACTIVE":
        return <FaMoneyBillWave className="me-1" />;
      case "PENDING":
        return <FaHourglassHalf className="me-1" />;
      case "REJECTED":
      case "CANCELLED":
        return <FaTimesCircle className="me-1" />;
      case "APPROVED":
      case "COMPLETED":
        return <FaCheckCircle className="me-1" />;
      case "DRAFT":
        return <FaFileAlt className="me-1" />;
      default:
        return null;
    }
  };

  return (
    <Badge className={`tender-status ${getStatusClass(status || '')} ${className}`}>
      {getStatusIcon(status)} {getStatusText(status)}
    </Badge>
  );
};

export default StatusBadge; 