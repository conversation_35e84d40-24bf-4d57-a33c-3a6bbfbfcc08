@import url('../../styles/colors.css');

/* ===================== ADVANTAGES ======================*/
.advantages-area {
  position: relative;
  background-color: #f7f7f7;
}

.single-advantage-box {
  background-color: #fff;
  border-radius: 5px;
  padding: 30px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  transition: all 0.3s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.single-advantage-box:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
  transform: translateY(-5px);
}

.advantage-box-icon {
  font-size: 40px;
  color: var(--primary-color);
  margin-bottom: 20px;
  display: inline-block;
}

.advantage-box-text h3 {
  font-size: 20px;
  color: #001238;
  margin-bottom: 15px;
  font-weight: 600;
}

.advantage-box-text p {
  color: #666;
  margin-bottom: 0;
  font-size: 15px;
  line-height: 1.6;
}

@media (min-width: 992px) and (max-width: 1169px) {
  .advantage-box-text h3 {
    font-size: 18px;
  }
  .advantage-box-text p {
    font-size: 14px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .advantage-box-text h3 {
    font-size: 17px;
  }
  .advantage-box-text p {
    font-size: 13px;
  }
  .advantage-box-icon {
    font-size: 35px;
  }
}

@media (max-width: 767px) {
  .single-advantage-box {
    margin-bottom: 20px;
  }
  .advantage-box-text h3 {
    font-size: 18px;
  }
}

.gauto-about-content-left .gauto-about-list li i {
  color: var(--primary-color);
} 