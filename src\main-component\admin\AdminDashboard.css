.admin-dashboard {
  background-color: #f8f9fa;
  min-height: calc(100vh - 70px); /* <PERSON><PERSON>'<PERSON><PERSON> <PERSON><PERSON><PERSON> g<PERSON> */
}

.dashboard-card {
  border-radius: 10px;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #e7f1ff;
  color: #0d6efd;
}

.count {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: #333;
}

.label {
  font-size: 0.9rem;
  color: #6c757d;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.table td {
  vertical-align: middle;
}

.badge {
  padding: 0.5em 0.8em;
  font-weight: 500;
}

@media (max-width: 768px) {
  .count {
    font-size: 1.75rem;
  }
} 