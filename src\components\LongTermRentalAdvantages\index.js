import React from "react";
import { Container } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { 
  FaMoneyBillWave, 
  FaClock, 
  FaFileInvoiceDollar,
  FaTools, 
  FaUserTie, 
  FaLaptop, 
  FaPercent,
  FaChartLine,
  FaShieldAlt
} from "react-icons/fa";
import SectionTitle from "../common/SectionTitle";

import "./style.css";

const LongTermRentalAdvantages = () => {
  const { t } = useTranslation();

  const advantages = [
    {
      icon: <FaMoneyBillWave />,
      title: t("advantages.capitalPreservation.title"),
      description: t("advantages.capitalPreservation.description")
    },
    {
      icon: <FaFileInvoiceDollar />,
      title: t("advantages.fixedCost.title"),
      description: t("advantages.fixedCost.description")
    },
    {
      icon: <FaTools />,
      title: t("advantages.maintenanceEase.title"),
      description: t("advantages.maintenanceEase.description")
    },
    {
      icon: <FaLaptop />,
      title: t("advantages.remoteAccess.title"),
      description: t("advantages.remoteAccess.description")
    },
    {
      icon: <FaUserTie />,
      title: t("advantages.resourceOptimization.title"),
      description: t("advantages.resourceOptimization.description")
    },
    {
      icon: <FaPercent />,
      title: t("advantages.taxBenefit.title"),
      description: t("advantages.taxBenefit.description")
    },
    {
      icon: <FaClock />,
      title: t("advantages.fastDelivery.title"),
      description: t("advantages.fastDelivery.description")
    },
    {
      icon: <FaChartLine />,
      title: t("advantages.financialFlexibility.title"),
      description: t("advantages.financialFlexibility.description")
    },
    {
      icon: <FaShieldAlt />,
      title: t("advantages.riskManagement.title"),
      description: t("advantages.riskManagement.description")
    }
  ];

  return (
    <section className="advantages-section">
      <Container>
        <SectionTitle
          title={t("advantages.header.title")}
          description={t("advantages.header.description")}
          alignment="center"
        />
        
        <div className="advantages-container">
          {advantages.map((advantage, index) => (
            <div className="advantage-item" key={index}>
              <div className="advantage-icon">{advantage.icon}</div>
              <div className="advantage-content">
                <h3>{advantage.title}</h3>
                <p>{advantage.description}</p>
              </div>
            </div>
          ))}
        </div>
        
        <div className="advantages-cta">
          <a href="#" className="cta-button">{t("advantages.cta")}</a>
        </div>
      </Container>
    </section>
  );
};

export default LongTermRentalAdvantages; 