import React from 'react';
import TenderBidDetail from '../../../components/TenderBidDetail';
import PageTitle from '../../../components/PageTitle';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

/**
 * Kiralama şirketi teklif sayfası
 */
const TenderBidPage = () => {
  const { t } = useTranslation();
  const { tenderId } = useParams();
  
  return (
    <div className="wrapper">
      <PageTitle
        pageTitle={t('tender.bid.page_title')}
        pagesub={t('tender.bid.page_subtitle')}
      />
      <TenderBidDetail tenderId={tenderId} />
    </div>
  );
};

export default TenderBidPage; 