import React from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { FaT<PERSON>s, FaClipboardList, FaCheckCircle, FaMoneyBillWave } from 'react-icons/fa';
import './style.css';

/**
 * Dashboard istatistik kartları bileşeni
 */
const DashboardStats = ({ dashboardData }) => {
  const { t } = useTranslation();

  return (
    <section className="dashboard-stats-section">
      <Container fluid="lg">
        <Row className="dashboard-stats-row">
          <Col xl={3} lg={3} md={6} sm={6} xs={12} className="dashboard-stats-col">
            <div className="stat-card">
              <div className="stat-content">
                <div className="stat-title">{t('dashboard.stats.active_tenders')}</div>
                <div className="stat-value">{dashboardData?.myTenders || 0}</div>
                <div className="stat-trend">
                  <span className="trend-icon">↑</span> <span className="trend-value">{dashboardData?.lastWeekIncreaseActiveTenders ?? 0}</span> <span className="trend-text">{t('dashboard.stats.new')}</span> <span className="trend-period">{t('dashboard.stats.last_7_days')}</span>
                </div>
              </div>
              <div className="stat-icon-wrap">
                <div className="stat-icon-container icon-bg-orange">
                  <FaTools className="stat-icon" />
                </div>
              </div>
            </div>
          </Col>
          
          <Col xl={3} lg={3} md={6} sm={6} xs={12} className="dashboard-stats-col">
            <div className="stat-card">
              <div className="stat-content">
                <div className="stat-title">{t('dashboard.stats.given_bids')}</div>
                <div className="stat-value">{dashboardData?.openTenders || 0}</div>
                <div className="stat-trend">
                  <span className="trend-icon">↑</span> <span className="trend-value">5</span> <span className="trend-text">{t('dashboard.stats.new')}</span> <span className="trend-period">{t('dashboard.stats.last_7_days')}</span>
                </div>
              </div>
              <div className="stat-icon-wrap">
                <div className="stat-icon-container icon-bg-dark">
                  <FaClipboardList className="stat-icon" />
                </div>
              </div>
            </div>
          </Col>
          
          <Col xl={3} lg={3} md={6} sm={6} xs={12} className="dashboard-stats-col">
            <div className="stat-card">
              <div className="stat-content">
                <div className="stat-title">{t('dashboard.stats.won_tenders')}</div>
                <div className="stat-value">{dashboardData?.activeBids || 0}</div>
                <div className="stat-trend">
                  <span className="trend-icon">↑</span> <span className="trend-value">{dashboardData?.lastWeekIncreaseWonTenders ?? 0}</span> <span className="trend-text">{t('dashboard.stats.new')}</span> <span className="trend-period">{t('dashboard.stats.last_7_days')}</span>
                </div>
              </div>
              <div className="stat-icon-wrap">
                <div className="stat-icon-container icon-bg-green">
                  <FaCheckCircle className="stat-icon" />
                </div>
              </div>
            </div>
          </Col>
          
          <Col xl={3} lg={3} md={6} sm={6} xs={12} className="dashboard-stats-col">
            <div className="stat-card">
              <div className="stat-content">
                <div className="stat-title">{t('dashboard.stats.total_transaction')}</div>
                <div className="stat-value">₺{Number(dashboardData?.totalProcess ?? 0).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</div>
                <div className="stat-trend">
                  <span className="trend-icon">↑</span> <span className="trend-value">₺{Number(dashboardData?.lastMonthIncreaseTotalProcess ?? 0).toLocaleString('tr-TR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</span> <span className="trend-period">{t('dashboard.stats.last_month')}</span>
                </div>
              </div>
              <div className="stat-icon-wrap">
                <div className="stat-icon-container icon-bg-blue">
                  <FaMoneyBillWave className="stat-icon" />
                </div>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default DashboardStats; 