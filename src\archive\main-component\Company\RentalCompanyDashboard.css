@import url('../../styles/colors.css');

/* Genel Dashboard Stilleri */
.section_70 {
  padding: 70px 0;
}

@media (max-width: 768px) {
  .section_70 {
    padding: 40px 0;
  }
}

.dashboard-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

/* Dashboard Cards Bölümü */
.dashboard-cards-section {
  background-color: #f8f9fa;
  padding-top: 30px;
  padding-bottom: 50px;
}

.dashboard-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.07);
  transition: all 0.4s ease;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.dashboard-card:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(var(--primary-color-rgb), 0.02) 0%, rgba(var(--primary-color-rgb), 0.05) 100%);
  z-index: -1;
  transition: opacity 0.4s ease;
  opacity: 0;
}

.dashboard-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px var(--primary-shadow);
}

.dashboard-card:hover:before {
  opacity: 1;
}

.dashboard-card:after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  height: 5px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  transform: scaleX(0);
  transform-origin: 0 0;
  transition: transform 0.4s ease;
}

.dashboard-card:hover:after {
  transform: scaleX(1);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: #ffffff;
  color: var(--primary-color);
  box-shadow: 0 6px 15px var(--primary-shadow);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.icon-container svg {
  font-size: 30px;
  color: var(--primary-color);
}

.count {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.35rem;
  color: #333;
  background: linear-gradient(90deg, #001238, #001f63);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -1px;
}

.label {
  font-size: 1rem;
  color: #6c757d;
  font-weight: 500;
  position: relative;
  display: inline-block;
}

.label:after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  transform: translateX(-50%);
}

/* Ana Sekmeler ve Grafikler */
.dashboard-main-section {
  background-color: #fff;
  padding-top: 20px;
  padding-bottom: 40px;
}

.pricing-info-section {
  background-color: #f8f9fa;
  padding-top: 30px;
  padding-bottom: 70px;
}

.chart-card {
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
  border: none;
  overflow: hidden;
}

.chart-card .card-header {
  background: linear-gradient(90deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 20px 25px;
}

.chart-card .card-header h5 {
  font-weight: 700;
  color: #001238;
  position: relative;
  display: inline-block;
  padding-left: 15px;
}

.chart-card .card-header h5:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 20px;
  background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
  border-radius: 3px;
}

.chart-card .card-body {
  padding: 25px;
  background-color: #ffffff;
}

/* Başlık Stilleri */
.dashboard-heading {
  margin-bottom: 30px !important;
}

.site-heading {
  text-align: center;
  width: 60%;
  margin-left: auto;
  margin-right: auto;
}

.site-heading h4 {
  position: relative;
  display: inline-block;
  font-size: 20px;
  color: var(--primary-color);
  margin-bottom: 10px;
  font-weight: 500;
  text-transform: capitalize;
  padding-left: 0;
  margin-left: 0;
}

.site-heading h2 {
  font-size: 40px;
  color: #001238;
  position: relative;
  margin-bottom: 30px;
  letter-spacing: 1px;
  display: inline-block;
  text-transform: capitalize;
  font-weight: 600;
}

.site-heading h2:after {
  content: "";
  position: absolute;
  width: 70px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
}

/* Tab Stilleri */
.dashboard-tabs .nav-link {
  color: #495057;
  border: none;
  padding: 0.75rem 1rem;
  font-weight: 500;
}

.dashboard-tabs .nav-link.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: transparent;
}

.tender-nav {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 0.5rem;
}

.tender-nav .nav-link {
  border-radius: 6px;
  padding: 0.5rem 1rem;
  color: #495057;
  margin-right: 0.25rem;
}

.tender-nav .nav-link.active {
  background-color: var(--primary-color);
  color: white;
}

/* Tablo Stilleri */
.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
  background-color: #f8f9fa;
}

.table td {
  vertical-align: middle;
  padding: 0.75rem;
}

.badge {
  padding: 0.4em 0.7em;
  font-weight: 500;
  border-radius: 4px;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Mobil Uyumluluk */
@media (max-width: 991px) {
  .chart-tabs {
    flex-wrap: wrap;
  }
  
  .site-heading {
    width: 90%;
  }
  
  .site-heading h2 {
    font-size: 32px;
  }
}

@media (max-width: 768px) {
  .count {
    font-size: 2rem;
  }
  
  .dashboard-cards-section .site-heading {
    width: 100%;
  }
  
  .icon-container {
    width: 60px;
    height: 60px;
  }
  
  .site-heading h4 {
    font-size: 18px;
  }
  
  .site-heading h2 {
    font-size: 28px;
  }
}

.stat-card-gradient {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  color: white;
  border: none;
}

.rental-company-page .nav-link.active {
  color: var(--primary-color);
  font-weight: 500;
}

.rental-company-page .nav-link:hover {
  color: var(--primary-color);
}

.action-card {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-stats-vertical {
  background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 10px;
  padding: 25px;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.company-notification .notification-item .notification-time {
  color: var(--primary-color);
  font-size: 13px;
}

.activity-section {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.tender-list-container .nav-pills .nav-link.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background: transparent;
}

.action-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
} 