import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

// Async thunk action creators
export const fetchCart = createAsyncThunk(
  'cart/fetchCart',
  async (_, { rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.get('/cart');
      
      // Sepet bilgilerini localStorage'dan alalım (Gerçek uygulamada API'dan gelecek)
      const cartItems = localStorage.getItem('cartItems');
      
      return cartItems ? JSON.parse(cartItems) : [];
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Sepet verileri alınamadı');
    }
  }
);

export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async (item, { getState, rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.post('/cart', item);
      
      // Mevcut sepeti al
      const { cart } = getState();
      const existingItem = cart.items.find((i) => i.id === item.id);
      
      let updatedItems;
      if (existingItem) {
        // Eğer ürün zaten sepette varsa miktarını arttır
        updatedItems = cart.items.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        );
      } else {
        // Yeni ürün ekle
        updatedItems = [...cart.items, { ...item, quantity: 1 }];
      }
      
      // localStorage'a kaydet (Gerçek uygulamada API'ya gönderilecek)
      localStorage.setItem('cartItems', JSON.stringify(updatedItems));
      
      return updatedItems;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Ürün sepete eklenemedi');
    }
  }
);

export const removeFromCart = createAsyncThunk(
  'cart/removeFromCart',
  async (itemId, { getState, rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.delete(`/cart/${itemId}`);
      
      // Mevcut sepeti al
      const { cart } = getState();
      const updatedItems = cart.items.filter((item) => item.id !== itemId);
      
      // localStorage'a kaydet (Gerçek uygulamada API'ya gönderilecek)
      localStorage.setItem('cartItems', JSON.stringify(updatedItems));
      
      return updatedItems;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Ürün sepetten çıkarılamadı');
    }
  }
);

export const updateCartItem = createAsyncThunk(
  'cart/updateCartItem',
  async ({ itemId, quantity }, { getState, rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.put(`/cart/${itemId}`, { quantity });
      
      // Mevcut sepeti al
      const { cart } = getState();
      const updatedItems = cart.items.map((item) =>
        item.id === itemId ? { ...item, quantity } : item
      );
      
      // localStorage'a kaydet (Gerçek uygulamada API'ya gönderilecek)
      localStorage.setItem('cartItems', JSON.stringify(updatedItems));
      
      return updatedItems;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Sepet güncellenemedi');
    }
  }
);

export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (_, { rejectWithValue }) => {
    try {
      // Bu kısımda gerçek API isteği yapılacak
      // const response = await api.delete('/cart');
      
      // localStorage'dan sepeti temizle (Gerçek uygulamada API'ya gönderilecek)
      localStorage.removeItem('cartItems');
      
      return [];
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Sepet temizlenemedi');
    }
  }
);

// Helper function
const calculateTotals = (items) => {
  return items.reduce(
    (acc, item) => {
      const itemTotal = item.price * item.quantity;
      return {
        subtotal: acc.subtotal + itemTotal,
        itemCount: acc.itemCount + item.quantity,
      };
    },
    { subtotal: 0, itemCount: 0 }
  );
};

// Initial State
const initialState = {
  items: [],
  subtotal: 0,
  itemCount: 0,
  loading: false,
  error: null,
};

// Cart Slice
const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // fetchCart
    builder
      .addCase(fetchCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        const totals = calculateTotals(action.payload);
        state.subtotal = totals.subtotal;
        state.itemCount = totals.itemCount;
      })
      .addCase(fetchCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // addToCart
    builder
      .addCase(addToCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        const totals = calculateTotals(action.payload);
        state.subtotal = totals.subtotal;
        state.itemCount = totals.itemCount;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // removeFromCart
    builder
      .addCase(removeFromCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        const totals = calculateTotals(action.payload);
        state.subtotal = totals.subtotal;
        state.itemCount = totals.itemCount;
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // updateCartItem
    builder
      .addCase(updateCartItem.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCartItem.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        const totals = calculateTotals(action.payload);
        state.subtotal = totals.subtotal;
        state.itemCount = totals.itemCount;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // clearCart
    builder
      .addCase(clearCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(clearCart.fulfilled, (state) => {
        state.loading = false;
        state.items = [];
        state.subtotal = 0;
        state.itemCount = 0;
      })
      .addCase(clearCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError } = cartSlice.actions;

export default cartSlice.reducer; 