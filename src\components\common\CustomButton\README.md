# CustomButton Bileşeni

Bu bile<PERSON>en, projede kullanılan tüm butonlar için standart bir aray<PERSON>z sağlar. Farklı varyasyonları ve boyutları destekler.

## Özellikler

| Prop | Tip | Varsayılan | Açıklama |
|------|-----|------------|----------|
| variant | string | 'primary' | Butonun görünümü: 'primary', 'secondary', 'outline', 'white', 'success', 'danger', 'warning' |
| size | string | 'md' | Butonun boyutu: 'sm', 'md', 'lg' |
| fullWidth | boolean | false | Butonun tam genişlikte olup olmayacağı |
| to | string | - | Link olarak kullanılacaksa hedef URL (isteğe bağlı) |
| type | string | 'button' | Button tipi: 'button', 'submit', 'reset' |
| disabled | boolean | false | Buton devre dışı mı? |
| onClick | function | - | Tıklama olayı işleyicisi |
| className | string | '' | Ek CSS sınıfları |
| icon | node | - | Buton içerisinde gösterilecek ikon |
| iconPosition | string | 'left' | İkon pozisyonu: 'left', 'right' |

## Kullanım Örnekleri

### Normal Buton

```jsx
import { CustomButton } from '../common';

// Temel kullanım
<CustomButton>Tıkla</CustomButton>

// Primary buton
<CustomButton variant="primary">Kaydet</CustomButton>

// Secondary buton
<CustomButton variant="secondary">İptal</CustomButton>
```

### İkonlu Buton

```jsx
import { CustomButton } from '../common';
import { FaSearch } from 'react-icons/fa';

// Solda ikon
<CustomButton icon={<FaSearch />}>Ara</CustomButton>

// Sağda ikon
<CustomButton 
  icon={<FaSearch />} 
  iconPosition="right"
>
  Ara
</CustomButton>
```

### Link Buton

```jsx
import { CustomButton } from '../common';

// Link olarak
<CustomButton to="/urunler">Ürünlere Git</CustomButton>
```

### Farklı Boyutlar

```jsx
import { CustomButton } from '../common';

// Küçük buton
<CustomButton size="sm">Küçük</CustomButton>

// Orta buton (varsayılan)
<CustomButton size="md">Orta</CustomButton>

// Büyük buton
<CustomButton size="lg">Büyük</CustomButton>
```

### Diğer Varyasyonlar

```jsx
import { CustomButton } from '../common';

// Outline buton
<CustomButton variant="outline">Outline</CustomButton>

// Beyaz buton (Koyu arka planlarda)
<CustomButton variant="white">Beyaz</CustomButton>

// Aktif beyaz buton
<CustomButton variant="white" className="active">Aktif Beyaz</CustomButton>

// Başarı butonu
<CustomButton variant="success">Başarılı</CustomButton>

// Tehlike butonu
<CustomButton variant="danger">Tehlike</CustomButton>

// Uyarı butonu
<CustomButton variant="warning">Uyarı</CustomButton>
```

### Tam Genişlik Buton

```jsx
import { CustomButton } from '../common';

// Tam genişlikte buton
<CustomButton fullWidth>Tam Genişlik</CustomButton>
```

### Form Gönderme Butonu

```jsx
import { CustomButton } from '../common';

<form onSubmit={handleSubmit}>
  {/* Form içeriği */}
  <CustomButton type="submit">Gönder</CustomButton>
</form>
```

### Devre Dışı Buton

```jsx
import { CustomButton } from '../common';

<CustomButton disabled>Devre Dışı</CustomButton>
``` 