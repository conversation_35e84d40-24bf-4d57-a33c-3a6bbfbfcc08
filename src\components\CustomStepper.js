import React from 'react';
import PropTypes from 'prop-types';

const CustomStepper = ({
  steps,
  activeStep,
  activeColor,
  completeColor,
  defaultColor,
  circleFontColor,
  activeTitleColor,
  completeTitleColor,
  defaultTitleColor,
  size,
  circleFontSize,
  titleFontSize,
  circleTop,
  titleTop,
  completeBarColor,
  barStyle,
  borderStyle,
  onClick
}) => {
  return (
    <div className="custom-stepper-container">
      {steps.map((step, index) => {
        // Adım durumunu belirle
        const isActive = index === activeStep;
        const isCompleted = index < activeStep;
        
        // Renk ve stil belirleme
        const circleColor = isActive 
          ? activeColor 
          : isCompleted 
            ? completeColor 
            : defaultColor;
        
        const titleColor = isActive 
          ? activeTitleColor 
          : isCompleted 
            ? completeTitleColor 
            : defaultTitleColor;
        
        // Çizgi stili (son adım hari<PERSON>)
        const showLine = index < steps.length - 1;
        const lineColor = index < activeStep ? completeColor : defaultColor;
        
        return (
          <div className="step-item" key={index}>
            <div className="step-item-container">
              {/* Adım Numa<PERSON>ı Dairesi */}
              <div 
                className={`step-circle ${isActive ? 'active-circle' : ''} ${isCompleted ? 'completed-circle' : ''}`}
                style={{
                  backgroundColor: circleColor,
                  width: size,
                  height: size,
                  borderRadius: '50%',
                  color: circleFontColor,
                  fontSize: circleFontSize,
                  top: circleTop,
                  lineHeight: `${size}px`,
                  cursor: onClick ? 'pointer' : 'default'
                }}
                onClick={() => onClick && onClick(index)}
              >
                {isCompleted ? '✓' : index + 1}
              </div>
              
              {/* Adım Başlığı */}
              <div 
                className={`step-title ${isActive ? 'active-title' : ''} ${isCompleted ? 'completed-title' : ''}`}
                style={{
                  color: titleColor,
                  fontSize: titleFontSize,
                  top: titleTop,
                  marginTop: titleTop
                }}
              >
                {step.title}
              </div>
              
              {/* Adım Açıklaması (varsa) */}
              {step.description && (
                <div className="step-description">
                  {step.description}
                </div>
              )}
            </div>
            
            {/* Bağlantı Çizgisi */}
            {showLine && (
              <div 
                className={`step-line ${index < activeStep ? 'completed-line' : ''}`}
                style={{
                  backgroundColor: lineColor,
                  height: barStyle === 'solid' ? '3px' : '1px',
                  borderTopStyle: barStyle === 'solid' ? 'none' : borderStyle || 'solid',
                  borderTopWidth: barStyle === 'solid' ? '0' : '1px',
                  borderTopColor: lineColor
                }}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};

CustomStepper.propTypes = {
  steps: PropTypes.array.isRequired,
  activeStep: PropTypes.number,
  activeColor: PropTypes.string,
  completeColor: PropTypes.string,
  defaultColor: PropTypes.string,
  circleFontColor: PropTypes.string,
  activeTitleColor: PropTypes.string,
  completeTitleColor: PropTypes.string,
  defaultTitleColor: PropTypes.string,
  size: PropTypes.number,
  circleFontSize: PropTypes.number,
  titleFontSize: PropTypes.number,
  circleTop: PropTypes.number,
  titleTop: PropTypes.number,
  completeBarColor: PropTypes.string,
  barStyle: PropTypes.string,
  borderStyle: PropTypes.string,
  onClick: PropTypes.func
};

export default CustomStepper; 