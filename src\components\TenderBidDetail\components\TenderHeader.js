import React from 'react';
import { FaCalendarAlt, FaMoneyBillWave, FaRegCalendarAlt, FaCalendarCheck, FaArrowRight, FaHourglassHalf } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import StatusBadge from './StatusBadge';
import { formatDate, formatDetailedDate, formatCurrency } from '../utils/formatters';
import RemainingTimeCounter from './RemainingTimeCounter';

/**
 * İhale başlık ve meta bilgilerini gösteren bileşen
 * @param {object} props - Bileşen propsları
 * @param {object} props.tender - İhale verileri
 */
const TenderHeader = ({ tender }) => {
  const { t } = useTranslation();
  
  if (!tender) return null;
  
  const startDateDetails = formatDetailedDate(tender.startDate);
  const endDateDetails = formatDetailedDate(tender.endDate);
  
  return (
    <div className="tender-header">
      <div className="tender-header-main">
        <h1 className="tender-title">{tender.title}</h1>
        <p className="mb-2">{tender.description}</p>
        <div className="d-flex align-items-center mt-2">
          <div className="me-3">
            <FaCalendarAlt className="me-1 text-muted small-icon" />
            <span className="text-muted small">
              {t('tender.created_at', 'Oluşturulma')}: {formatDate(tender.createdAt)}
            </span>
          </div>
          <div>
            <StatusBadge status={tender.status} />
          </div>
        </div>
      </div>
      
      <div className="tender-meta">
        {/* Tarih aralığı kartları */}
        <div className="meta-cards-container">
          <div className="date-card start-date">
            <div className="date-icon-wrapper">
              <FaRegCalendarAlt className="date-icon start" />
            </div>
            <div className="date-content">
              <div className="date-label">{t('tender.start_date', 'Başlangıç Tarihi')}</div>
              <div className="date-value">
                {startDateDetails.day} {startDateDetails.month} {startDateDetails.year}
              </div>
            </div>
          </div>
          
          <div className="date-connector">
            <div className="connector-line"></div>
            <FaArrowRight className="connector-icon" />
          </div>
          
          <div className="date-card end-date">
            <div className="date-icon-wrapper">
              <FaCalendarCheck className="date-icon end" />
            </div>
            <div className="date-content">
              <div className="date-label">{t('tender.end_date', 'Bitiş Tarihi')}</div>
              <div className="date-value">
                {endDateDetails.day} {endDateDetails.month} {endDateDetails.year}
              </div>
            </div>
          </div>
        </div>
        
        {/* Bütçe ve kalan süre kartları */}
        <div className="meta-cards-container">
          {/* Bütçe aralığı kartı */}
          <div className="date-card budget-card">
            <div className="date-icon-wrapper budget-icon-wrapper">
              <FaMoneyBillWave className="date-icon budget" />
            </div>
            <div className="date-content">
              <div className="date-label">{t('tender.budget_range', 'Bütçe Aralığı')}</div>
              <div className="date-value">
                {formatCurrency(tender.minBudget)} - {formatCurrency(tender.maxBudget)}
              </div>
            </div>
          </div>
          
          <div className="date-connector invisible-connector">
            <div className="connector-line"></div>
          </div>
          
          {/* Kalan süre kartı */}
          <div className="date-card time-card">
            <div className="date-icon-wrapper time-icon-wrapper">
              <FaHourglassHalf className="date-icon time" />
            </div>
            <div className="date-content">
              <div className="date-label">{t('tender.remaining_time', 'Kalan Süre')}</div>
              <div className="countdown-container">
                <RemainingTimeCounter endDate={tender.endDate} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TenderHeader; 