import React from 'react';
import { FaCalendarAlt, FaMoneyBillWave, FaRegCalendarAlt, FaCalendarCheck, FaArrowRight, FaHourglassHalf } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import StatusBadge from './StatusBadge';
import { formatDate, formatDetailedDate, formatCurrency } from '../utils/formatters';
import RemainingTimeCounter from './RemainingTimeCounter';

/**
 * İhale başlık ve meta bilgilerini gösteren bileşen
 * @param {object} props - Bileşen propsları
 * @param {object} props.tender - İhale verileri
 */
const TenderHeader = ({ tender }) => {
  const { t } = useTranslation();

  if (!tender) return null;

  const startDateDetails = formatDetailedDate(tender.startDate);
  const endDateDetails = formatDetailedDate(tender.endDate);

  return (
    <div className="modern-tender-header">
      {/* Başlık ve Durum */}
      <div className="header-top">
        <div className="title-section">
          <h2 className="tender-title-modern">{tender.title}</h2>
          <p className="tender-description">{tender.description}</p>
        </div>
        <div className="status-section">
          <StatusBadge status={tender.status} />
          <div className="created-date">
            <FaCalendarAlt className="me-1" />
            <span>{formatDate(tender.createdAt)}</span>
          </div>
        </div>
      </div>

      {/* Özet Bilgiler */}
      <div className="header-summary">
        <div className="summary-grid">
          {/* Başlangıç Tarihi */}
          <div className="summary-item">
            <div className="summary-icon start-date">
              <FaRegCalendarAlt />
            </div>
            <div className="summary-content">
              <div className="summary-label">{t('tender.start_date', 'Başlangıç')}</div>
              <div className="summary-value">
                {startDateDetails.day} {startDateDetails.month}
              </div>
            </div>
          </div>

          {/* Bitiş Tarihi */}
          <div className="summary-item">
            <div className="summary-icon end-date">
              <FaCalendarCheck />
            </div>
            <div className="summary-content">
              <div className="summary-label">{t('tender.end_date', 'Bitiş')}</div>
              <div className="summary-value">
                {endDateDetails.day} {endDateDetails.month}
              </div>
            </div>
          </div>

          {/* Bütçe */}
          <div className="summary-item">
            <div className="summary-icon budget">
              <FaMoneyBillWave />
            </div>
            <div className="summary-content">
              <div className="summary-label">{t('tender.budget_range', 'Bütçe')}</div>
              <div className="summary-value budget-range">
                {formatCurrency(tender.minBudget)} - {formatCurrency(tender.maxBudget)}
              </div>
            </div>
          </div>

          {/* Kalan Süre */}
          <div className="summary-item">
            <div className="summary-icon time">
              <FaHourglassHalf />
            </div>
            <div className="summary-content">
              <div className="summary-label">{t('tender.remaining_time', 'Kalan Süre')}</div>
              <div className="summary-value">
                <RemainingTimeCounter endDate={tender.endDate} />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Araç Görseli Alanı */}
      <div className="vehicle-preview">
        <div className="vehicle-image-placeholder">
          <div className="placeholder-content">
            <i className="bi bi-truck fs-1 text-muted"></i>
            <p className="placeholder-text">{t('tender.vehicle_image_placeholder', 'Görsel Yok')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TenderHeader;