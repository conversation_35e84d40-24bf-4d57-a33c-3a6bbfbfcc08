import React, { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Container, Card, Button, Form, Alert, Row, Col, Badge } from "react-bootstrap";
import { FaCar, FaGasPump, FaCogs, FaUserFriends, FaBuilding, FaChartLine, FaMapMarkerAlt, FaStar, FaMedal, FaCheckCircle, FaSnowflake, FaCalendarAlt, FaGavel, FaHourglassHalf } from "react-icons/fa";
import tenderService from "../../services/tenderService";
import PageTitle from "../../components/PageTitle";
import "./style.css";
import "../../styles/colors.css";

const USAGE_STATUS_OPTIONS = [
  { id: 10, label: "Sıfır" },
  { id: 20, label: "<PERSON>kinci El" },
  { id: 30, label: "Demo" },
];

const TenderBidPage = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const tenderId = location.state?.tenderId;

  const [tender, setTender] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [bids, setBids] = useState([
    { amount: "", vehicleYear: "", vehicleKm: "", usageStatusId: "", quantity: "" }
  ]);
  const [success, setSuccess] = useState(false);
  const [sending, setSending] = useState(false);
  const [sentCount, setSentCount] = useState(0);
  const [remainingTime, setRemainingTime] = useState(null);
  const [remainingTimeObj, setRemainingTimeObj] = useState(null);
  const timerRef = useRef();

  useEffect(() => {
    if (!tenderId) {
      setError("İhale ID bulunamadı.");
      setLoading(false);
      return;
    }
    tenderService.getAvailableTenderDetail(tenderId)
      .then((response) => {
        if (response.data && response.data.result) {
          setTender(response.data.result);
        } else {
          setError("İhale detayları bulunamadı.");
        }
      })
      .catch(() => setError("İhale detayları yüklenirken hata oluştu."))
      .finally(() => setLoading(false));
  }, [tenderId]);

  useEffect(() => {
    if (!tender) return;
    // Kalan süre hesaplama fonksiyonu
    const calculateRemainingTime = () => {
      const now = new Date();
      const startDate = new Date(tender.startDate);
      const endDate = new Date(tender.endDate);
      if (now < startDate) {
        setRemainingTime(t("tender_details.not_started"));
        setRemainingTimeObj(null);
        return;
      }
      if (now > endDate) {
        setRemainingTime(t("tender_details.expired"));
        setRemainingTimeObj(null);
        return;
      }
      const diff = endDate.getTime() - now.getTime();
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);
      let remainingTimeText = '';
      if (days > 0) {
        remainingTimeText = `${days} ${t("tender_details.days")}`;
        if (hours > 0) remainingTimeText += ` ${hours} ${t("tender_details.hours")}`;
      } else if (hours > 0) {
        remainingTimeText = `${hours} ${t("tender_details.hours")}`;
        if (minutes > 0) remainingTimeText += ` ${minutes} ${t("tender_details.minutes")}`;
      } else {
        remainingTimeText = `${minutes} ${t("tender_details.minutes")} ${seconds} ${t("tender_details.seconds")}`;
      }
      setRemainingTime(remainingTimeText);
      setRemainingTimeObj({ days, hours, minutes, seconds });
    };
    calculateRemainingTime();
    timerRef.current = setInterval(calculateRemainingTime, 1000);
    return () => clearInterval(timerRef.current);
  }, [tender, t]);

  const handleBidChange = (field, value) => {
    setBids([{ ...bids[0], [field]: value }]);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSending(true);
    setSuccess(false);
    setSentCount(0);
    const bid = bids[0];
    const payload = {
      tenderId,
      amount: Number(bid.amount),
      vehicleYear: Number(bid.vehicleYear),
      vehicleKm: Number(bid.vehicleKm),
      usageStatusId: Number(bid.usageStatusId),
      quantity: Number(bid.quantity)
    };
    try {
      await tenderService.sendBid(payload);
      setSentCount(1);
      setSuccess(true);
    } catch (err) {
      // Hata yönetimi yapılabilir
    }
    setSending(false);
  };

  return (
    <>
      <PageTitle
        pageTitle={t("tender_bid_page.title")}
        pagesub={t("tender_bid_page.subtitle")}
      />
      <div className="tender-bid-page-container">
        <Container>
          {loading ? (
            <div className="text-center py-5">
              <span className="spinner-border text-danger" role="status"></span>
            </div>
          ) : error ? (
            <Alert variant="danger">{error}</Alert>
          ) : tender ? (
            <Row className="justify-content-center">
              <Col md={10} lg={8}>
                <Card className="tender-detail-card mb-3">
                  <Card.Header className="tender-detail-header">
                    <div className="tender-title-row">
                      <FaCar className="detail-icon" style={{fontSize: 16}} />
                      <span className="tender-brand">{tender.vehicleBrand}</span>
                      <span className="tender-model">{tender.vehicleModel}</span>
                      <Badge bg="success" className="tender-status-badge">{tender.tenderStatus?.statusName}</Badge>
                    </div>
                  </Card.Header>
                  <Card.Body>
                    {/* İhale zamanları alanı */}
                    <div style={{ display: 'flex', alignItems: 'center', gap: 24, marginBottom: 18, background: '#f8f9fa', padding: '10px 12px', borderRadius: 8, border: '1px solid #e9ecef' }}>
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                        <span style={{ fontSize: 12, color: '#6c757d', marginBottom: 2 }}>{t('tender_details.start_date')}</span>
                        <span style={{ fontSize: 13, fontWeight: 500, color: '#212529', display: 'flex', alignItems: 'center', gap: 6 }}>
                          <FaCalendarAlt style={{ color: 'var(--primary-color)', fontSize: 13 }} />
                          {tender.startDate ? new Date(tender.startDate).toLocaleDateString('tr-TR') : '-'}
                        </span>
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                        <span style={{ fontSize: 12, color: '#6c757d', marginBottom: 2 }}>{t('tender_details.end_date')}</span>
                        <span style={{ fontSize: 13, fontWeight: 500, color: '#212529', display: 'flex', alignItems: 'center', gap: 6 }}>
                          <FaCalendarAlt style={{ color: 'var(--primary-color)', fontSize: 13 }} />
                          {tender.endDate ? new Date(tender.endDate).toLocaleDateString('tr-TR') : '-'}
                        </span>
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', minWidth: 120 }}>
                        <span style={{ fontSize: 12, color: '#6c757d', marginBottom: 2 }}>{t('tender_details.remaining_time')}</span>
                        <span style={{ fontSize: 13, fontWeight: 600, color: 'var(--primary-color)', display: 'flex', alignItems: 'center', gap: 6, background: '#fff', padding: '4px 10px', borderRadius: 6, border: '1px solid #e9ecef' }}>
                          <FaHourglassHalf style={{ fontSize: 13 }} />
                          {remainingTimeObj ? (
                            <>
                              {remainingTimeObj.days > 0 && <span>{remainingTimeObj.days}g </span>}
                              <span>{remainingTimeObj.hours.toString().padStart(2, '0')}s </span>
                              <span>{remainingTimeObj.minutes.toString().padStart(2, '0')}d </span>
                              <span>{remainingTimeObj.seconds.toString().padStart(2, '0')}sn</span>
                            </>
                          ) : (
                            <span>{remainingTime || t('tender_details.expired')}</span>
                          )}
                        </span>
                      </div>
                    </div>
                    <div className="section-title"><FaGavel className="detail-icon" style={{fontSize: 13}} /> {t("tender_bid_page.title")}</div>
                    <div className="tender-info-compact-grid">
                      <div className="detail-item"><FaGasPump className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.fuel_type")}</span><span className="detail-value">{tender.fuelType?.typeName}</span></div></div>
                      <div className="detail-item"><FaCogs className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.gear_type")}</span><span className="detail-value">{tender.gearType?.typeName}</span></div></div>
                      <div className="detail-item"><FaUserFriends className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.usage_status")}</span><span className="detail-value">{tender.usageStatus?.statusName}</span></div></div>
                      <div className="detail-item"><FaBuilding className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.body_type")}</span><span className="detail-value">{tender.vehicleBodyType?.typeName}</span></div></div>
                      <div className="detail-item"><FaChartLine className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.km_limit")}</span><span className="detail-value">{tender.kmLimit?.limit}</span></div></div>
                      <div className="detail-item"><FaMapMarkerAlt className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.city")}</span><span className="detail-value">{tender.city?.name}</span></div></div>
                      <div className="detail-item"><FaCalendarAlt className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.rental_period")}</span><span className="detail-value">{tender.rentalPeriod?.period}</span></div></div>
                      <div className="detail-item"><FaMedal className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.declaration_limit")}</span><span className="detail-value">{tender.declarationLimit?.limitName}</span></div></div>
                      <div className="detail-item"><FaCheckCircle className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.maintenance_responsible")}</span><span className="detail-value">{tender.maintenanceResponsible?.responsible}</span></div></div>
                      <div className="detail-item"><FaSnowflake className="detail-icon" /><div className="detail-content"><span className="detail-label">{t("tender_bid_page.has_winter_tire")}</span><span className="detail-value">{tender.hasWinterTire ? t("common.yes") : t("common.no")}</span></div></div>
                      <div className="detail-item"><FaCar className="detail-icon" /><div className="detail-content"><span className="detail-label">{t('available_tenders_page.vehicle_count')}</span><span className="detail-value">{tender.quantity}</span></div></div>
                    </div>
                  </Card.Body>
                </Card>
                <Card className="tender-bid-form-card">
                  <Card.Body>
                    <div className="section-title"><FaGavel className="detail-icon" style={{fontSize: 13}} /> {t("tender_bid_page.bid_form_title")}</div>
                    {success && <Alert variant="success">{sentCount} {t("tender_bid_page.success_message")}</Alert>}
                    <Form onSubmit={handleSubmit} autoComplete="off">
                      <div className="compact-bid-form">
                        <Form.Group className="compact-input year-input">
                          <Form.Label>{t("tender_bid_page.vehicle_year")}</Form.Label>
                          <Form.Control
                            type="number"
                            min={2000}
                            max={2100}
                            required
                            value={bids[0].vehicleYear}
                            onChange={e => handleBidChange("vehicleYear", e.target.value)}
                          />
                        </Form.Group>
                        <Form.Group className="compact-input km-input">
                          <Form.Label>{t("tender_bid_page.vehicle_km")}</Form.Label>
                          <Form.Control
                            type="number"
                            min={0}
                            required
                            value={bids[0].vehicleKm}
                            onChange={e => handleBidChange("vehicleKm", e.target.value)}
                          />
                        </Form.Group>
                        <Form.Group className="compact-input status-input">
                          <Form.Label>{t("tender_bid_page.usage_status")}</Form.Label>
                          <Form.Select
                            required
                            value={bids[0].usageStatusId}
                            onChange={e => handleBidChange("usageStatusId", e.target.value)}
                          >
                            <option value="">{t("common.select")}</option>
                            {USAGE_STATUS_OPTIONS.map(opt => (
                              <option key={opt.id} value={opt.id}>{opt.label}</option>
                            ))}
                          </Form.Select>
                        </Form.Group>
                        <Form.Group className="compact-input quantity-input">
                          <Form.Label>{t("available_tenders_page.vehicle_count")}</Form.Label>
                          <Form.Control
                            type="number"
                            min={1}
                            required
                            value={bids[0].quantity}
                            onChange={e => handleBidChange("quantity", e.target.value)}
                          />
                        </Form.Group>
                        <Form.Group className="compact-input amount-input">
                          <Form.Label>{t("tender_bid_page.bid_amount")}</Form.Label>
                          <div className="amount-wrapper">
                            <Form.Control
                              type="number"
                              min={0}
                              required
                              value={bids[0].amount}
                              onChange={e => handleBidChange("amount", e.target.value)}
                            />
                            <span className="currency">₺</span>
                          </div>
                        </Form.Group>
                        <Button 
                          type="submit" 
                          variant="success" 
                          className="compact-submit-btn" 
                          disabled={sending}
                        >
                          {sending ? (
                            <span className="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                          ) : (
                            <FaGavel />
                          )}
                          <span>{sending ? t("common.loading") : t("tender_bid_page.submit_bid")}</span>
                        </Button>
                      </div>
                    </Form>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          ) : null}
        </Container>
      </div>
    </>
  );
};

export default TenderBidPage; 