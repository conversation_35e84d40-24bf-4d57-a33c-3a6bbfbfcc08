import React, { useState, useEffect } from 'react';
import { Form, Button, Row, Col, Card, Alert, Spinner, InputGroup } from 'react-bootstrap';
import { FaSave, FaPaperPlane, FaArrowLeft, FaCheckCircle } from 'react-icons/fa';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import axios from 'axios';

// Services
import { submitBid, saveDraftBid } from '../../services/tenderBidService';

/**
 * İhaleye teklif vermek için form bileşeni
 */
const TenderBidForm = ({ tender, existingBid, onBidSubmitted }) => {
  const { t } = useTranslation();
  
  const [formData, setFormData] = useState({
    totalAmount: '',
    price: '',
    description: '',
    terms: '',
    vehicles: [],
    attachments: []
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [vehiclePrices, setVehiclePrices] = useState({});
  const [totalPrice, setTotalPrice] = useState(0);
  
  // Mevcut teklif varsa formu onunla doldur
  useEffect(() => {
    if (existingBid) {
      setFormData({
        totalAmount: existingBid.totalAmount || '',
        price: existingBid.price || '',
        description: existingBid.description || '',
        terms: existingBid.terms || '',
        vehicles: existingBid.vehicles || [],
        attachments: existingBid.attachments || []
      });

      // Araç fiyatlarını doldur
      if (existingBid.vehicles && existingBid.vehicles.length > 0) {
        const prices = {};
        existingBid.vehicles.forEach(vehicle => {
          prices[vehicle.id] = vehicle.price || 0;
        });
        setVehiclePrices(prices);
      }
    } else if (tender && tender.requiredVehicles) {
      // Yeni teklif için araçları hazırla
      const initialVehicles = tender.requiredVehicles.map(vehicle => ({
        ...vehicle,
        price: 0
      }));
      
      setFormData(prev => ({
        ...prev,
        vehicles: initialVehicles
      }));
    }
  }, [existingBid, tender]);

  // Araç fiyatları değiştiğinde toplam fiyatı hesapla
  useEffect(() => {
    let total = 0;
    Object.values(vehiclePrices).forEach(price => {
      total += parseFloat(price || 0);
    });
    
    setTotalPrice(total);
    
    setFormData(prev => ({
      ...prev,
      totalAmount: total.toString()
    }));
  }, [vehiclePrices]);

  // Araç fiyatı değiştiğinde güncelle
  const handleVehiclePriceChange = (id, price) => {
    setVehiclePrices(prev => ({
      ...prev,
      [id]: price
    }));
  };
  
  // Form değişikliklerini yönetir
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Taslak teklifi kaydeder
  const handleSaveDraft = async (e) => {
    e.preventDefault();
    
    try {
      setLoading(true);
      setError(null);
      
      // tender nesnesinin company özelliği olmayabilir, güvenli şekilde kontrol edelim
      const bidData = {
        ...formData,
        tenderId: tender.id
      };
      
      if (tender.company) {
        bidData.companyId = tender.company.id;
        bidData.companyName = tender.company.name;
      }
      
      const response = await saveDraftBid(tender.id, bidData);
      
      if (!response || !response.success) {
        setError(response?.message || t('tender.bid.save_draft_error', 'Taslak kaydedilirken bir hata oluştu'));
        setLoading(false);
        return;
      }
      
      setLoading(false);
      setSuccess(true);
      
      // 3 saniye sonra başarı mesajını temizle
      setTimeout(() => {
        setSuccess(false);
      }, 3000);
      
      // Alert kullanarak kullanıcıya bildir
      alert(t('tender.bid.save_draft_success', 'Teklifiniz taslak olarak kaydedildi'));
      
    } catch (err) {
      console.error('Teklif taslağı kaydedilemedi:', err);
      setError(t('tender.bid.save_draft_error', 'Taslak kaydedilirken bir hata oluştu'));
      setLoading(false);
    }
  };
  
  // Form gönderme işlemi
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Form verilerini hazırla
      const bidData = {
        tenderId: tender.id,
        totalAmount: parseFloat(formData.totalAmount),
        description: formData.description,
        terms: formData.terms,
        vehicles: tender.requiredVehicles.map(vehicle => ({
          vehicleId: vehicle.id,
          price: parseFloat(vehiclePrices[vehicle.id] || 0)
        }))
      };

      // Direkt axios yerine servis fonksiyonunu kullan
      const response = await submitBid(tender.id, bidData);
      
      // Başarılı yanıt kontrolü
      if (response && response.success && response.data) {
        onBidSubmitted(response.data);
      } else {
        setError(response?.message || t("Teklif gönderilirken bir hata oluştu."));
      }

    } catch (err) {
      console.error('Teklif gönderilirken hata oluştu:', err);
      setError(err.message || t("Teklif gönderilirken bir hata oluştu."));
    } finally {
      setLoading(false);
    }
  };
  
  // Teklif başarıyla gönderildiyse teşekkür mesajı göster
  if (success) {
    return (
      <div className="bid-form-section">
        <Card className="text-center p-5">
          <Card.Body>
            <FaCheckCircle size={60} className="text-success mb-4" />
            <h3>{t('tender.bid.success_title', 'Teklif Gönderildi')}</h3>
            <p>{t('tender.bid.success_message', { customer: tender.customer?.name || '' }, 'Teklifiniz başarıyla gönderildi.')}</p>
            <p>{t('tender.bid.success_notification', 'Değerlendirme sonucunda bildirim alacaksınız.')}</p>
            <Button 
              variant="primary" 
              className="mt-3"
              onClick={() => window.location.href = '/company/tenders'}
            >
              {t('tender.return_to_tenders', 'Tekliflere Dön')}
            </Button>
          </Card.Body>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="tender-bid-form">
      {error && (
        <Alert variant="danger" className="mb-4">
          <Alert.Heading>{t("Hata!")}</Alert.Heading>
          <p>{error}</p>
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        <div className="bid-form-section">
          <h4 className="bid-form-section-title">{t("Araç Teklifi")}</h4>
          <p className="text-muted mb-4">
            {t("Lütfen ihale için talep edilen her araç için fiyat teklifinizi belirtin.")}
          </p>

          {tender?.requiredVehicles && tender.requiredVehicles.length > 0 ? (
            <Row className="g-3">
              {tender.requiredVehicles.map((vehicle, idx) => (
                <Col md={6} key={idx} className="mb-3">
                  <Form.Group>
                    <Form.Label className="fw-bold">
                      {vehicle.type} ({vehicle.quantity} {t('tender.vehicle.units', 'adet')})
                    </Form.Label>
                    <InputGroup>
                      <Form.Control
                        type="number"
                        placeholder={t('tender.bid.price_per_vehicle', 'Araç başına fiyat')}
                        value={vehiclePrices[vehicle.id] || ''}
                        onChange={(e) => handleVehiclePriceChange(vehicle.id, e.target.value)}
                      />
                      <InputGroup.Text>₺</InputGroup.Text>
                    </InputGroup>
                    <Form.Text className="text-muted">
                      {t('tender.bid.monthly_price_hint', 'Aylık kira fiyatı giriniz')}
                    </Form.Text>
                  </Form.Group>
                </Col>
              ))}
            </Row>
          ) : (
            <Alert variant="info">
              {t("Bu ihale için herhangi bir araç talebi bulunmamaktadır.")}
            </Alert>
          )}
        </div>

        <Row>
          <Col md={8}>
            <Card className="mb-4">
              <Card.Header>
                <h6 className="mb-0">{t('tender.bid.details', 'Teklif Detayları')}</h6>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>{t('tender.bid.description', 'Açıklama')}</Form.Label>
                      <Form.Control 
                        as="textarea"
                        rows={3}
                        name="description"
                        value={formData.description}
                        onChange={handleChange}
                        placeholder={t('tender.bid.description_placeholder', 'Teklifiniz hakkında detaylı bilgi giriniz...')}
                        className="form-control-solid"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>{t('tender.bid.terms', 'Şartlar ve Koşullar')}</Form.Label>
                      <Form.Control 
                        as="textarea"
                        rows={3}
                        name="terms"
                        value={formData.terms}
                        onChange={handleChange}
                        placeholder={t('tender.bid.terms_placeholder', 'Ödeme şartları, teslimat koşulları vb.')}
                        className="form-control-solid"
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          
          <Col md={4}>
            <Card className="mb-4">
              <Card.Header>
                <h6 className="mb-0">{t('tender.customer_info', 'Müşteri Bilgileri')}</h6>
              </Card.Header>
              <Card.Body>
                <div className="customer-info">
                  {/* Burada tender.company.name değil, güvenli erişim kullanıyoruz */}
                  <p><strong>{t('common.company', 'Şirket')}:</strong> {tender?.company?.name || 'Bilgi yok'}</p>
                  <p><strong>{t('common.contact', 'İletişim')}:</strong> {tender?.customer?.contactName || 'Bilgi yok'}</p>
                  <p><strong>{t('common.phone', 'Telefon')}:</strong> {tender?.customer?.phone || 'Bilgi yok'}</p>
                  <p><strong>{t('common.email', 'E-posta')}:</strong> {tender?.customer?.email || 'Bilgi yok'}</p>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
        
        <div className="d-flex justify-content-between mt-4">
          <Button variant="outline-secondary" onClick={() => window.history.back()}>
            <FaArrowLeft className="me-2" />
            {t('common.back', 'Geri')}
          </Button>
          
          <div>
            <Button 
              variant="outline-primary" 
              className="me-2"
              onClick={handleSaveDraft}
              disabled={loading}
            >
              {loading ? (
                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-1" />
              ) : (
                <FaSave className="me-2" />
              )}
              {t('tender.bid.save_draft', 'Taslak Kaydet')}
            </Button>
            
            <Button 
              type="submit" 
              variant="primary"
              disabled={loading}
            >
              {loading ? (
                <Spinner as="span" animation="border" size="sm" role="status" aria-hidden="true" className="me-1" />
              ) : (
                <FaPaperPlane className="me-2" />
              )}
              {t('tender.bid.submit', 'Teklifi Gönder')}
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

TenderBidForm.propTypes = {
  tender: PropTypes.object.isRequired,
  existingBid: PropTypes.object,
  onBidSubmitted: PropTypes.func.isRequired
};

export default TenderBidForm; 