import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'react-bootstrap';
import { FaAngleRight } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import './style.css';

// API servisi import edeceğiz
import { useMockAPI } from '../../services/api';
import tendersService from '../../services/tendersService';

const SuggestedTenders = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  const [suggestedTenders, setSuggestedTenders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // API'dan veri çekme işlemi
  useEffect(() => {
    const fetchSuggestedTenders = async () => {
      setIsLoading(true);
      try {
        if (useMockAPI) {
          // Mock veri kullan
          setTimeout(() => {
            setSuggestedTenders([
              {
                id: 101,
                title: t('dashboard.suggested_tenders.mock_data.tender1_title'),
                priceRange: '₺15.000-25.000',
                vehicleCount: 5,
                validDays: 2
              },
              {
                id: 102,
                title: t('dashboard.suggested_tenders.mock_data.tender2_title'),
                priceRange: '₺30.000-40.000',
                vehicleCount: 5,
                validDays: 2
              }
            ]);
            setIsLoading(false);
          }, 600);
        } else {
          // Gerçek API çağrısı
          const response = await tendersService.getTenders({ status: 'ACTIVE', suggested: true, limit: 3 });
          setSuggestedTenders(response.data.tenders);
          setIsLoading(false);
        }
      } catch (err) {
        console.error(t('dashboard.suggested_tenders.error_loading'), err);
        setError(t('errors.failed_to_fetch'));
        setIsLoading(false);
      }
    };

    fetchSuggestedTenders();
  }, [t]);

  // İhale detay sayfasına yönlendirme
  const handleViewTender = (tenderId) => {
    navigate(`/tender/${tenderId}`);
  };

  // Teklif verme sayfasına yönlendirme
  const handleBidTender = (tenderId) => {
    navigate(`/tender/${tenderId}/bid`);
  };
  
  // Tüm ihaleleri görüntüleme
  const handleViewAllTenders = () => {
    navigate('/tenders/explore');
  };
  
  return (
    <div className="suggested-tenders-section">
      <div className="section-header">
        <h3>{t('dashboard.suggested_tenders.title')}</h3>
      </div>
      
      <div className="suggested-tenders-list">
        {isLoading ? (
          <div className="loading-indicator">{t('common.loading')}</div>
        ) : error ? (
          <div className="error-message">{error}</div>
        ) : suggestedTenders.length === 0 ? (
          <div className="no-items-message">{t('dashboard.suggested_tenders.no_items')}</div>
        ) : (
          <>
            {suggestedTenders.map(tender => (
              <Card key={tender.id} className="suggested-tender-card">
                <Card.Body>
                  <div className="badge-container">
                    <span className="new-badge">{t('dashboard.suggested_tenders.match_percentage', { percentage: 25 })}</span>
                  </div>
                  <Card.Title>{tender.title}</Card.Title>
                  <div className="tender-info">
                    <div>
                      <span className="info-label">{t('dashboard.tenders.fields.price_range')}:</span>
                      <span className="info-value">{tender.priceRange}</span>
                    </div>
                    <div>
                      <span className="info-label">{t('dashboard.tenders.fields.vehicles')}:</span>
                      <span className="info-value">{tender.vehicleCount}</span>
                    </div>
                    <div>
                      <span className="info-label">{t('dashboard.tenders.fields.valid_days')}:</span>
                      <span className="info-value">{tender.validDays} {t('dashboard.suggested_tenders.in_days')}</span>
                    </div>
                  </div>
                  <div className="card-actions">
                    <Button 
                      variant="primary" 
                      size="sm" 
                      className="bid-button"
                      onClick={() => handleBidTender(tender.id)}
                    >
                      {t('dashboard.suggested_tenders.bid_button')}
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            ))}
            
            <div className="view-all-container">
              <Button 
                variant="link" 
                className="view-all-button"
                onClick={handleViewAllTenders}
              >
                {t('dashboard.suggested_tenders.view_all')} <FaAngleRight />
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SuggestedTenders; 