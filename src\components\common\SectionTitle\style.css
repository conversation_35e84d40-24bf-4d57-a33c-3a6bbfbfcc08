@import url('../../../styles/colors.css');

.section-title {
  margin-bottom: 40px;
}

.section-title .subtitle {
  color: var(--primary-color);
  font-size: 18px;
  font-weight: 500;
  position: relative;
  margin-bottom: 15px;
  text-transform: capitalize;
}

.section-title .title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 15px;
  text-transform: capitalize;
  color: var(--secondary-color);
  position: relative;
}

.section-title .title:after {
  content: "";
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--primary-color);
}

.section-title .description {
  font-size: 16px;
  color: var(--text-light-gray);
  max-width: 800px;
  margin: 0 auto;
  margin-top: 25px;
}

/* Alignment Variations */
.section-title.align-left {
  text-align: left;
}

.section-title.align-left .title:after {
  left: 0;
  transform: none;
}

.section-title.align-left .description {
  margin: 0;
  margin-top: 25px;
}

.section-title.align-center {
  text-align: center;
}

.section-title.align-right {
  text-align: right;
}

.section-title.align-right .title:after {
  left: auto;
  right: 0;
  transform: none;
}

.section-title.align-right .description {
  margin-left: auto;
  margin-right: 0;
}

/* Responsive adjustments */
@media (max-width: 991px) {
  .section-title .title {
    font-size: 32px;
  }
}

@media (max-width: 768px) {
  .section-title .title {
    font-size: 28px;
  }
  
  .section-title .subtitle {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .section-title .title {
    font-size: 24px;
  }
  
  .section-title .subtitle {
    font-size: 14px;
  }
  
  .section-title .title:after {
    width: 60px;
  }
} 