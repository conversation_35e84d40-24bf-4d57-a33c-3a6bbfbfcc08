@import url('../../styles/colors.css');

.unauthorized-section {
  padding: 80px 0;
  background-color: #f8f9fa;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.unauthorized-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.icon-container {
  width: 120px;
  height: 120px;
  background-color: #f8d7d5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.icon-container svg {
  color: var(--primary-color);
}

.unauthorized-card h1 {
  color: #333;
  font-weight: 700;
}

.unauthorized-card .lead {
  color: #555;
  font-size: 1.1rem;
}

.unauthorized-card .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 10px 20px;
}

.unauthorized-card .btn-primary:hover {
  background-color: #d92b1b;
  border-color: #d92b1b;
}

.unauthorized-card .btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 10px 20px;
}

.unauthorized-card .btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

@media (max-width: 767px) {
  .unauthorized-section {
    padding: 60px 0;
  }
  
  .unauthorized-card {
    padding: 15px;
  }
  
  .icon-container {
    width: 90px;
    height: 90px;
  }
  
  .icon-container svg {
    width: 40px;
    height: 40px;
  }
}

.unauthorized-container {
  background-color: #f8f9fa;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.unauthorized-content {
  text-align: center;
  max-width: 650px;
  margin: 0 auto;
  padding: 40px;
  background-color: #f8d7d5;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.unauthorized-icon {
  font-size: 80px;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.unauthorized-title {
  font-size: 32px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 700;
}

.unauthorized-message {
  font-size: 18px;
  margin-bottom: 25px;
  color: #555;
}

.unauthorized-btn {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
  padding: 12px 25px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s;
}

.unauthorized-btn:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.unauthorized-btn-outline {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
  padding: 12px 25px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s;
  margin-left: 10px;
}

.unauthorized-btn-outline:hover {
  background-color: var(--primary-color);
  color: #fff;
} 