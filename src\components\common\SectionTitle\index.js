import React from 'react';
import PropTypes from 'prop-types';
import './style.css';

/**
 * SectionTitle - <PERSON><PERSON><PERSON><PERSON> tüm bölüm başlıkları için kullanılacak ortak bileşen
 * 
 * @param {Object} props
 * @param {string} props.subtitle - Alt başlık metni
 * @param {string} props.title - <PERSON> başlık metni
 * @param {string} props.description - A<PERSON><PERSON><PERSON><PERSON> metni (isteğe bağlı)
 * @param {string} props.alignment - Yazı hizalaması: "left", "center", "right"
 * @param {string} props.className - Ek CSS sınıfları
 * @returns {React.ReactElement}
 */
const SectionTitle = ({
  subtitle,
  title,
  description,
  alignment = 'center',
  className = '',
}) => {
  // CSS sınıflarını oluştur
  const containerClasses = [
    'section-title',
    `align-${alignment}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={containerClasses}>
      {subtitle && <h4 className="subtitle">{subtitle}</h4>}
      {title && <h2 className="title">{title}</h2>}
      {description && <p className="description">{description}</p>}
    </div>
  );
};

SectionTitle.propTypes = {
  subtitle: PropTypes.string,
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
  alignment: PropTypes.oneOf(['left', 'center', 'right']),
  className: PropTypes.string,
};

export default SectionTitle; 