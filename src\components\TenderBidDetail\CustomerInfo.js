import React from 'react';
import { Card } from 'react-bootstrap';
import { FaUser, FaEnvelope, FaPhone, FaBuilding } from 'react-icons/fa';
import PropTypes from 'prop-types';
import './style.css';

/**
 * Müşteri bilgilerini gösteren bileşen
 */
const CustomerInfo = ({ customer }) => {
  if (!customer) {
    return (
      <Card className="mb-4 customer-info-card">
        <Card.Body className="text-center">
          <p className="text-muted">Müşteri bilgisi bulunamadı</p>
        </Card.Body>
      </Card>
    );
  }

  return (
    <Card className="mb-4 customer-info-card">
      <Card.Header className="tender-card-header">
        <h5 className="mb-0">
          <FaBuilding className="me-2" /> Müşteri Bilgileri
        </h5>
      </Card.Header>
      <Card.Body>
        <div className="customer-info">
          <p>
            <FaBuilding className="me-2 text-primary" />
            <strong>Şirket:</strong> {customer.name || 'Belirtilmemiş'}
          </p>
          <p>
            <FaUser className="me-2 text-primary" />
            <strong>İletişim Kişisi:</strong> {customer.contactName || 'Belirtilmemiş'}
          </p>
          <p>
            <FaPhone className="me-2 text-primary" />
            <strong>Telefon:</strong> {customer.phone || 'Belirtilmemiş'}
          </p>
          <p>
            <FaEnvelope className="me-2 text-primary" />
            <strong>Email:</strong> {customer.email || 'Belirtilmemiş'}
          </p>
        </div>
      </Card.Body>
    </Card>
  );
};

CustomerInfo.propTypes = {
  customer: PropTypes.object
};

export default CustomerInfo; 