import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { contractService } from '../../services';

// Async thunk action creators
export const fetchContracts = createAsyncThunk(
  'contracts/fetchContracts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await contractService.getContracts(params);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Sözleşmeler alınamadı');
    }
  }
);

export const fetchContractById = createAsyncThunk(
  'contracts/fetchContractById',
  async (contractId, { rejectWithValue }) => {
    try {
      const response = await contractService.getContractById(contractId);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Sözleşme detayları alınamadı');
    }
  }
);

export const createContract = createAsyncThunk(
  'contracts/createContract',
  async (contractData, { rejectWithValue }) => {
    try {
      const response = await contractService.createContract(contractData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Sözleşme oluşturulamadı');
    }
  }
);

export const updateContract = createAsyncThunk(
  'contracts/updateContract',
  async ({ contractId, contractData }, { rejectWithValue }) => {
    try {
      const response = await contractService.updateContract(contractId, contractData);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.message || 'Sözleşme güncellenemedi');
    }
  }
);

export const uploadContractFile = createAsyncThunk(
  'contracts/uploadContractFile',
  async ({ contractId, file }, { rejectWithValue }) => {
    try {
      const response = await contractService.uploadContractFile(contractId, file);
      return { ...response.data, contractId };
    } catch (error) {
      return rejectWithValue(error.message || 'Sözleşme dosyası yüklenemedi');
    }
  }
);

// Initial State
const initialState = {
  contracts: [],
  currentContract: null,
  loading: false,
  error: null,
  filters: {
    status: '',
  },
};

// Contracts Slice
const contractsSlice = createSlice({
  name: 'contracts',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload,
      };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // fetchContracts
    builder
      .addCase(fetchContracts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContracts.fulfilled, (state, action) => {
        state.loading = false;
        state.contracts = action.payload;
      })
      .addCase(fetchContracts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // fetchContractById
    builder
      .addCase(fetchContractById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchContractById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentContract = action.payload;
      })
      .addCase(fetchContractById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // createContract
    builder
      .addCase(createContract.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createContract.fulfilled, (state, action) => {
        state.loading = false;
        state.contracts.push(action.payload);
      })
      .addCase(createContract.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // updateContract
    builder
      .addCase(updateContract.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateContract.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.contracts.findIndex(
          (contract) => contract.id === action.payload.id
        );
        if (index !== -1) {
          state.contracts[index] = action.payload;
        }
        if (state.currentContract && state.currentContract.id === action.payload.id) {
          state.currentContract = action.payload;
        }
      })
      .addCase(updateContract.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });

    // uploadContractFile
    builder
      .addCase(uploadContractFile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(uploadContractFile.fulfilled, (state, action) => {
        state.loading = false;
        // Sözleşme durumunu güncelle
        const { contractId } = action.payload;
        const contractIndex = state.contracts.findIndex(
          (contract) => contract.id === contractId
        );
        if (contractIndex !== -1) {
          state.contracts[contractIndex].status = 'İmzalandı';
        }
        if (state.currentContract && state.currentContract.id === contractId) {
          state.currentContract.status = 'İmzalandı';
        }
      })
      .addCase(uploadContractFile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { setFilters, clearFilters, clearError } = contractsSlice.actions;

export default contractsSlice.reducer; 