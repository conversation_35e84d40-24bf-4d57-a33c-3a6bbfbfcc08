@import url('../../styles/colors.css');

/* ===================== HERO ======================*/
.gauto-hero-area {
  position: relative;
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
  padding: 30px 0 80px;
  overflow: hidden;
  z-index: 1;
}

/* Hero içerik stilleri */
.hero-content {
  padding: 5px 0;
  position: relative;
  z-index: 5;
  text-align: left;
  max-width: 600px;
}

.hero-title {
  color: #fff;
  font-size: 34px;
  font-weight: 600;
  margin-bottom: 10px;
  font-family: "Poppins", sans-serif;
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
  margin-bottom: 0;
  line-height: 1.4;
}

/* <PERSON> butonları */
.hero-buttons {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

/* Hero'da butonlar için override */
.hero-buttons .gauto-btn {
  margin: 0;
}

.hero-buttons .gauto-btn-white {
  margin: 0;
  background-color: #fff;
  color: #333;
  padding: 10px 20px;
  border-radius: 3px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.hero-buttons .gauto-btn-white:hover {
  background-color: #f5f5f5;
  color: var(--primary-color);
}

/* FindCar bileşeni wrapper */
.hero-findcar-wrapper {
  position: relative;
  width: 100%;
  margin-top: 15px;
  z-index: 10;
}

/* Hero içindeki FindCar kartı override */
.hero-findcar-wrapper .findcar-area {
  margin-top: 0;
  padding: 0;
}

.hero-findcar-wrapper .search-box-wrapper {
  margin-top: 0;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  overflow: hidden;
}

/* Minimal arka plan efekti */
.gauto-hero-area::before {
  content: "";
  position: absolute;
  top: -5%;
  right: -10%;
  width: 45%;
  height: 200%;
  background: rgba(255, 255, 255, 0.05);
  transform: rotate(30deg);
  z-index: 2;
}

.gauto-hero-area::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%);
  z-index: 2;
}

/* Responsive */
@media (max-width: 991px) {
  .hero-title {
    font-size: 30px;
  }
  .hero-subtitle {
    font-size: 14px;
  }
  .gauto-hero-area {
    padding: 30px 0 70px;
  }
  .hero-content {
    max-width: 100%;
    text-align: center;
    padding-bottom: 5px;
  }
  .hero-findcar-wrapper {
    margin-top: 20px;
  }
}

@media (max-width: 767px) {
  .hero-title {
    font-size: 26px;
  }
  .hero-subtitle {
    font-size: 13px;
    margin-bottom: 5px;
  }
  .gauto-hero-area {
    padding: 25px 0 60px;
  }
  .hero-content {
    padding-bottom: 0;
  }
  .hero-findcar-wrapper {
    margin-top: 15px;
  }
}

@media (max-width: 576px) {
  .gauto-hero-area {
    padding: 20px 0 50px;
  }
  .hero-findcar-wrapper {
    margin-top: 10px;
  }
}
