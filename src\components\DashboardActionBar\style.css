@import url('../../styles/colors.css');

.dashboard-action-bar {
  background-color: var(--light-gray);
  border-radius: 10px;
  padding: 15px 0;
  margin-bottom: 25px;
  border: 1px solid var(--medium-gray);
}

.action-buttons-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 10px 15px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
}

.action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.action-icon {
  margin-right: 8px;
  font-size: 18px;
}

.create-button .action-icon {
  color: var(--primary-color);
}

.explore-button .action-icon {
  color: var(--info);
}

.reports-button .action-icon {
  color: var(--success);
}

.help-button .action-icon {
  color: var(--secondary-color);
}

@media (max-width: 768px) {
  .action-button {
    flex-direction: column;
    padding: 8px;
  }
  
  .action-icon {
    margin-right: 0;
    margin-bottom: 5px;
    font-size: 22px;
  }
} 