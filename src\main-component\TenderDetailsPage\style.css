@import url('../../styles/colors.css');

.tender-details-area {
  padding: 50px 0;
  background-color: var(--gray-50);
  min-height: 600px;
}

/* <PERSON><PERSON> dön butonu */
.back-button-container {
  margin-bottom: 20px;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.back-button:hover {
  transform: translateX(-5px);
}

/* <PERSON><PERSON><PERSON><PERSON> g<PERSON> */
.tender-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 20px var(--primary-shadow);
}

/* Kartlar */
.tender-detail-card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 5px 20px var(--primary-shadow);
  margin-bottom: 25px;
  overflow: hidden;
}

.tender-detail-card .card-header {
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  padding: 20px;
  font-weight: 600;
  color: var(--gray-900);
  font-family: "Poppins", sans-serif;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tender-detail-card .card-body {
  padding: 25px;
}

.tender-detail-card .card-footer {
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: 20px;
}

/* Ana kart bilgileri */
.tender-status-badge {
  font-size: 12px;
  padding: 6px 12px;
  border-radius: 30px;
  font-weight: 500;
  text-transform: uppercase;
}

.car-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 20px;
  font-size: 22px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.car-title .info-icon {
  color: var(--primary-color);
}

.car-specs {
  margin-bottom: 30px;
}

.section-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 15px;
  font-size: 18px;
  border-left: 4px solid var(--primary-color);
  padding-left: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed var(--gray-200);
}

.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.detail-icon {
  color: var(--primary-color);
  font-size: 20px;
  min-width: 20px;
  margin-top: 3px;
  opacity: 0.8;
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: 5px;
}

.detail-value {
  color: var(--gray-900);
  font-weight: 500;
  font-size: 16px;
}

/* Kalan süre vurgulaması */
.remaining-time-item {
  background-color: var(--primary-transparent);
  border-radius: 8px;
  padding: 15px;
  border: 1px dashed var(--primary-color);
}

.remaining-time {
  color: var(--primary-color);
  font-weight: 600;
}

.tender-period-info,
.tender-date-info {
  margin-bottom: 30px;
}

.tender-description {
  margin-top: 20px;
}

.tender-description p {
  color: var(--gray-600);
  line-height: 1.8;
  font-size: 15px;
}

/* Şirket listesi */
.card-header-icon {
  color: var(--primary-color);
  font-size: 20px;
  opacity: 0.8;
  margin-right: 10px;
}

.company-responses-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.company-response-item {
  border: 1px solid var(--gray-200);
  border-radius: 10px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  background-color: var(--white);
}

.company-response-item:hover {
  box-shadow: 0 8px 25px var(--primary-shadow);
  transform: translateY(-5px);
  border-color: var(--gray-300);
}

.company-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.company-logo-placeholder {
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 20px;
  font-weight: 600;
}

.company-details {
  display: flex;
  flex-direction: column;
}

.company-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 5px;
  font-size: 16px;
}

.company-location {
  color: var(--gray-600);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.company-offer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.offer-amount {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
  padding: 8px 15px;
  border-radius: 30px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.offer-icon {
  color: var(--primary-color);
  opacity: 0.8;
}

.offer-actions {
  display: flex;
  gap: 10px;
}

.offer-action-btn {
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

/* Kabul edilmiş teklif stili */
.accepted-offer-alert {
  padding: 0;
  margin-bottom: 0;
  border: none;
}

.accepted-offer-header {
  background-color: var(--success);
  padding: 15px 20px;
  border-radius: 10px 10px 0 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.accepted-offer-header h5 {
  color: var(--white);
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.accepted-icon {
  color: var(--white);
  font-size: 18px;
}

.accepted-logo {
  background-color: var(--success);
}

.company-response-item.accepted {
  border-radius: 0 0 10px 10px;
  border-top: none;
  background-color: var(--soft-success);
  border-color: var(--success);
  opacity: 0.2;
}

.company-response-item.accepted:hover {
  transform: none;
  box-shadow: none;
}

.accepted-amount {
  background-color: var(--soft-success);
  color: var(--success);
}

.accepted-amount .offer-icon {
  color: var(--success);
}

.accepted-date {
  color: var(--success);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 5px;
}

.accepted-date-icon {
  font-size: 12px;
}

/* Yıldız derecelendirme stilleri */
.company-rating-container {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-top: 8px;
}

.company-rating {
  display: flex;
  align-items: center;
  gap: 2px;
}

.star-icon {
  font-size: 14px;
  color: var(--gray-300);
}

.star-icon.filled {
  color: var(--warning);
}

.star-icon.half {
  background: linear-gradient(90deg, var(--warning) 50%, var(--gray-300) 50%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.star-icon.empty {
  color: var(--gray-300);
}

.rating-value {
  font-size: 14px;
  color: var(--gray-700);
  margin-left: 4px;
  font-weight: 500;
}

.review-count {
  font-size: 13px;
  color: var(--gray-600);
}

.offer-details {
  font-size: 13px;
  color: var(--gray-600);
  margin: 5px 0;
  font-style: italic;
  max-width: 300px;
  text-align: right;
}

/* Şirket detay stilleri */
.company-additional-info {
  padding: 20px;
  border-top: 1px solid var(--success);
  opacity: 0.2;
}

.company-additional-info h6 {
  font-size: 16px;
  font-weight: 600;
  color: var(--success);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px dashed var(--success);
  opacity: 0.2;
}

.company-stats-info {
  margin-bottom: 20px;
  background-color: var(--gray-50);
  border-radius: 8px;
  padding: 15px 10px;
  box-shadow: 0 2px 5px var(--primary-shadow);
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 10px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.stats-item:hover {
  background-color: var(--white);
  box-shadow: 0 3px 8px var(--primary-shadow);
}

.stat-label {
  color: var(--gray-600);
  font-size: 12px;
  margin-bottom: 6px;
  font-weight: 500;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--success);
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-icon {
  color: var(--primary-color);
  opacity: 0.8;
}

.stat-icon.win {
  color: var(--primary-color);
  opacity: 0.8;
}

.company-contact-info {
  margin-bottom: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.contact-icon {
  color: var(--primary-color);
  font-size: 14px;
  opacity: 0.8;
}

.contact-item a {
  color: var(--gray-700);
  font-size: 14px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-item a:hover {
  color: var(--success);
  text-decoration: underline;
}

.company-description {
  padding: 15px;
  background-color: var(--gray-50);
  border-radius: 8px;
  border-left: 3px solid var(--success);
}

.company-description p {
  color: var(--gray-600);
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
}

/* Logo stilleri */
.company-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
}

/* Teklif karşılaştırma stili */
.offer-comparison {
  background-color: var(--gray-50);
  border-radius: 10px;
  padding: 20px;
  margin-top: 10px;
}

.comparison-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.comparison-icon {
  color: var(--primary-color);
  font-size: 18px;
  opacity: 0.8;
}

.comparison-header h5 {
  margin: 0;
  color: var(--gray-900);
  font-size: 16px;
  font-weight: 600;
}

.offer-comparison p {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: 15px;
}

.price-range {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  background-color: var(--gray-50);
}

.price-range-item {
  flex: 1;
  background-color: var(--white);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.range-label {
  color: var(--gray-600);
}

.range-value {
  color: var(--gray-900);
}

/* Özet kartı */
.sticky-sidebar {
  position: sticky;
  top: 20px;
}

.summary-card .card-footer {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tender-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  width: 100%;
  margin-bottom: 10px;
}

.tender-summary-list .list-group-item {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  border-bottom: 1px solid var(--gray-200);
}

.summary-label {
  color: var(--gray-600);
  font-size: 14px;
}

.summary-value {
  font-weight: 600;
  color: var(--gray-900);
}

.status-active {
  color: var(--success);
}

.status-completed {
  color: var(--info);
}

.status-cancelled {
  color: var(--danger);
}

.accepted-price {
  color: var(--success);
  font-weight: 700;
}

/* Süreç kartı */
.process-timeline {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.process-step {
  display: flex;
  gap: 15px;
  position: relative;
}

.process-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 15px;
  width: 2px;
  height: calc(100% + 15px);
  background-color: var(--gray-200);
  z-index: 0;
}

.step-indicator {
  width: 30px;
  height: 30px;
  background-color: var(--gray-200);
  color: var(--gray-600);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.process-step.completed .step-indicator {
  background-color: var(--success);
  color: var(--white);
}

.process-step.active .step-indicator {
  background-color: var(--primary-color);
  color: var(--white);
}

.step-content {
  flex: 1;
}

.step-content h5 {
  font-size: 16px;
  color: var(--gray-900);
  margin-bottom: 5px;
  font-weight: 600;
}

.process-step.active h5 {
  color: var(--primary-color);
}

.step-content p {
  font-size: 14px;
  color: var(--gray-600);
  margin: 0;
}

/* Mobil Responsive */
@media (max-width: 991px) {
  .tender-details-area {
    padding: 30px 0;
  }

  .sticky-sidebar {
    position: static;
    margin-top: 20px;
  }

  .car-title {
    font-size: 20px;
  }
  
  .price-range {
    flex-direction: column;
  }
  
  .price-range-item {
    text-align: left;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

@media (max-width: 767px) {
  .company-response-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .company-offer {
    align-items: flex-start;
    width: 100%;
  }

  .offer-actions {
    width: 100%;
    justify-content: space-between;
  }

  .tender-detail-card .card-header {
    font-size: 18px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .tender-status-badge {
    align-self: flex-start;
  }

  .company-contact-info {
    margin-bottom: 20px;
  }
  
  .contact-item {
    margin-bottom: 15px;
  }
  
  .company-additional-info {
    padding: 15px;
  }
}

@media (max-width: 576px) {
  .detail-item {
    flex-direction: column;
    gap: 5px;
  }

  .tender-action-btn {
    font-size: 14px;
  }

  .tender-summary-list .list-group-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

.tender-detail-specs strong {
  color: var(--primary-color);
  font-weight: 600;
}

.tender-detail-specs .detail-item {
  margin-bottom: 15px;
  padding-left: 15px;
  border-left: 4px solid var(--primary-color);
}

.tender-action-buttons .btn-tender-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: var(--white);
  transition: all 0.3s;
}

.tender-action-buttons .btn-tender-primary:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.bid-price-highlight {
  color: var(--primary-color);
  font-weight: 700;
}

.bid-submission-section .btn-bid-submit {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
  font-weight: 500;
  transition: all 0.3s;
}

.bid-submission-section .btn-bid-submit:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

/* Kullanıcı ve Firma Bilgileri Bölümü */
.user-company-info-container {
  display: flex;
  gap: 24px;
  margin: 16px 0 8px;
  background: var(--gray-50);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--gray-200);
}

.user-info-section,
.company-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.user-info-section {
  border-right: 1px solid var(--gray-200);
  padding-right: 24px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  gap: 12px;
  flex-wrap: wrap;
}

.section-header .title-group {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.section-header .icon {
  color: var(--primary-color);
  font-size: 14px;
  opacity: 0.8;
  flex-shrink: 0;
}

.section-header strong {
  color: var(--gray-600);
  font-size: 13px;
  font-weight: 500;
  white-space: nowrap;
}

.section-header .info-title {
  color: var(--gray-900);
  font-size: 14px;
  font-weight: 500;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 4px;
}

.info-link,
.info-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--gray-700);
  text-decoration: none;
  padding: 2px 0;
}

.info-link:hover {
  color: var(--primary-color);
}

.info-link .icon,
.info-text .icon {
  color: var(--primary-color);
  font-size: 13px;
  opacity: 0.8;
}

.info-text {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: var(--gray-700);
  text-decoration: none;
  font-size: 13px;
  width: 100%;
  overflow: hidden;
}

.info-text .link-icon {
  color: var(--primary-color);
  font-size: 13px;
  opacity: 0.8;
  flex-shrink: 0;
  margin-top: 3px;
}

.info-text span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.info-text.address {
  align-items: flex-start;
}

.info-text.address .link-icon {
  margin-top: 3px;
}

.info-text.address span {
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.4;
}

/* Tooltip Stilleri */
.tooltip-inner {
  text-align: left !important;
  max-width: 300px !important;
  padding: 10px 12px !important;
  font-size: 13px !important;
  background-color: var(--gray-900) !important;
  color: var(--white) !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px var(--primary-shadow);
  white-space: normal !important;
  word-wrap: break-word !important;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
  color: var(--white);
  font-size: 13px;
  line-height: 1.4;
}

.tooltip.show {
  opacity: 1;
}

.bs-tooltip-auto[x-placement^=top] .arrow::before, 
.bs-tooltip-top .arrow::before {
  border-top-color: var(--gray-900);
}

.bs-tooltip-auto[x-placement^=right] .arrow::before, 
.bs-tooltip-right .arrow::before {
  border-right-color: var(--gray-900);
}

.bs-tooltip-auto[x-placement^=bottom] .arrow::before, 
.bs-tooltip-bottom .arrow::before {
  border-bottom-color: var(--gray-900);
}

.bs-tooltip-auto[x-placement^=left] .arrow::before, 
.bs-tooltip-left .arrow::before {
  border-left-color: var(--gray-900);
}

/* Kullanıcı ve Firma Bilgileri - Yeni Minimalist Tasarım */
.info-sections-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1px;
  background: var(--gray-50);
  border-radius: 8px;
  overflow: hidden;
  margin-top: 24px;
  border: 1px solid var(--gray-200);
}

.info-section {
  background: var(--white);
  padding: 16px;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--gray-200);
  flex-shrink: 0;
}

/* İkon stilleri standardizasyonu */
.info-header .info-icon,
.info-link .link-icon,
.info-text .link-icon {
  color: var(--primary-color);
  opacity: 0.8;
  flex-shrink: 0;
}

.info-header .info-icon {
  font-size: 16px;
}

.info-link .link-icon,
.info-text .link-icon {
  font-size: 14px;
  margin-top: 3px;
}

.info-header .info-label {
  color: var(--gray-700);
  font-size: 14px;
  font-weight: 500;
}

.info-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow: hidden;
  flex: 1;
}

.info-row {
  display: flex;
  align-items: flex-start;
  min-height: 24px;
  width: 100%;
}

.info-name {
  font-weight: 500;
  color: var(--gray-900);
  font-size: 14px;
}

.info-link,
.info-text {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--gray-700);
  text-decoration: none;
  padding: 2px 0;
  width: 100%;
}

.info-link:hover {
  color: var(--primary-color);
}

.info-link:hover .link-icon,
.info-text:hover .link-icon {
  opacity: 1;
}

.info-text.address {
  align-items: flex-start;
}

.info-text.address .link-icon {
  margin-top: 3px;
}

.info-text.address span {
  white-space: normal;
  word-wrap: break-word;
  line-height: 1.4;
}

/* Tooltip Stilleri */
.tooltip-inner {
  text-align: left !important;
  max-width: 300px !important;
  padding: 10px 12px !important;
  font-size: 13px !important;
  background-color: var(--gray-900) !important;
  color: var(--white) !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 8px var(--primary-shadow);
  white-space: normal !important;
  word-wrap: break-word !important;
}

/* Responsive tasarım */
@media (max-width: 768px) {
  .info-sections-container {
    grid-template-columns: 1fr;
  }

  .info-section {
    padding: 12px;
  }

  .info-header {
    margin-bottom: 8px;
    padding-bottom: 6px;
  }

  .info-name {
    font-size: 13px;
  }

  .info-link,
  .info-text {
    font-size: 12px;
  }

  .user-info-section {
    border-right: none;
    border-bottom: 1px solid var(--gray-200);
    padding-right: 0;
    padding-bottom: 16px;
  }
}

/* İkonların hover durumunda opaklığı artırma */
.info-link:hover .link-icon,
.info-text:hover .link-icon,
.contact-item:hover .contact-icon {
  opacity: 1;
} 