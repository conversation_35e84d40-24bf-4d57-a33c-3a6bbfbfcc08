import api, { useMockAPI } from './api';

// ----- Gerçek API Çağrıları ----- //

/**
 * Kullanıcı profil bilgilerini getir
 * @returns {Promise} API yanıtı
 */
const getUserProfile = () => {
  return api.get('/User/GetUserProfile');
};

/**
 * Kullanıcı profil bilgilerini güncelle
 * @param {Object} profileData - Güncellenecek profil verileri
 * @returns {Promise} API yanıtı
 */
const updateUserProfile = (profileData) => {
  return api.put('/User/UpdateUserProfile', profileData);
};

/**
 * Kullanıcı şifresini güncelle
 * @param {Object} passwordData - Şifre güncelleme verileri
 * @returns {Promise} API yanıtı
 */
const updateUserPassword = (passwordData) => {
  return api.put('/User/UpdateUserPassword', passwordData);
};

// ----- Mock API Çağrıları ----- //

/**
 * Mock kullanıcı profil bilgilerini getir
 * @returns {Promise} Mock API yanıtı
 */
const mockGetUserProfile = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Mock profil verisi
      const mockProfileData = {
        result: {
          userInformation: {
            username: "anıl",
            firstName: "Anıl",
            lastName: "Orhan",
            email: "<EMAIL>",
            phone: "**********",
            userType: "Customer",
            isActiveAccount: true
          },
          companyInformation: {
            name: "Test",
            title: "Demo",
            taxNumber: "123456",
            taxOffice: "Demo",
            address: "Demo",
            phone: "**********",
            email: "<EMAIL>",
            hasFindex: false,
            findexExpireDate: null,
            documentsCompleted: false
          }
        },
        isSuccess: true,
        message: "",
        errorCode: null
      };

      resolve({ data: mockProfileData });
    }, 600);
  });
};

/**
 * Mock kullanıcı profil bilgilerini güncelle
 * @param {Object} profileData - Güncellenecek profil verileri
 * @returns {Promise} Mock API yanıtı
 */
const mockUpdateUserProfile = (profileData) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // Başarılı güncelleme yanıtı
      const mockResponse = {
        result: true,
        isSuccess: true,
        message: null,
        errorCode: null
      };

      resolve({ data: mockResponse });
    }, 800);
  });
};

/**
 * Mock kullanıcı şifresini güncelle
 * @param {Object} passwordData - Şifre güncelleme verileri
 * @returns {Promise} Mock API yanıtı
 */
const mockUpdateUserPassword = (passwordData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // Şifre doğrulama kontrolü
      if (passwordData.password !== '12345678Aa.') {
        return reject({
          response: {
            data: {
              result: false,
              isSuccess: false,
              message: 'Mevcut şifre yanlış',
              errorCode: 'INVALID_PASSWORD'
            }
          }
        });
      }

      // Yeni şifre ve onay kontrolü
      if (passwordData.newPassword !== passwordData.confirmNewPassword) {
        return reject({
          response: {
            data: {
              result: false,
              isSuccess: false,
              message: 'Yeni şifre ve onay şifresi eşleşmiyor',
              errorCode: 'PASSWORD_MISMATCH'
            }
          }
        });
      }

      // Başarılı şifre güncelleme yanıtı
      const mockResponse = {
        result: true,
        isSuccess: true,
        message: null,
        errorCode: null
      };

      resolve({ data: mockResponse });
    }, 700);
  });
};

// Kullanılacak servisleri belirleme (mock mu gerçek mi)
const userService = {
  getUserProfile: useMockAPI ? mockGetUserProfile : getUserProfile,
  updateUserProfile: useMockAPI ? mockUpdateUserProfile : updateUserProfile,
  updateUserPassword: useMockAPI ? mockUpdateUserPassword : updateUserPassword,

  // Önbellek yönetimi için yardımcı fonksiyonlar
  clearProfileCache: () => {
    localStorage.removeItem('userProfileCache');
    localStorage.removeItem('profileLastFetched');
  },

  // Profil verilerini önbelleğe alma
  cacheProfileData: (profileData) => {
    localStorage.setItem('userProfileCache', JSON.stringify(profileData));
    localStorage.setItem('profileLastFetched', Date.now().toString());
  },

  // Önbellekteki profil verilerini getirme
  getCachedProfile: () => {
    try {
      const cachedData = localStorage.getItem('userProfileCache');
      const lastFetched = localStorage.getItem('profileLastFetched');

      if (!cachedData || !lastFetched) {
        return null;
      }

      // Önbellek süresi kontrolü (30 dakika)
      const cacheTime = 30 * 60 * 1000; // 30 dakika
      const now = Date.now();

      if (now - parseInt(lastFetched) > cacheTime) {
        // Önbellek süresi dolmuş, temizle
        userService.clearProfileCache();
        return null;
      }

      return JSON.parse(cachedData);
    } catch (error) {
      console.error('Önbellek okuma hatası:', error);
      return null;
    }
  }
};

export default userService;
