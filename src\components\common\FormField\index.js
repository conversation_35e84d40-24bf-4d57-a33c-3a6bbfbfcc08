import React from 'react';
import PropTypes from 'prop-types';
import './style.css';

/**
 * FormField - Projedeki tüm form alanları için kull<PERSON> standart bileşen
 * 
 * @param {Object} props
 * @param {string} props.type - Input tipi: "text", "email", "password", "tel" vb.
 * @param {string} props.name - Input alanı ismi
 * @param {string} props.value - Input değeri
 * @param {function} props.onChange - Değişiklik olayı işleyicisi
 * @param {string} props.placeholder - Placeholder metni
 * @param {boolean} props.required - Zorunlu alan mı?
 * @param {Node} props.icon - Sağ tarafta gösterilecek ikon (isteğe bağlı)
 * @param {string} props.className - Ek CSS sınıfları (isteğe bağlı)
 * @param {Object} props.errorMessage - Hata mesajı (isteğe bağlı)
 * @returns {React.ReactElement}
 */
const FormField = ({
  type = 'text',
  name,
  value,
  onChange,
  placeholder = '',
  required = false,
  icon = null,
  className = '',
  errorMessage = '',
  ...rest
}) => {
  // CSS sınıflarını oluştur
  const fieldClasses = [
    'form-field',
    icon ? 'has-icon' : '',
    errorMessage ? 'has-error' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={fieldClasses}>
      <input
        type={type}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        {...rest}
      />
      {icon && <span className="field-icon">{icon}</span>}
      {errorMessage && <div className="error-message">{errorMessage}</div>}
    </div>
  );
};

FormField.propTypes = {
  type: PropTypes.oneOf(['text', 'email', 'password', 'tel', 'number', 'date', 'time']),
  name: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  icon: PropTypes.node,
  className: PropTypes.string,
  errorMessage: PropTypes.string
};

export default FormField; 