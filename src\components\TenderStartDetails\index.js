import React from "react";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";
import {
  FaCar,
  FaCogs,
  FaTachometerAlt,
  FaEmpire,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaGasPump,
  FaCity,
  FaTools,
  FaSnowflake,
  FaRegClipboard,
  FaMoneyBillWave
} from "react-icons/fa";

import "./style.css";

const TenderStartDetails = ({ tenderParams, carInfo, maintenanceResponsibleId, hasWinterTire, dataSources = {} }) => {
  const { t } = useTranslation();

  // Yardımcı fonksiyonlar
  const getName = (arr, id, key = 'name', fallback = '-') => {
    if (!arr || !id) return fallback;
    const found = arr.find(item => String(item.id) === String(id) || String(item.brandId) === String(id) || String(item.cityId) === String(id));
    return found ? (found[key] || found.responsible || found.statusName || found.typeName || found.period || found.limit || found.limitName || found.purpose) : fallback;
  };

  // Özellikler (sadece araç bilgileri)
  const features = [
    { label: t("tender_start_details.marka"), value: getName(dataSources.brands, tenderParams.brand), icon: <FaCar /> },
    { label: t("tender_start_details.model"), value: getName(dataSources.models, tenderParams.model), icon: <FaCar /> },
    { label: t("tender_start_details.vites"), value: getName(dataSources.transmissionTypes, tenderParams.transmission, 'name'), icon: <FaCogs /> },
    { label: t("tender_start_details.yakit_tipi"), value: getName(dataSources.fuelTypes, tenderParams.fuelType, 'name'), icon: <FaGasPump /> },
    { label: t("tender_start_details.body_type"), value: getName(dataSources.bodyTypes, tenderParams.bodyType, 'name'), icon: <FaEmpire /> },
    { label: t("tender_start_details.sehir"), value: getName(dataSources.cities, tenderParams.deliveryCity), icon: <FaCity /> },
  ];

  // Özellikleri iki kolona böl
  const halfLength = Math.ceil(features.length / 2);
  const firstColumnFeatures = features.slice(0, halfLength);
  const secondColumnFeatures = features.slice(halfLength);

  return (
    <section className="gauto-car-tender section_70">
      <Container>
        <Row>
          <Col lg={6}>
            <div className="car-booking-image">
              {carInfo?.picture ? (
                <img src={carInfo.picture} alt={`${carInfo.brand} ${carInfo.model}`} />
              ) : (
                <div className="car-placeholder">
                  <FaCar size={120} />
                  <p>{t("tender_start_details.gorsel_yok")}</p>
                </div>
              )}
            </div>
          </Col>
          <Col lg={6}>
            <div className="car-booking-right">
              <p className="rental-tag">{t("tender_start_details.ihale")}</p>
              <h3>{getName(dataSources.brands, tenderParams.brand)} {getName(dataSources.models, tenderParams.model)}</h3>
              <div className="price-rating">
                <div className="price-rent">
                  <h4>
                    {carInfo?.monthlyPrice > 0 
                      ? `${carInfo.monthlyPrice} TL` 
                      : t("tender_start_details.fiyat_ihale_sonucu")}
                  </h4>
                </div>
              </div>
              <p>
                {t("tender_start_details.ihale_aciklamasi")}: {getName(dataSources.rentalPeriods, tenderParams.rentalPeriod, 'period')} {t("tender_start_details.ihale_aciklamasi_metin")}
              </p>
              <div className="car-features clearfix">
                {firstColumnFeatures.length > 0 && (
                  <ul>
                    {firstColumnFeatures.map((feature, index) => (
                      <li key={index}>
                        {feature.icon} {feature.label}: {feature.value}
                      </li>
                    ))}
                  </ul>
                )}
                {secondColumnFeatures.length > 0 && (
                  <ul>
                    {secondColumnFeatures.map((feature, index) => (
                      <li key={index}>
                        {feature.icon} {feature.label}: {feature.value}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default TenderStartDetails; 