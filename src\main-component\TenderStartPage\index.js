import React, { Fragment, useEffect, useState, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { Row, Col, Card, Container, Alert, Tab, Tabs, Form, Modal, Button } from "react-bootstrap";
import { FaInfoCircle, FaStar, FaUserClock, FaFileAlt, FaHandshake, FaCheck, FaArrowRight, FaArrowLeft, FaCogs, FaGasPump, FaCarSide, FaMapMarkerAlt, FaHashtag, FaCar, FaMoneyBillWave, FaRegClipboard, FaTachometerAlt, FaCalendarAlt, FaTools, FaSnowflake } from "react-icons/fa";
// Material UI Stepper bileşenlerini import ediyoruz
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import StepButton from '@mui/material/StepButton';
import { ThemeProvider, createTheme } from '@mui/material/styles';

import PageTitle from "../../components/PageTitle";
import TenderList from "../../components/TenderList";
import TenderStartDetails from "../../components/TenderStartDetails";
import TenderPartnerBrands from "../../components/TenderPartnerBrands";
import { updateTenderParams, clearTenderParams } from "../../redux/slices/tenderSlice";
import tenderService from "../../services/tenderService";

import "./style.css";

// Primary color değişkenini CSS'den alıyoruz
const getPrimaryColor = () => {
  // Tarayıcı ortamında mıyız kontrol etmek için
  if (typeof window !== 'undefined') {
    const rootStyles = getComputedStyle(document.documentElement);
    return rootStyles.getPropertyValue('--primary-color').trim() || "#FF6200";
  }
  return "#FF6200"; // Varsayılan değer
};

// LocalStorage anahtarı ve süre (ms)
const FORM_CACHE_KEY = 'tenderFormCache';
const FORM_CACHE_DURATION = 5 * 60 * 1000; // 5 dakika

// Sayfa açıldığında localStorage'dan veri oku
function getInitialFormState() {
  try {
    const cache = JSON.parse(localStorage.getItem(FORM_CACHE_KEY));
    if (cache && cache.timestamp && (Date.now() - cache.timestamp < FORM_CACHE_DURATION)) {
      return {
        formData: cache.formData,
        currentStepIndex: cache.currentStepIndex
      };
    } else {
      localStorage.removeItem(FORM_CACHE_KEY);
    }
  } catch (e) {}
  return null;
}

const initialCache = getInitialFormState();

const TenderStartPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  // URL'den tab parametresini al
  const queryParams = new URLSearchParams(location.search);
  const tabParam = queryParams.get('tab');
  
  const [activeTab, setActiveTab] = useState(() => {
    // Eğer URL'de tab parametresi varsa onu kullan
    if (tabParam) {
      return tabParam;
    }
    // Eğer doğrudan URL'ye erişildiyse (yeni ihale başlatmak için), new-tender kullan
    else if (document.referrer === '') {
      return "new-tender";
    }
    // Aksi halde localStorage'dan al, yoksa varsayılan olarak "new-tender" kullan
    return localStorage.getItem("activeTab") || "new-tender";
  });
  
  // Stepper için aktif adım state'i
  const [currentStepIndex, setCurrentStepIndex] = useState(initialCache ? initialCache.currentStepIndex : 0);
  
  // Kuralları kabul etme durumu için state
  const [rulesAccepted, setRulesAccepted] = useState(false);
  
  // Redux store'dan ihale parametrelerini al
  const { tenderParams, isActive } = useSelector(state => state.tender);
  const { isAuthenticated, user } = useSelector(state => state.auth);
  
  // Form doğrulama state'i
  const [isFormValid, setIsFormValid] = useState(false);
  
  // Form state'leri - sadece bir kez başlatılsın
  const [formData, setFormData] = useState(initialCache ? initialCache.formData : {
    usagePurpose: '',
    vehicleCondition: '',
    transmission: '',
    fuelType: '',
    bodyType: '',
    deliveryCity: '',
    quantity: '1',
    brand: '',
    model: '',
    paymentMethod: '',
    declarationLimit: '',
    mileageLimit: '',
    rentalPeriod: '',
    maintenanceResponsibleId: '',
    hasWinterTire: false,
  });

  // Dinamik veri kaynakları state'i
  const [dataSources, setDataSources] = useState({
    usagePurposes: [],
    vehicleConditions: [],
    transmissionTypes: [],
    fuelTypes: [],
    bodyTypes: [],
    cities: [],
    paymentMethods: [],
    declarationLimits: [],
    mileageLimits: [],
    rentalPeriods: [],
    brands: [],
    models: [],
    maintenanceResponsibles: [],
  });

  // carInfo'yu en başta tanımla
  const carInfo = useMemo(() => {
    let modelObj = dataSources.models.find(m => m.id === formData.model);
    return {
      id: 1,
      image: modelObj?.picture || '',
      picture: modelObj?.picture || '',
      model: modelObj?.name || formData.model || 'Yok',
      brand: dataSources.brands.find(b => b.brandId === formData.brand)?.name || 'Yok',
      transmission: formData.transmission || 'Otomatik',
      fuelType: formData.fuelType || 'Dizel',
    };
  }, [formData.model, formData.brand, formData.transmission, formData.fuelType, dataSources.models, dataSources.brands]);
  
  const [isOptionsLoading, setIsOptionsLoading] = useState(true);
  const [optionsError, setOptionsError] = useState(null);

  // Component yüklendiğinde primary color değerini güncelliyoruz
  const [primaryColor, setPrimaryColor] = useState("#FF6200");
  const [primaryLight, setPrimaryLight] = useState("#ff8133");
  
  const [isModelsLoading, setIsModelsLoading] = useState(false);
  
  useEffect(() => {
    setPrimaryColor(getPrimaryColor());
  }, []);

  // Material UI tema özelleştirmesi
  const theme = useMemo(() => createTheme({
    palette: {
      primary: {
        main: primaryColor,
      },
      success: {
        main: '#28a745',
      },
    },
    components: {
      MuiStepIcon: {
        styleOverrides: {
          root: {
            '&.Mui-active': {
              color: primaryColor,
            },
            '&.Mui-completed': {
              color: '#28a745',
            },
          },
        },
      },
      MuiStepLabel: {
        styleOverrides: {
          label: {
            '&.Mui-active': {
              color: primaryColor,
              fontWeight: 600,
            },
            '&.Mui-completed': {
              color: '#28a745',
            },
          },
        },
      },
    },
  }), [primaryColor]);

  // Form değişikliğini izleme - useCallback ile optimize edildi
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Form validasyonu için gerekli alanları useMemo ile tanımlıyoruz
  const requiredFields = useMemo(() => [
    'usagePurpose', 'vehicleCondition', 'transmission', 
    'fuelType', 'bodyType', 'deliveryCity', 'quantity', 
    'brand', 'model', 'paymentMethod', 'declarationLimit', 
    'mileageLimit', 'rentalPeriod', 'maintenanceResponsibleId'
  ], []);
  
  // Her form değişikliğinde form validasyonu yap
  useEffect(() => {
    const isValid = requiredFields.every(field => formData[field] !== "");
    setIsFormValid(isValid);
  }, [formData, requiredFields]);
  
  // Form submit işlemi - useCallback ile optimize edildi
  const handleFormSubmit = useCallback((e) => {
    e.preventDefault();
    
    // Redux'a form verilerini gönder
    dispatch(updateTenderParams(formData));
    
    // Bir sonraki adıma geç
    setCurrentStepIndex(1);
  }, [formData, dispatch]);
  
  // Stepper adımları arasında geçiş - useCallback ile optimize edildi
  const handleNextStep = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(prevStep => prevStep + 1);
    }
  }, [currentStepIndex]);
  
  const handlePrevStep = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(prevStep => prevStep - 1);
    }
  }, [currentStepIndex]);
  
  // İhale başlatma işlemini gerçekleştir - useCallback ile optimize edildi
  const handleStartTender = useCallback(async () => {
    try {
      const payload = {
        vehicleBrandId: formData.brand,
        vehicleModelId: formData.model,
        vehicleYear: formData.year || 2023,
        fuelTypeId: formData.fuelType,
        gearTypeId: formData.transmission,
        usageStatusId: formData.vehicleCondition,
        vehicleBodyTypeId: formData.bodyType,
        kmLimitId: formData.mileageLimit,
        deliveryCityId: formData.deliveryCity,
        usagePurposeId: formData.usagePurpose,
        rentalPeriodId: formData.rentalPeriod,
        declarationLimitId: formData.declarationLimit,
        maintenanceResponsibleId: formData.maintenanceResponsibleId,
        tenderStatusId: 100,
        hasWinterTire: formData.hasWinterTire,
        vehicleQuantity: formData.quantity
      };
      const res = await tenderService.createTender(payload);
      if (res.data && res.data.isSuccess && res.data.result?.tenderId) {
        dispatch(clearTenderParams());
        navigate('/tender-details', { state: { tenderId: res.data.result.tenderId } });
      } else {
        alert(res.data.message || 'İhale başlatılamadı!');
      }
    } catch (err) {
      alert('Bir hata oluştu, lütfen tekrar deneyin.');
    }
  }, [formData, dispatch, navigate]);
  
  // Tab değişikliğini izle - useCallback ile optimize edildi
  const handleTabChange = useCallback((tabKey) => {
    setActiveTab(tabKey);
    // Seçilen tab'i localStorage'a kaydet
    localStorage.setItem("activeTab", tabKey);
    
    // URL'i güncelle (sayfa yenilenmeden)
    navigate(`?tab=${tabKey}`, { replace: true });
    
    // "Yeni İhale Başlat" sekmesine geçilirse adımları sıfırla
    if (tabKey === "new-tender") {
      setCurrentStepIndex(0);
    }
  }, [navigate]);
  
  // Modal Açma/Kapama - useCallback ile optimize edildi
  const [showInfoModal, setShowInfoModal] = useState(false);
  const handleShowInfoModal = useCallback(() => setShowInfoModal(true), []);
  const handleCloseInfoModal = useCallback(() => setShowInfoModal(false), []);
  
  // Detaylı Bilgi sayfasına yönlendirme - useCallback ile optimize edildi
  const handleGoToDetailPage = useCallback(() => {
    navigate('/ihale-sureci-detay');
    handleCloseInfoModal();
  }, [navigate, handleCloseInfoModal]);

  // Adımların tanımlanması - useMemo ile optimize edildi
  const steps = useMemo(() => [
    { 
      title: t("tender_start.step_form_title"), 
      onClick: () => setCurrentStepIndex(0),
      description: t("tender_start.step_form_desc"), 
      completed: currentStepIndex > 0
    },
    { 
      title: t("tender_start.step_details_title"), 
      onClick: () => setCurrentStepIndex(1),
      description: t("tender_start.step_details_desc"), 
      completed: currentStepIndex > 1 
    },
    { 
      title: t("tender_start.step_summary_title"), 
      onClick: () => setCurrentStepIndex(2),
      description: t("tender_start.step_summary_desc"), 
      completed: false 
    }
  ], [t, currentStepIndex, setCurrentStepIndex]);
  
  // Adım içeriğini render et - her değişiklikte yeniden oluşturmak yerine useCallback ile optimize edildi
  const renderStepContent = useCallback(() => {
    switch(currentStepIndex) {
      case 0:
        return (
          <Card className="tender-start-card mb-4">
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="text-white mb-0">{t("tender_start.card_title")}</h5>
              <button 
                className="info-button"
                onClick={handleShowInfoModal}
                aria-label={t("tender_start.info_button_aria")}
              >
                <FaInfoCircle size={20} />
              </button>
            </Card.Header>
            <Card.Body>
              <p className="tender-intro-text mb-4">
                {t("tender_start.intro_text")}
              </p>
              
              <Form onSubmit={handleFormSubmit}>
                <Row className="mb-4">
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaStar className="input-label-icon" />{t("tender_start.usage_purpose")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="usagePurpose"
                        value={formData.usagePurpose}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.usagePurposes.map((item) => (
                          <option key={item.id} value={item.id}>{item.purpose}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaCogs className="input-label-icon" />{t("tender_start.vehicle_condition")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="vehicleCondition"
                        value={formData.vehicleCondition}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.vehicleConditions.map((item) => (
                          <option key={item.id} value={item.id}>{item.statusName}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaCogs className="input-label-icon" />{t("tender_start.transmission")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="transmission"
                        value={formData.transmission}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.transmissionTypes.map((item) => (
                          <option key={item.id} value={item.id}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaGasPump className="input-label-icon" />{t("tender_start.fuel_type")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="fuelType"
                        value={formData.fuelType}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.fuelTypes.map((item) => (
                          <option key={item.id} value={item.id}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaCarSide className="input-label-icon" />{t("tender_start.body_type")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="bodyType"
                        value={formData.bodyType}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.bodyTypes.map((item) => (
                          <option key={item.id} value={item.id}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaMapMarkerAlt className="input-label-icon" />{t("tender_start.delivery_city")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="deliveryCity"
                        value={formData.deliveryCity}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.cities.map((city) => (
                          <option key={city.cityId} value={city.cityId}>{city.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaHashtag className="input-label-icon" />{t("tender_start.quantity")}</Form.Label>
                      <Form.Control
                        type="number"
                        name="quantity"
                        min="1"
                        value={formData.quantity}
                        onChange={handleInputChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaCar className="input-label-icon" />{t("tender_start.brand")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="brand"
                        value={formData.brand}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.brands.map((item) => (
                          <option key={item.brandId} value={item.brandId}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaCar className="input-label-icon" />{t("tender_start.model")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="model"
                        value={formData.model}
                        onChange={handleInputChange}
                        required
                        disabled={!formData.brand || isModelsLoading}
                      >
                        <option value="">{isModelsLoading ? t("tender_start.loading") : t("tender_start.select")}</option>
                        {dataSources.models.map((item) => (
                          <option key={item.id} value={item.id}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaMoneyBillWave className="input-label-icon" />{t("tender_start.payment_method")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="paymentMethod"
                        value={formData.paymentMethod}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.paymentMethods.map((item) => (
                          <option key={item.id} value={item.id}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaRegClipboard className="input-label-icon" />{t("tender_start.declaration_limit")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="declarationLimit"
                        value={formData.declarationLimit}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.declarationLimits.map((item) => (
                          <option key={item.id} value={item.id}>{item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaCalendarAlt className="input-label-icon" />{t("tender_start.rental_period")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="rentalPeriod"
                        value={formData.rentalPeriod}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.rentalPeriods.map((item) => (
                          <option key={item.id} value={item.id}>{item.period}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaTools className="input-label-icon" />{t("tender_start.maintenance_responsible")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="maintenanceResponsibleId"
                        value={formData.maintenanceResponsibleId}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.maintenanceResponsibles && dataSources.maintenanceResponsibles.map((item) => (
                          <option key={item.id} value={item.id}>{item.responsible || item.name}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaSnowflake className="input-label-icon" />{t("tender_start.has_winter_tire")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="hasWinterTire"
                        value={formData.hasWinterTire ? 'true' : 'false'}
                        onChange={e => setFormData(prev => ({ ...prev, hasWinterTire: e.target.value === 'true' }))}
                        required
                      >
                        <option value="false">{t("common.no")}</option>
                        <option value="true">{t("common.yes")}</option>
                      </Form.Control>
                    </Form.Group>
                  </Col>
                  <Col lg={6} md={6} sm={12} className="mb-3">
                    <Form.Group>
                      <Form.Label><FaTachometerAlt className="input-label-icon" />{t("tender_start.mileage_limit")}</Form.Label>
                      <Form.Control
                        as="select"
                        name="mileageLimit"
                        value={formData.mileageLimit}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">{t("tender_start.select")}</option>
                        {dataSources.mileageLimits.map((item) => (
                          <option key={item.id} value={item.id}>{item.limit}</option>
                        ))}
                      </Form.Control>
                    </Form.Group>
                  </Col>
                </Row>
                
                <div className="navigation-buttons justify-content-end">
                  <button 
                    type="submit" 
                    className="gauto-theme-btn btn-sm"
                    disabled={!isFormValid}>
                    {t("tender_start.continue")}
                    <FaArrowRight className="btn-icon" />
                  </button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        );
        
      case 1:
        return (
          <>
            <TenderStartDetails 
              tenderParams={formData} 
              carInfo={carInfo} 
              maintenanceResponsibleId={formData.maintenanceResponsibleId}
              hasWinterTire={formData.hasWinterTire}
              dataSources={dataSources}
            />
            
            {/* İhaleye katılabilecek firmalar slider'ı */}
            <TenderPartnerBrands carId={carInfo.id} />
            
            <div className="navigation-buttons">
              <button 
                className="gauto-theme-btn secondary-btn btn-sm"
                onClick={handlePrevStep}>
                <FaArrowLeft className="btn-icon" />
                {t("tender_start.back")}
              </button>
              <button 
                className="gauto-theme-btn btn-sm"
                onClick={handleNextStep}>
                {t("tender_start.continue_summary")}
                <FaArrowRight className="btn-icon" />
              </button>
            </div>
          </>
        );
        
      case 2:
        return (
          <Card className="tender-start-card mb-4">
            <Card.Header>
              <h5 className="text-white mb-0">{t("tender_start.summary_title")}</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6}>
                  <div className="summary-section">
                    <h6 className="section-title">{t("tender_start.vehicle_info")}</h6>
                    <div className="summary-list">
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.brand")}:</span>
                        <span className="item-value">{dataSources.brands.find(b => String(b.brandId) === String(formData.brand))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.model")}:</span>
                        <span className="item-value">{dataSources.models.find(m => String(m.id) === String(formData.model))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.vehicle_condition")}:</span>
                        <span className="item-value">{dataSources.vehicleConditions.find(v => String(v.id) === String(formData.vehicleCondition))?.statusName || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.transmission")}:</span>
                        <span className="item-value">{dataSources.transmissionTypes.find(t => String(t.id) === String(formData.transmission))?.typeName || dataSources.transmissionTypes.find(t => String(t.id) === String(formData.transmission))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.fuel_type")}:</span>
                        <span className="item-value">{dataSources.fuelTypes.find(f => String(f.id) === String(formData.fuelType))?.typeName || dataSources.fuelTypes.find(f => String(f.id) === String(formData.fuelType))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.body_type")}:</span>
                        <span className="item-value">{dataSources.bodyTypes.find(b => String(b.id) === String(formData.bodyType))?.typeName || dataSources.bodyTypes.find(b => String(b.id) === String(formData.bodyType))?.name || '-'}</span>
                      </div>
                      {formData.year && (
                        <div className="summary-item">
                          <span className="item-label">{t("tender_start.model_year")}:</span>
                          <span className="item-value">{formData.year}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </Col>
                <Col md={6}>
                  <div className="summary-section">
                    <h6 className="section-title">{t("tender_start.rental_info")}</h6>
                    <div className="summary-list">
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.quantity")}:</span>
                        <span className="item-value">{formData.quantity || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.usage_purpose")}:</span>
                        <span className="item-value">{dataSources.usagePurposes.find(u => String(u.id) === String(formData.usagePurpose))?.purpose || dataSources.usagePurposes.find(u => String(u.id) === String(formData.usagePurpose))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.delivery_city")}:</span>
                        <span className="item-value">{dataSources.cities.find(c => String(c.cityId) === String(formData.deliveryCity))?.name || '-'}</span>
                      </div>
                      {formData.district && (
                        <div className="summary-item">
                          <span className="item-label">{t("tender_start.delivery_district")}:</span>
                          <span className="item-value">{formData.district}</span>
                        </div>
                      )}
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.payment_method")}:</span>
                        <span className="item-value">{dataSources.paymentMethods.find(p => String(p.id) === String(formData.paymentMethod))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.mileage_limit")}:</span>
                        <span className="item-value">{dataSources.mileageLimits.find(m => String(m.id) === String(formData.mileageLimit))?.limit || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.rental_period")}:</span>
                        <span className="item-value">{dataSources.rentalPeriods.find(r => String(r.id) === String(formData.rentalPeriod))?.period || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.declaration_limit")}:</span>
                        <span className="item-value">{dataSources.declarationLimits.find(d => String(d.id) === String(formData.declarationLimit))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.maintenance_responsible")}:</span>
                        <span className="item-value">{dataSources.maintenanceResponsibles.find(m => String(m.id) === String(formData.maintenanceResponsibleId))?.responsible || dataSources.maintenanceResponsibles.find(m => String(m.id) === String(formData.maintenanceResponsibleId))?.name || '-'}</span>
                      </div>
                      <div className="summary-item">
                        <span className="item-label">{t("tender_start.has_winter_tire")}:</span>
                        <span className="item-value">{formData.hasWinterTire ? t("common.yes") : t("common.no")}</span>
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
              
              <div className="tender-rules mt-4">
                <h6 className="section-title">{t("tender_start.tender_rules")}</h6>
                <div className="rules-content">
                  <p>{t("tender_start.rules_content")}</p>
                  <Form.Check 
                    type="checkbox" 
                    id="rulesCheck"
                    label={t("tender_start.accept_rules")} 
                    className="mt-3"
                    required
                    checked={rulesAccepted}
                    onChange={(e) => setRulesAccepted(e.target.checked)}
                  />
                </div>
              </div>
              
              <div className="navigation-buttons">
                <button 
                  className="gauto-theme-btn secondary-btn btn-sm"
                  onClick={handlePrevStep}>
                  <FaArrowLeft className="btn-icon" />
                  {t("tender_start.back")}
                </button>
                <button 
                  className="gauto-theme-btn btn-sm"
                  onClick={handleStartTender}
                  disabled={!rulesAccepted}>
                  <FaCheck className="btn-icon" />
                  {t("tender_start.start_tender")}
                </button>
              </div>
            </Card.Body>
          </Card>
        );
        
      default:
        return null;
    }
  }, [
    currentStepIndex, t, handleShowInfoModal, formData, 
    dataSources, isFormValid, handleFormSubmit, carInfo, 
    handlePrevStep, handleNextStep, rulesAccepted, 
    setRulesAccepted, handleStartTender, handleInputChange, isModelsLoading
  ]);

  // 1. localStorage'a kaydet
  useEffect(() => {
    localStorage.setItem(FORM_CACHE_KEY, JSON.stringify({
      formData,
      currentStepIndex,
      timestamp: Date.now()
    }));
  }, [formData, currentStepIndex]);

  // 2. opsiyonları yükle
  useEffect(() => {
    setIsOptionsLoading(true);
    tenderService.getTenderOptions()
      .then((res) => {
        const options = res.data.result;
        setDataSources({
          usagePurposes: options.usagePurposes || [],
          vehicleConditions: options.usageStatus || [],
          transmissionTypes: options.gearTypes || [],
          fuelTypes: options.fuelTypes || [],
          bodyTypes: options.vehicleBodyTypes || [],
          cities: options.cities || [],
          paymentMethods: [ // API'den gelmediği için sabit ekliyoruz
            { id: 1, name: 'Nakit' },
            { id: 2, name: 'Vadeli' }
          ],
          declarationLimits: options.declarationLimits || [],
          mileageLimits: options.kmLimits || [],
          rentalPeriods: options.rentalPeriods || [],
          brands: options.vehicleBrands || [],
          models: options.vehicleBrands && formData.brand
            ? ((options.vehicleBrands.find(b => b.name === formData.brand)?.vehicleModels || []).map(model => ({
              id: model.modelId,
              name: model.name,
              picture: model.picture
            })) )
            : [],
          maintenanceResponsibles: options.maintenanceResponsibles || [],
        });
        setIsOptionsLoading(false);
      })
      .catch((err) => {
        setOptionsError("Opsiyonlar yüklenemedi");
        setIsOptionsLoading(false);
      });
  }, []);

  // 3. Brand değiştiğinde model listesini yükle
  useEffect(() => {
    if (!formData.brand) {
      setDataSources(prev => ({ ...prev, models: [] }));
      return;
    }
    const selectedBrand = dataSources.brands.find(b => b.brandId === formData.brand);
    if (selectedBrand && selectedBrand.brandId) {
      tenderService.getVehicleModels(selectedBrand.brandId)
        .then(res => {
          setDataSources(prev => ({
            ...prev,
            models: (res.data.result.vehicleModels || []).map(model => ({
              id: model.modelId,
              name: model.name,
              picture: model.picture
            }))
          }));
        })
        .catch(() => {
          setDataSources(prev => ({ ...prev, models: [] }));
        });
    }
  }, [formData.brand, dataSources.brands]);

  // KOŞULLU RETURN'LAR
  if (isOptionsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[300px]">
        <span className="text-lg font-semibold">Yükleniyor...</span>
      </div>
    );
  }
  if (optionsError) {
    return (
      <div className="flex items-center justify-center min-h-[300px] text-red-500">
        <span>{optionsError}</span>
      </div>
    );
  }

  // JSX return
  return (
    <Fragment>
      <PageTitle
        pageTitle={t("tender_start.page_title")}
        pagesub={t("tender_start.page_subtitle")}
      />
      
      <div className="tender-start-container">
        <Container>
          {!isAuthenticated && (
            <Alert variant="warning" className="mb-4">
              <Alert.Heading>{t("tender_start.alert_title")}</Alert.Heading>
              <p>{t("tender_start.alert_message")}</p>
            </Alert>
          )}
          
          <Tabs 
            activeKey={activeTab}
            className="mb-4"
            onSelect={handleTabChange}
          >
            <Tab eventKey="new-tender" title={t("tender_start.tab_new_tender")}>
              {/* Material UI Stepper bileşeni */}
              <div className="custom-stepper">
                <ThemeProvider theme={theme}>
                  <Stepper activeStep={currentStepIndex} alternativeLabel>
                    {steps.map((step, index) => (
                      <Step key={index} completed={step.completed}>
                        <StepButton
                          onClick={() => step.onClick && step.onClick()}
                          optional={step.description ? <span className="step-description">{step.description}</span> : null}
                        >
                          {step.title}
                        </StepButton>
                      </Step>
                    ))}
                  </Stepper>
                </ThemeProvider>
              </div>
              
              {/* Adım içeriği */}
              {renderStepContent()}
            </Tab>
            <Tab eventKey="my-tenders" title={t("tender_start.tab_my_tenders")}>
              <TenderList 
                userId={isAuthenticated && user ? user.id || 1 : null}
                auctionState="ALL"
              />
              <div className="mt-3 text-muted">
                <small>{t("tender_start.user_id_note")}</small>
              </div>
            </Tab>
          </Tabs>
        </Container>
      </div>
      
      {/* İhale Süreci Bilgi Modalı */}
      <Modal show={showInfoModal} onHide={handleCloseInfoModal} size="lg" centered>
        <Modal.Header closeButton>
          <Modal.Title>{t("tender_process.title")}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="tender-process-steps">
            <div className="process-step-card">
              <div className="card-icon">
                <FaFileAlt />
              </div>
              <div className="step-content">
                <h5>{t("tender_process.step1_title")}</h5>
                <p>{t("tender_process.step1_description")}</p>
              </div>
            </div>
            
            <div className="process-step-card">
              <div className="card-icon">
                <FaUserClock />
              </div>
              <div className="step-content">
                <h5>{t("tender_process.step2_title")}</h5>
                <p dangerouslySetInnerHTML={{ __html: t("tender_process.step2_description") }}></p>
              </div>
            </div>
            
            <div className="process-step-card">
              <div className="card-icon">
                <FaStar />
              </div>
              <div className="step-content">
                <h5>{t("tender_process.step3_title")}</h5>
                <p dangerouslySetInnerHTML={{ __html: t("tender_process.step3_description") }}></p>
              </div>
            </div>
            
            <div className="process-step-card last-step">
              <div className="card-icon success-icon">
                <FaHandshake />
              </div>
              <div className="step-content">
                <h5>{t("tender_process.step4_title")}</h5>
                <p>{t("tender_process.step4_description")}</p>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseInfoModal}>
            {t("common.close")}
          </Button>
          <Button variant="primary" onClick={handleGoToDetailPage}>
            {t("common.detailed_info")}
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  );
};

export default TenderStartPage; 