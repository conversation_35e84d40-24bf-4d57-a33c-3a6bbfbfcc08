import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import Product from "../../components/Products";

const ProductPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.product")}
        pagesub={t("header-navigation.product")}
      />
      <Product />
    </Fragment>
  );
};
export default ProductPage;
