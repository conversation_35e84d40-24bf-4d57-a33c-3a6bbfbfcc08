/* ===================== ABOUT PAGE ======================*/
/* CSS değişkenleri tanımlayalım */
@import url('../../styles/colors.css');

/* Tü<PERSON> bölümlerin ortak stili */
.section_70 {
  padding: 70px 0;
}

.about-page-left h4 {
  font-size: 20px;
  color: var(--primary-color);
  margin-bottom: 10px;
  display: block;
  font-weight: 500;
  text-transform: capitalize;
  font-family: "Rubik", sans-serif;
}

.about-page-left h3 {
  font-size: 36px;
  color: #001238;
  letter-spacing: 1px;
  margin-bottom: 15px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: inline-block;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 45px;
}

.about-page-left p span {
  color: #111;
  text-transform: capitalize;
}

.about-page-left p {
  margin: 0 0 10px;
}

.about-list {
  list-style: none;
  padding: 0;
  margin: 20px 0;
}

.about-list li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 25px;
  color: #565656;
}

.about-list li i {
  position: absolute;
  left: 0;
  top: 2px;
  color: var(--primary-color);
}

.about-page-call {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-top: 30px;
  -ms-flex-line-pack: center;
  align-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.page-call-icon {
  font-size: 50px;
  margin-right: 30px;
  color: var(--primary-color);
}

.call-info p {
  text-transform: capitalize;
  color: var(--primary-color);
  font-size: 17px;
  margin-bottom: 5px;
}

.call-info h4 {
  color: #001238;
  font-size: 20px;
  letter-spacing: 2px;
}

.call-info h4 a {
  color: #001238;
}

.about-promo-text {
  text-align: center;
  width: 55%;
  margin: 0 auto;
}

.gauto-about-promo {
  padding-bottom: 50px;
  background: #fbfbfd none repeat scroll 0 0;
}

.about-promo-text h3 {
  font-size: 36px;
  color: #001238;
  letter-spacing: 1px;
  margin-bottom: 15px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: inline-block;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 45px;
}

.about-promo-text h3 span {
  color: var(--primary-color);
}

.about-promo-image {
  margin-top: 30px;
}

/* How it works section */
.how-it-works {
  background-color: #f8f9fa;
  padding: 80px 0;
}

.how-it-works .site-heading {
  text-align: center;
  margin-bottom: 50px;
}

.process-title-area {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.process-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: var(--primary-color);
  font-size: 20px;
  margin-right: 15px;
}

.subtitle {
  text-transform: uppercase;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 5px;
}

.process-step-card {
  position: relative;
  background-color: #ffffff;
  border-radius: 10px;
  padding: 25px 20px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.5s ease;
  overflow: visible;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
}

.process-step-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.process-step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  font-weight: bold;
  font-size: 22px;
  margin-bottom: 15px;
  box-shadow: 0 4px 10px var(--primary-shadow);
  position: relative;
  z-index: 10;
  transition: all 0.3s ease;
}

.process-step-number:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(var(--primary-color-rgb), 0.5);
}

.process-step-number span {
  display: block;
  line-height: 1;
}

.process-step-content h3 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #1c3b37;
}

.process-step-content p {
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.process-step-card:hover .process-step-number {
  transform: scale(1.1);
}

/* SVG Path için stiller */
.process-svg-container {
  display: none;
}

.process-path {
  display: none;
}

/* Process Steps Row için pozisyon */
.process-steps-row {
  position: relative;
  display: flex;
  justify-content: center;
}

.process-step-col {
  display: flex;
  justify-content: center;
}

/* Numaralar arasındaki bağlantı çizgileri için stil */
.connecting-paths {
  display: none;
}

/* About promo section */
.about-promo-text {
  margin-bottom: 30px;
  text-align: center;
}

.about-promo-text h3 {
  color: #001238;
  font-weight: 600;
  font-size: 24px;
}

.about-promo-text h3 span {
  color: var(--primary-color);
}

.about-promo-image img {
  max-width: 100%;
  border-radius: 5px;
}

@media (max-width: 767.98px) {
  .process-title-area {
    flex-direction: column;
    text-align: center;
  }
  
  .process-icon {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .about-page-left h3,
  .about-promo-text h3 {
    font-size: 30px;
  }
  .about-page-right {
    margin-top: 30px;
  }
  .about-page-right img {
    width: 100%;
  }
  .about-promo-text {
    width: 80%;
  }
  .process-step-card {
    margin-bottom: 20px;
  }
  .process-step-number {
    width: 60px;
    height: 60px;
  }
  .process-step-number span {
    font-size: 26px;
  }
  .process-step-content h3 {
    font-size: 20px;
  }
  .how-it-works .main-title {
    font-size: 36px;
  }
}

@media (max-width: 767px) {
  .about-page-left h3,
  .about-promo-text h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .about-page-right {
    margin-top: 30px;
  }
  .about-promo-text {
    width: 100%;
  }
  .process-step-card {
    margin-bottom: 20px;
  }
  .process-step-content h3 {
    font-size: 20px;
  }
  .how-it-works .main-title {
    font-size: 30px;
    line-height: 1.3;
  }
  .how-it-works {
    padding: 60px 0;
  }
}

@media only screen and (min-width: 480px) and (max-width: 767px) {
  .about-page-left h3,
  .about-promo-text h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .about-page-right {
    margin-top: 30px;
  }
  .about-promo-text {
    width: 100%;
  }
}

/* Mobil cihazlarda SVG container'ı gizle */
@media (max-width: 991.98px) {
  .process-svg-container {
    display: none;
  }
  
  .process-step-col:nth-child(odd),
  .process-step-col:nth-child(even) {
    transform: translateY(0);
  }
  
  .process-step-card {
    margin-bottom: 30px;
    animation: fadeInUp 0.8s ease-out forwards;
    animation-delay: 0.1s !important;
  }
  
  .process-step-card:hover {
    transform: translateY(-5px);
  }
}

/* Container stil düzenlemeleri */
.position-relative {
  overflow: visible !important;
  padding-top: 30px;
  padding-bottom: 30px;
  position: relative;
}

/* react-archer için stil düzenlemeleri */
._react-archer__arrow {
  z-index: 5 !important;
  filter: drop-shadow(0 0 2px rgba(var(--primary-color-rgb), 0.4));
  pointer-events: none;
  animation: fadeIn 1s ease-out forwards;
  animation-delay: 1.5s;
  opacity: 0;
}

._react-archer__arrow-path {
  animation: dash 25s linear infinite;
  stroke-linecap: round;
  pointer-events: none;
}

@keyframes dash {
  to {
    stroke-dashoffset: -300;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* SVG Container için stiller */
._react-archer__svg-container {
  overflow: visible !important;
  z-index: 2 !important;
  position: absolute !important;
  pointer-events: none;
}

/* Süreç kartları için farklı konumlar - ama animasyonsuz */
.process-step-col:nth-child(odd) {
  transform: translateY(-20px);
}

.process-step-col:nth-child(even) {
  transform: translateY(20px);
}

/* Kartların asenkron animasyonları için farklı gecikmeler - sadece fadeInUp için */
.process-step-col:nth-child(1) .process-step-card {
  animation-delay: 0.1s;
}

.process-step-col:nth-child(2) .process-step-card {
  animation-delay: 0.3s;
}

.process-step-col:nth-child(3) .process-step-card {
  animation-delay: 0.5s;
}

.process-step-col:nth-child(4) .process-step-card {
  animation-delay: 0.7s;
}

/* Numaralı butonlar için dönen parıltı efekti */
.process-step-number::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(var(--primary-color-rgb), 0.3);
  top: 0;
  left: 0;
  animation: pulse 3s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* fadeInUp animasyonu kartların ilk görünüm efekti için */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
