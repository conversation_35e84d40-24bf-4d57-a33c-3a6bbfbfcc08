/* ===================== CAR BOOKING ======================*/
@import "/node_modules/@syncfusion/ej2-base/styles/material.css";
@import "/node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "/node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "/node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "/node_modules/@syncfusion/ej2-react-calendars/styles/material.css";
@import url('../../styles/colors.css');
.gauto-car-booking {
  background: #fbfbfb none repeat scroll 0 0;
}

.rental-tag {
  display: inline-block;
  padding: 5px 15px;
  line-height: 20px;
  text-transform: uppercase;
  background: var(--primary-color);
  color: #fff;
  font-weight: 500;
  font-size: 14px;
  border-radius: 3px;
  margin-bottom: 5px;
}

.car-booking-right h3 {
  font-size: 30px;
  color: #001238;
  letter-spacing: 1px;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: block;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 45px;
}

.car-booking-right h4 {
  color: #020202;
  font-size: 18px;
  text-transform: capitalize;
  margin-top: 0px;
  display: inline-block;
}

.car-booking-right h4 span {
  text-transform: capitalize;
  color: var(--primary-color);
  font-size: 16px;
}

.price-rent {
  display: inline-block;
  margin-right: 15px;
}

.car-rating {
  display: inline-block;
}

.car-rating ul {
  display: inline-block;
}

.car-rating ul li {
  display: inline-block;
  margin-right: 1px;
  color: #ffcd00;
}

.car-rating p {
  display: inline-block;
  margin-left: 5px;
  color: #001238;
  text-transform: capitalize;
}

.price-rating {
  margin-bottom: 20px;
}

.car-features ul {
  width: 32%;
  float: left;
  margin-top: 20px;
}

.car-features ul li {
  margin: 5px 0;
}

.car-features ul li svg {
  margin-right: 5px;
  fill: var(--primary-color);
}

.single-booking h3,
.booking-right h3 {
  font-size: 23px;
  color: #001238;
  letter-spacing: 1px;
  margin-bottom: 10px;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  display: block;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  line-height: 28px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0ff;
}

.single-booking form label {
  display: block;
  color: #001238;
  font-weight: 500;
  font-size: 14px;
  text-transform: capitalize;
}

.single-booking form p {
  margin-top: 15px;
}

.single-booking form p input,
.single-booking form p select {
  width: 100%;
  border: 2px solid #f0f0ff;
  padding: 5px 10px;
  height: 45px;
  color: #111;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

.single-booking form p textarea {
  width: 100%;
  border: 2px solid #f0f0ff;
  padding: 5px 10px;
  height: 120px;
  color: #111;
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
}

.single-booking form p input:focus,
.single-booking form p select:focus {
  border: 2px solid var(--primary-color);
}

.single-booking {
  margin-bottom: 30px;
}

.single-booking:last-child {
  margin-bottom: 0px;
}

.gauto-payment .payment {
  display: block;
  position: relative;
  float: left;
  width: 100%;
  height: auto;
}

.gauto-payment .payment input[type="radio"] {
  position: absolute;
  visibility: hidden;
}

.gauto-payment .payment label {
  display: inline-block;
  position: relative;
  padding: 0 0 0 30px;
  margin: 10px auto;
  z-index: 9;
  cursor: pointer;
  -webkit-transition: all 0.25s linear;
  color: #020202;
  font-weight: 500;
}

.gauto-payment .payment .check {
  display: block;
  position: absolute;
  border: 3px solid #aaaaaa;
  border-radius: 100%;
  height: 20px;
  width: 20px;
  top: 14px;
  left: 0;
  z-index: 5;
  transition: border 0.25s linear;
  -webkit-transition: border 0.25s linear;
}

.gauto-payment .payment .check:before {
  display: block;
  position: absolute;
  content: "";
  border-radius: 100%;
  height: 8px;
  width: 8px;
  top: 3px;
  left: 3px;
  margin: auto;
  transition: background 0.25s linear;
  -webkit-transition: background 0.25s linear;
}

.gauto-payment .payment input[type="radio"]:checked ~ .check {
  border: 3px solid var(--primary-color);
}

.gauto-payment .payment input[type="radio"]:checked ~ .check:before {
  background: var(--primary-color);
}

.gauto-payment .payment input[type="radio"]:checked ~ label {
  color: var(--primary-color);
}

.payment img {
  float: right;
  margin-top: 15px;
}

.action-btn {
  text-align: right;
}

.action-btn a.gauto-btn {
  color: var(--primary-color);
  margin: 30px 0 0 0;
}

.action-btn a.gauto-btn:hover {
  color: #fff;
}

@media (min-width: 768px) and (max-width: 991px) {
  .car-booking-image img {
    width: 100%;
  }
  .car-booking-right {
    margin-top: 30px;
  }
  .booking-right {
    margin-top: 30px;
  }
}

@media (max-width: 767px) {
  .car-booking-right {
    margin-top: 30px;
  }
  .car-booking-right h3,
  .product-details-text h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .car-features ul {
    width: 50%;
  }
  .booking-right {
    margin-top: 30px;
  }
}
@media only screen and (min-width: 480px) and (max-width: 767px) {
  .car-booking-right {
    margin-top: 30px;
  }
  .car-booking-right h3,
  .product-details-text h3 {
    font-size: 28px;
    line-height: 40px;
  }
  .car-features ul {
    width: 33%;
  }
  .booking-right {
    margin-top: 30px;
  }
}

.pickup-location {
  background: var(--primary-color);
}

.booking-right-form h3 span {
  color: var(--primary-color);
}

.booking-details-text ul li svg {
  fill: var(--primary-color);
}

.payment-select ul li label span {
  border: 2px solid var(--primary-color);
}

.payment-method-select {
  border: 3px solid var(--primary-color);
}

.payment-method-select:after {
  background: var(--primary-color);
}

.payment-method-select h5 span {
  color: var(--primary-color);
}

.pickupcar-price h4 {
  color: var(--primary-color);
}
