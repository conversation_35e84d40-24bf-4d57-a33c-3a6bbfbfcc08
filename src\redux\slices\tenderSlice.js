import { createSlice } from "@reduxjs/toolkit";

// LocalStorage'dan kayıtlı ihale parametrelerini al
const loadTenderParamsFromStorage = () => {
  try {
    const storedParams = localStorage.getItem("tenderParams");
    return storedParams ? JSON.parse(storedParams) : null;
  } catch (error) {
    console.error("Kayıtlı ihale parametreleri yüklenemedi:", error);
    return null;
  }
};

// Başlangıç durumu için hem localStorage hem de varsayılan değerleri kullan
const initialState = {
  tenderParams: loadTenderParamsFromStorage() || {},
  isActive: loadTenderParamsFromStorage() ? true : false,
  loading: false,
  error: null
};

const tenderSlice = createSlice({
  name: "tender",
  initialState,
  reducers: {
    // İhale parametrelerini kaydet
    setTenderParams: (state, action) => {
      state.tenderParams = action.payload;
      state.isActive = true;
      
      // LocalStorage'a kaydet
      try {
        localStorage.setItem("tenderParams", JSON.stringify(action.payload));
      } catch (error) {
        console.error("İhale parametreleri kaydedilemedi:", error);
      }
    },
    
    // İhale parametrelerini temizle
    clearTenderParams: (state) => {
      state.tenderParams = {};
      state.isActive = false;
      
      // LocalStorage'dan sil
      try {
        localStorage.removeItem("tenderParams");
      } catch (error) {
        console.error("İhale parametreleri silinemedi:", error);
      }
    },
    
    // İhale parametrelerini güncelle
    updateTenderParams: (state, action) => {
      state.tenderParams = { ...state.tenderParams, ...action.payload };
      
      // LocalStorage'a kaydet
      try {
        localStorage.setItem("tenderParams", JSON.stringify(state.tenderParams));
      } catch (error) {
        console.error("İhale parametreleri güncellenemedi:", error);
      }
    }
  }
});

export const { setTenderParams, clearTenderParams, updateTenderParams } = tenderSlice.actions;

export default tenderSlice.reducer; 