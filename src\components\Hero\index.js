import React from "react";
import { useTranslation } from "react-i18next";
import { Col, Container, Row } from "react-bootstrap";
import FindTender from "../FindTender";

import "./style.css";

const Hero = ({ title, description, buttonText }) => {
  const { t } = useTranslation();
  
  // Varsayılan değerler kullanılırken veya prop değerleri kullanılırken
  const heroTitle = title || t("hero.title");
  const heroDescription = description || t("hero.subtitle");
  const heroButtonText = (buttonText) || t("hero.buttonText");

  return (
    <section className="gauto-hero-area">
      <Container>
        <Row className="align-items-center">
          <Col lg={7} md={10} sm={12}>
            <div className="hero-content">
              <h1 className="hero-title">{heroTitle}</h1>
              <p className="hero-subtitle">{heroDescription}</p>
            </div>
          </Col>
        </Row>
        
        {/* FindCar Bileşeni - Container içinde */}
        <div className="hero-findcar-wrapper">
          <FindTender buttonText={heroButtonText} />
        </div>
      </Container>
    </section>
  );
};

export default Hero;
