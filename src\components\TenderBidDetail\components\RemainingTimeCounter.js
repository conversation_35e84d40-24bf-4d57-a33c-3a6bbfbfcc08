import React from 'react';
import { FaClock } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useRemainingTime from '../hooks/useRemainingTime';
import { formatRemainingTime } from '../utils/formatters';

/**
 * İ<PERSON> için kalan süreyi gösteren sayaç bileşeni
 * @param {object} props - Bileşen propsları
 * @param {string} props.endDate - İhale bitiş tarihi
 * @param {string} props.className - Ekstra CSS sınıfı
 */
const RemainingTimeCounter = ({ endDate, className = '' }) => {
  const { t } = useTranslation();
  const remainingTime = useRemainingTime(endDate);

  if (!remainingTime) return null;

  return (
    <div className={`modern-remaining-time ${className}`}>
      {remainingTime === "Süre doldu" || remainingTime === "Time expired" ? (
        <div className="time-expired">
          <FaClock className="expired-icon" />
          <span className="expired-text">{t('tender.time_expired', 'Süre doldu')}</span>
        </div>
      ) : (
        <div className="time-display">
          {formatRemainingTime(remainingTime).map((unit, index) => (
            <div className="time-unit-modern" key={index}>
              <div className="time-value-modern">{unit.value}</div>
              <div className="time-label-modern">{unit.label}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RemainingTimeCounter;