import React from 'react';
import { FaClock } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useRemainingTime from '../hooks/useRemainingTime';
import { formatRemainingTime } from '../utils/formatters';

/**
 * <PERSON><PERSON> için kalan süreyi gösteren sayaç bileşeni
 * @param {object} props - Bileşen propsları
 * @param {string} props.endDate - İhale bitiş tarihi
 * @param {string} props.className - Ekstra CSS sınıfı
 */
const RemainingTimeCounter = ({ endDate, className = '' }) => {
  const { t } = useTranslation();
  const remainingTime = useRemainingTime(endDate);

  if (!remainingTime) return null;

  return (
    <div className={`remaining-time ${className}`}>
      <div className="remaining-time-header">
        <h6>
          <FaClock />
          {t('tender.remaining_time', '<PERSON><PERSON>')}
        </h6>
      </div>
      <div className="remaining-time-body">
        {remainingTime === "Süre doldu" || remainingTime === "Time expired" ? (
          <div className="time-value text-danger">
            {t('tender.time_expired', 'Süre doldu')}
          </div>
        ) : (
          <>
            <div className="remaining-time-counter">
              {formatRemainingTime(remainingTime).map((unit, index) => (
                <div className="time-unit" key={index}>
                  <div className="time-value">{unit.value}</div>
                  <div className="time-label">{unit.label}</div>
                </div>
              ))}
            </div>
            <div className="remaining-time-text">
              {t('tender.remaining_time_desc', 'İhaleye teklif verebilmek için kalan süre')}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RemainingTimeCounter;