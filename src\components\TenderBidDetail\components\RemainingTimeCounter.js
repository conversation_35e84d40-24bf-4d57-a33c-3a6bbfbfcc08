import React from 'react';
import { FaClock } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import useRemainingTime from '../hooks/useRemainingTime';
import { formatRemainingTime } from '../utils/formatters';

/**
 * İhale için kalan süreyi gösteren sayaç bileşeni
 * @param {object} props - Bileşen propsları
 * @param {string} props.endDate - İhale bitiş tarihi
 * @param {string} props.className - Ekstra CSS sınıfı
 */
const RemainingTimeCounter = ({ endDate, className = '' }) => {
  const { t } = useTranslation();
  const remainingTime = useRemainingTime(endDate);
  
  if (!remainingTime) return null;
  
  return (
    <div className={`remaining-time ${className}`}>
      <div className="remaining-time-header">
        <h6>
          <FaClock />
          {t('tender.remaining_time', 'Ka<PERSON>re')}
        </h6>
      </div>
      <div className="remaining-time-body">
        {remainingTime === "Süre doldu" ? (
          <div className="time-value text-danger">
            {remainingTime}
          </div>
        ) : (
          <>
            <div className="remaining-time-counter">
              {formatRemainingTime(remainingTime).map((unit, index) => (
                <div className="time-unit" key={index}>
                  <div className="time-value">{unit.value}</div>
                  <div className="time-label">{unit.label}</div>
                </div>
              ))}
            </div>
            <div className="remaining-time-text">
              İhaleye teklif verebilmek için kalan süre
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default RemainingTimeCounter; 