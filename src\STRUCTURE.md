# LongTermRental Frontend Proje Yap<PERSON> belge, `src` klasöründeki dosya yapısını açıklamak ve kodun organizasyonunu net bir şekilde tanımlamak için hazırlanmıştır.

## Klas<PERSON>r Yapısı

```
src/
├── components/            # Yeniden kullanılabilir atomik bileşenler
│   ├── common/            # Genel kullanıma uygun ortak bileşenler  
│   ├── [ComponentName]/   # İlgili bileşenin tüm dosyaları (index.js, style.css vb.)
│
├── main-component/        # Sayfa seviyesindeki bileşenler
│   ├── [PageName]/        # İlgili sayfanın ana bileşeni
│
├── img/                   # Görsel dosyaları
├── constants/             # Sabit değerler ve yapılandırmalar
├── mockData/              # Geçici test verileri
├── redux/                 # Redux store ve slices
│   ├── slices/            # Redux slice dosyaları
│   ├── store/             # Redux store yapılandırması
│
├── routes/                # Rota yapılandırmaları
├── services/              # API ve servis katmanları
├── index.js               # Ana giriş noktası
└── index.css              # Global CSS
```

## Bileşen Organizasyonu

1. **components/**
   - Küçük, tekrar kullanılabilir bileşenler
   - Her bileşen, kendi klasöründe tanımlanır (index.js, style.css)
   - Başka komponentler tarafından içe aktarılabilir
   - Örneğin: Button, Card, Header, Footer, Form vb.

2. **main-component/**
   - Sayfa düzenindeki büyük bileşenler
   - Atomik bileşenleri birleştirerek tam sayfalar oluşturur
   - Her sayfa, kendi klasöründe tanımlanır
   - Örneğin: HomePage, AboutPage, LoginPage vb.

## İsimlendirme Kuralları

- Bileşen klasör isimleri PascalCase ile yazılır (örn. `UserProfile`, `LoginForm`)
- Dosya isimleri camelCase ile yazılır (örn. `userService.js`, `authUtils.js`)
- Stil dosyaları, ilgili bileşenle aynı klasörde `style.css` olarak adlandırılır

## İyi Uygulamalar

1. Bir bileşen, sadece bir sorumluluk üstlenmelidir
2. İlgili tüm dosyalar (bileşen, stil, test) aynı klasörde tutulmalıdır
3. components ve main-component arasındaki fark net olmalıdır:
   - components: Yeniden kullanılabilir, küçük, atomik bileşenler
   - main-component: Büyük, sayfa düzenindeki bileşenler

## Son Değişiklikler

Proje yapısı tek bir package.json ile merkezi olarak yönetilecek şekilde düzenlendi. WebUI klasörü kaldırılarak daha düz bir yapıya geçildi. Bu değişiklikler sayesinde proje yapısı daha anlaşılır ve bakımı daha kolay hale getirildi.

Bu yapıyı takip ederek, kod tekrarını azaltabilir ve projenin bakımını kolaylaştırabiliriz. 