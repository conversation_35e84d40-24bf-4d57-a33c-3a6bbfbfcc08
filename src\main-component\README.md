# Main-Component <PERSON><PERSON><PERSON><PERSON><PERSON>

<PERSON>, projede kull<PERSON>lan sayfa seviyesindeki bileşenleri içerir. Sayfaların ana yapılarını oluşturan ve atomik bileşenleri bir araya getiren daha büyük bileşenler burada bulunur.

## Kurallar ve Standartlar

1. Her sayfa kendi klasöründe olmalıdır:
   ```
   PageName/
   ├── index.js
   └── style.css (gerekirse)
   ```

2. Sayfa bileşenleri genellikle `components/` klasöründeki atomik bileşenleri kullanarak oluşturulmalıdır.

3. Sayfa bileşenleri aşağıdaki kategorilere ayrılır:
   - **Genel Sayfalar**: HomePage, AboutPage, ContactPage vb.
   - **Kullanıcı Rol Sayfaları**: 
     - admin/: Yönetici paneli sayfaları
     - customer/: Müşteri sayfaları
     - rental-company/: Kiralama şirketi sayfaları

## Mevcut Sorunlar ve Çözümler

Projede `main-component/` ve `components/` arasında bazı tekrarlanan yapılar bulunmaktadır.

### Öneriler:

1. Sayfa bileşenleri sadece `main-component/` klasöründe olmalıdır. Örneğin AboutPage, BlogPage vb.

2. `components/` klasöründeki sayfaları temsil eden bileşenler, yeniden adlandırılarak işlev karmaşasından kaçınılmalıdır:
   - `components/AboutPage` → `components/AboutContent` veya kaldırılmalı
   - `components/BlogPage` → `components/BlogContent` veya kaldırılmalı

3. Route yapılandırması tüm sayfa bileşenleri için `main-component/` klasöründeki bileşenleri referans almalıdır. 