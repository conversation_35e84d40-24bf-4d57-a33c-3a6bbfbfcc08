import React from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { FaMoneyBillWave } from 'react-icons/fa';
import './style.css';

/**
 * Teklif Ücretlendirme Bilgisi Bileşeni
 * Teklif verme ücretlerini ve paketleri gösterir
 */
const PricingInfo = () => {
  return (
    <section className="pricing-info-section">
      <Container>
        <Row>
          <Col>
            <Card className="pricing-card">
              <Card.Header>
                <h5 className="mb-0">
                  <FaMoneyBillWave className="me-2" />
                  Teklif Ücretlendirmesi
                </h5>
              </Card.Header>
              <Card.Body>
                <p className="mb-3">Her tedarikçi teklif verebilmek için araç sayısına, segmentine ve döneme göre bir teklif ödemesi yapar.</p>
                <div className="pricing-details mb-4">
                  <h6 className="pricing-section-title">Tek<PERSON><PERSON></h6>
                  <Row className="pricing-packages">
                    <Col md={4} className="mb-3">
                      <div className="pricing-package">
                        <h5>Başlangıç Paketi</h5>
                        <div className="price">₺1.000</div>
                        <ul className="package-features">
                          <li>Aylık 5 teklif hakkı</li>
                          <li>B ve C segment araçlar</li>
                          <li>Temel destek</li>
                        </ul>
                        <Button variant="outline-primary">Satın Al</Button>
                      </div>
                    </Col>
                    <Col md={4} className="mb-3">
                      <div className="pricing-package featured">
                        <div className="package-badge">Popüler</div>
                        <h5>Standart Paket</h5>
                        <div className="price">₺2.500</div>
                        <ul className="package-features">
                          <li>Aylık 15 teklif hakkı</li>
                          <li>Tüm segment araçlar</li>
                          <li>Öncelikli destek</li>
                          <li>İhale analizi</li>
                        </ul>
                        <Button variant="primary">Satın Al</Button>
                      </div>
                    </Col>
                    <Col md={4} className="mb-3">
                      <div className="pricing-package">
                        <h5>Premium Paket</h5>
                        <div className="price">₺5.000</div>
                        <ul className="package-features">
                          <li>Sınırsız teklif hakkı</li>
                          <li>Tüm segment araçlar</li>
                          <li>7/24 destek</li>
                          <li>İhale analizi</li>
                          <li>Özel danışmanlık</li>
                        </ul>
                        <Button variant="outline-primary">Satın Al</Button>
                      </div>
                    </Col>
                  </Row>
                </div>
                <p className="mb-0">Daha fazla bilgi için <a href="#" className="pricing-link">satış temsilcimize danışabilirsiniz</a>.</p>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default PricingInfo; 