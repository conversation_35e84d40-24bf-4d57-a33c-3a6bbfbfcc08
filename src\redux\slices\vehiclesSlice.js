import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { vehiclesService } from '../../services';

// Async thunk action creators
export const fetchVehicles = createAsyncThunk(
  'vehicles/fetchVehicles',
  async (filters = {}, { rejectWithValue }) => {
    try {
      // Vehicles servisini kullanarak araçları getir
      const response = await vehiclesService.getVehicles(filters);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Araçlar getirilemedi');
    }
  }
);

export const fetchVehicleById = createAsyncThunk(
  'vehicles/fetchVehicleById',
  async (id, { rejectWithValue }) => {
    try {
      // Vehicles servisini kullanarak belirli bir aracı getir
      const response = await vehiclesService.getVehicleById(id);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Araç detayları getirilemedi');
    }
  }
);

export const fetchVehicleCategories = createAsyncThunk(
  'vehicles/fetchCategories',
  async (_, { rejectWithValue }) => {
    try {
      // Vehicles servisini kullanarak kategorileri getir
      const response = await vehiclesService.getVehicleCategories();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Kategoriler getirilemedi');
    }
  }
);

export const fetchVehicleBrands = createAsyncThunk(
  'vehicles/fetchBrands',
  async (_, { rejectWithValue }) => {
    try {
      // Vehicles servisini kullanarak markaları getir
      const response = await vehiclesService.getVehicleBrands();
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Markalar getirilemedi');
    }
  }
);

export const fetchVehicleModelsByBrand = createAsyncThunk(
  'vehicles/fetchModelsByBrand',
  async (brand, { rejectWithValue }) => {
    try {
      // Vehicles servisini kullanarak belirli bir markaya ait modelleri getir
      const response = await vehiclesService.getVehicleModelsByBrand(brand);
      return { brand, models: response.data };
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Modeller getirilemedi');
    }
  }
);

// Initial State
const initialState = {
  vehicles: [],
  totalVehicles: 0,
  currentVehicle: null,
  categories: [],
  brands: [],
  modelsByBrand: {}, // { 'Toyota': [...], 'BMW': [...] }
  loading: false,
  error: null,
  success: false,
  filters: {
    brand: '',
    type: '',
    minPrice: '',
    maxPrice: '',
    fuelType: '',
    transmission: '',
    sort: 'price_asc',
    page: 1,
    limit: 10
  }
};

// Vehicle Slice
const vehiclesSlice = createSlice({
  name: 'vehicles',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = {
        ...state.filters,
        ...action.payload
      };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearVehicleDetails: (state) => {
      state.currentVehicle = null;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      // Araçları getirme
      .addCase(fetchVehicles.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicles.fulfilled, (state, action) => {
        state.loading = false;
        state.vehicles = action.payload.vehicles;
        state.totalVehicles = action.payload.total;
        state.success = true;
      })
      .addCase(fetchVehicles.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Araçlar getirilemedi';
        state.success = false;
      })

      // Belirli bir aracı getirme
      .addCase(fetchVehicleById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicleById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentVehicle = action.payload;
        state.success = true;
      })
      .addCase(fetchVehicleById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Araç detayları getirilemedi';
        state.success = false;
      })

      // Kategorileri getirme
      .addCase(fetchVehicleCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicleCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload;
        state.success = true;
      })
      .addCase(fetchVehicleCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Kategoriler getirilemedi';
        state.success = false;
      })

      // Markaları getirme
      .addCase(fetchVehicleBrands.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicleBrands.fulfilled, (state, action) => {
        state.loading = false;
        state.brands = action.payload;
        state.success = true;
      })
      .addCase(fetchVehicleBrands.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Markalar getirilemedi';
        state.success = false;
      })

      // Modelleri getirme
      .addCase(fetchVehicleModelsByBrand.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVehicleModelsByBrand.fulfilled, (state, action) => {
        state.loading = false;
        // Modelleri markaya göre depola
        state.modelsByBrand = {
          ...state.modelsByBrand,
          [action.payload.brand]: action.payload.models
        };
        state.success = true;
      })
      .addCase(fetchVehicleModelsByBrand.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Modeller getirilemedi';
        state.success = false;
      });
  }
});

export const { 
  setFilters, 
  clearFilters, 
  clearVehicleDetails, 
  clearError 
} = vehiclesSlice.actions;

export default vehiclesSlice.reducer; 