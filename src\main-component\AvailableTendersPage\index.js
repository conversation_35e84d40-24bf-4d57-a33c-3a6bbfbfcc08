import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Container, Button, Row, Col, Pagination } from "react-bootstrap";
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ort, <PERSON>a<PERSON>ye, FaGavel } from "react-icons/fa";

import PageTitle from "../../components/PageTitle";
import tenderService from "../../services/tenderService";
import "./style.css";

// const TENDER_STATUSES = [
//   { id: 0, label: "available_tenders_page.all" },
//   { id: 100, label: "available_tenders_page.draft" },
//   { id: 300, label: "available_tenders_page.live" },
//   { id: 400, label: "available_tenders_page.reject" },
//   { id: 500, label: "available_tenders_page.expired" },
//   { id: 600, label: "available_tenders_page.cancelled" },
//   { id: 700, label: "available_tenders_page.completed" },
// ];

const SORT_OPTIONS = [
  { value: "createddate", label: "available_tenders_page.sort_created_date" },
  { value: "startdate", label: "available_tenders_page.sort_start_date" },
  { value: "offercount", label: "available_tenders_page.sort_offer_count" },
];

const AvailableTendersPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  // State'ler
  const [tenders, setTenders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [statusId] = useState(300); // Sadece Live durumunda filtrele
  const [search, setSearch] = useState("");
  const [sort, setSort] = useState("createddate");
  const [isAscending, setIsAscending] = useState(true);

  useEffect(() => {
    setLoading(true);
    setError(null);
    tenderService.getAvailableTenders({
      pageNumber,
      pageSize,
      statusId,
      sort,
      isAscending,
      search: search || null
    })
      .then((response) => {
        if (response.data && response.data.result) {
          setTenders(response.data.result);
          setTotalCount(response.data.totalCount || 0);
        } else {
          setTenders([]);
          setTotalCount(0);
        }
      })
      .catch(() => {
        setError("İhaleler yüklenirken hata oluştu.");
        setTenders([]);
        setTotalCount(0);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [pageNumber, pageSize, statusId, sort, isAscending, search]);

  // Detay sayfasına yönlendirme
  const handleViewDetails = (tenderId) => {
    navigate('/tender-details', { state: { tenderId } });
  };

  // Teklif verme sayfasına yönlendirme
  const handleBid = (tenderId) => {
    navigate('/tender-bid', { state: { tenderId } });
  };

  return (
    <>
      <PageTitle
        pageTitle={t("available_tenders_page.title")}
        pagesub={t("available_tenders_page.subtitle")}
      />
      <div className="tenders-page-container">
        <Container>
          <Row className="align-items-center mb-3">
            <Col xs={12} md>
              <div className="filter-card shadow-sm p-3 mb-4 bg-white rounded-4">
                <Row className="gy-2 gx-3 align-items-center">
                  <Col xs={12} md={4}>
                    <label className="form-label mb-1">{t("available_tenders_page.search")}</label>
                    <div className="input-group">
                      <span className="input-group-text"><FaSearch /></span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder={t("available_tenders_page.search_placeholder")}
                        value={search}
                        onChange={(e) => {
                          setSearch(e.target.value);
                          setPageNumber(1);
                        }}
                      />
                    </div>
                  </Col>
                  {/*
                  <Col xs={6} md={2}>
                    <label className="form-label mb-1">{t("available_tenders_page.state_filter")}</label>
                    <select
                      className="form-select"
                      value={statusId}
                      onChange={(e) => {
                        const value = Number(e.target.value);
                        setStatusId(value === 0 ? 0 : value);
                        setPageNumber(1);
                      }}
                    >
                      {TENDER_STATUSES.map((status) => (
                        <option key={status.id} value={status.id}>
                          {t(status.label)}
                        </option>
                      ))}
                    </select>
                  </Col>
                  */}
                  <Col xs={6} md={4}>
                    <label className="form-label mb-1">{t("available_tenders_page.sort_by")}</label>
                    <select
                      className="form-select"
                      value={sort}
                      onChange={(e) => {
                        setSort(e.target.value);
                        setPageNumber(1);
                      }}
                    >
                      {SORT_OPTIONS.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                          {t(opt.label)}
                        </option>
                      ))}
                    </select>
                  </Col>
                  <Col xs={12} md={2}>
                    <label className="form-label mb-1 d-block">&nbsp;</label>
                    <Button
                      variant={isAscending ? "outline-secondary" : "outline-dark"}
                      className="sort-toggle-btn d-flex align-items-center gap-1 w-100"
                      onClick={() => setIsAscending((prev) => !prev)}
                      title={isAscending ? t("available_tenders_page.ascending") : t("available_tenders_page.descending")}
                      style={{ height: 38, minWidth: 60, padding: "0 6px" }}
                    >
                      <FaSort style={{ transform: isAscending ? "scaleY(1)" : "scaleY(-1)" }} />
                      <span style={{ fontSize: 13 }}>
                        {isAscending ? t("available_tenders_page.ascending") : t("available_tenders_page.descending")}
                      </span>
                    </Button>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
          {/* Listeleme Alanı */}
          {loading ? (
            <div className="tender-loading text-center py-5">
              <span className="spinner-border text-danger" role="status"></span>
            </div>
          ) : error ? (
            <div className="alert alert-danger" role="alert">
              {error}
            </div>
          ) : tenders.length === 0 ? (
            <div className="tender-empty text-center py-5">
              <h3>{t("available_tenders_page.empty_tenders")}</h3>
              <p>{t("available_tenders_page.empty_message")}</p>
            </div>
          ) : (
            <>
              <div className="advanced-list-container mb-4" style={{maxHeight: 500, overflowY: 'auto'}}>
                {tenders.map((tender) => (
                  <div
                    key={tender.id}
                    className="list-item d-flex flex-column flex-md-row align-items-start align-items-md-center gap-3 mb-3 p-3 bg-white rounded shadow-sm"
                  >
                    <img
                      src={tender.model?.picture || '/assets/no-image.png'}
                      alt={tender.model?.name || ''}
                      className="tender-img flex-shrink-0"
                      style={{ width: 90, height: 60, objectFit: 'cover', borderRadius: 8, background: '#f8f8f8' }}
                    />
                    <div className="flex-grow-1 w-100">
                      <div className="d-flex align-items-center gap-2 mb-1">
                        <span className="fw-bold">{tender.model?.brandName}</span>
                        <span className="text-secondary">{tender.model?.name}</span>
                        <span className="badge bg-light text-dark ms-2">{tender.tenderStatus?.statusName}</span>
                      </div>
                      <div className="text-muted small mb-1">
                        {t('available_tenders_page.start_date')}: {tender.startDate ? new Date(tender.startDate).toLocaleDateString('tr-TR') : '-'}
                        {" | "}
                        {t('available_tenders_page.end_date')}: {tender.endDate ? new Date(tender.endDate).toLocaleDateString('tr-TR') : '-'}
                      </div>
                      <div className="d-flex gap-3 flex-wrap">
                        <span className="badge bg-primary bg-opacity-10 text-white">
                          {t('available_tenders_page.vehicle_count')}: {tender.vehicleQuantity}
                        </span>
                        <span className="badge bg-success bg-opacity-10 text-success">
                          {t('available_tenders_page.offers')}: {tender.offerCount}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 mt-md-0 ms-md-3 w-100 w-md-auto d-flex justify-content-md-end gap-2">
                      {/*
                      <Button
                        variant="outline-primary"
                        className="detail-btn-responsive"
                        onClick={() => handleViewDetails(tender.id)}
                      >
                        <FaEye /> {t('available_tenders_page.details')}
                      </Button>
                      */}
                      <Button
                        variant="outline-success"
                        className="bid-btn-responsive"
                        onClick={() => handleBid(tender.id)}
                      >
                        <FaGavel /> {t('available_tenders_page.bid')}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              {/* Sayfalama */}
              <div className="pagination-wrapper d-flex justify-content-center align-items-center gap-2">
                <Pagination>
                  <Pagination.Prev
                    onClick={() => setPageNumber(pageNumber - 1)}
                    disabled={pageNumber === 1}
                  />
                  {Array.from({ length: Math.ceil(totalCount / pageSize) }, (_, i) => i + 1).map((page) => (
                    <Pagination.Item
                      key={page}
                      active={page === pageNumber}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </Pagination.Item>
                  ))}
                  <Pagination.Next
                    onClick={() => setPageNumber(pageNumber + 1)}
                    disabled={pageNumber === Math.ceil(totalCount / pageSize) || totalCount === 0}
                  />
                </Pagination>
                <div className="d-flex align-items-center ms-3">
                  <span className="me-2">{t("pagination.items_per_page")}:</span>
                  <select
                    className="form-select"
                    style={{ width: 70 }}
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value));
                      setPageNumber(1);
                    }}
                  >
                    {[5, 10, 20, 50, 100].map((size) => (
                      <option key={size} value={size}>{size}</option>
                    ))}
                  </select>
                </div>
              </div>
            </>
          )}
        </Container>
      </div>
    </>
  );
};

export default AvailableTendersPage; 