import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import Cart from "../../components/Cart";

const CartPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.shopping_cart")}
        pagesub={t("header-navigation.shopping_cart")}
      />
      <Cart />
    </Fragment>
  );
};
export default CartPage;
