.tenders-page-container {
    padding: 40px 0;
}

.tenders-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
}

.start-tender-btn {
    background-color: #e53e29;
    border-color: #e53e29;
    font-size: 14px;
    padding: 8px 15px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.start-tender-btn:hover {
    background-color: #d42e1e;
    border-color: #d42e1e;
}

.btn-icon {
    margin-right: 5px;
    font-size: 12px;
}

.sort-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    padding: 0;
    border-radius: 8px;
    font-size: 18px;
    transition: background 0.2s;
}

.filters-container {
    background: transparent;
    border: none;
    box-shadow: none;
    padding: 0 0 18px 0;
    margin-bottom: 18px;
    border-bottom: 1px solid #e9ecef;
}

.input-group-text {
    background: #f8f9fa;
    border-right: 0;
}

.form-control {
    border-left: 0;
}

.filter-card {
    border-radius: 18px;
    background: #fff;
    box-shadow: 0 2px 16px rgba(0,0,0,0.06);
    border: 1px solid #f0f0f0;
}

.detail-btn-responsive {
    width: 100%;
}

@media (min-width: 768px) {
    .detail-btn-responsive {
        width: auto !important;
        min-width: 140px;
        max-width: 220px;
    }
}

@media (max-width: 768px) {
    .filters-container {
        padding: 12px 6px 6px 6px;
    }
    .sort-toggle-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
    .start-tender-btn {
        width: 100% !important;
        margin-bottom: 12px;
    }
    .filter-card {
        margin-bottom: 18px !important;
        padding: 10px 4px 4px 4px;
    }
    .list-item {
        padding: 10px 6px !important;
        gap: 10px !important;
    }
    .tender-img {
        width: 100% !important;
        max-width: 100%;
        height: 48vw !important;
        max-height: 120px;
        margin-bottom: 8px;
    }
} 