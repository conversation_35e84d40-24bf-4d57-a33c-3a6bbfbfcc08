# Node.js te<PERSON><PERSON>ok-aşamalı bir yapı oluşturuyoruz
# Derleme aşaması
FROM node:18-alpine AS build

# Çalışma dizinini ayarlıyoruz
WORKDIR /app

# Bağımlılık dosyalarını kopyalıyoruz
COPY package.json package-lock.json ./

# Bağımlılıkları yüklüyoruz
RUN npm ci

# Uygulama kaynak kodlarını kopyalıyoruz
COPY . .

# Uygulamayı build ediyoruz
RUN npm run build

# İsteğe bağlı: _redirects dosyasını build klasörüne kopyalama
RUN if [ -f "public/_redirects" ]; then cp public/_redirects build/; fi

# İsteğe bağlı: web.config dosyasını build klasörüne kopyalama
RUN if [ -f "public/web.config" ]; then cp public/web.config build/; fi

# Çalıştırma aşaması
FROM nginx:stable-alpine AS production

# İnşa edilen dosyaları nginx'in sunduğu klasöre kopyalıyoruz
COPY --from=build /app/build /usr/share/nginx/html

# Nginx yapılandırmasını kopyalıyoruz
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 80 portunu açıyoruz
EXPOSE 80

# Nginx'i başlatıyoruz
CMD ["nginx", "-g", "daemon off;"] 