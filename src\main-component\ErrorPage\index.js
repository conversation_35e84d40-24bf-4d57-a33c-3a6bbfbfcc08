import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import Error from "../../components/Error";

const ErrorPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle pageTitle={t("error_page.error")} pagesub={t("error_page.error")} />
      <Error />
    </Fragment>
  );
};
export default ErrorPage;
