.my-bookings-area {
  padding: 70px 0;
  background-color: #f9f9f9;
}

.booking-card {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  border: none;
}

.booking-table {
  margin-bottom: 0;
}

.booking-table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #333;
  padding: 15px;
}

.booking-row {
  transition: all 0.3s ease;
}

.booking-row:hover {
  background-color: var(--primary-transparent);
}

.booking-row.cancelled {
  background-color: rgba(220, 53, 69, 0.05);
}

.booking-row.completed {
  background-color: rgba(13, 110, 253, 0.05);
}

.booking-row td {
  padding: 15px;
  vertical-align: middle;
}

.car-name, .booking-date, .booking-route, .booking-amount {
  display: flex;
  align-items: center;
  gap: 10px;
}

.car-name svg, .booking-date svg, .booking-route svg, .booking-amount svg {
  color: var(--primary-color);
  font-size: 16px;
}

.booking-actions {
  display: flex;
  gap: 8px;
}

.booking-actions button {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.no-bookings {
  text-align: center;
  padding: 50px 20px;
}

.no-bookings h3 {
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

.no-bookings p {
  margin-bottom: 20px;
  color: #666;
}

@media (max-width: 767px) {
  .booking-route span, .booking-date span {
    max-width: 150px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.dashboard-header-item:hover {
  background-color: var(--primary-transparent);
  border-color: var(--primary-color);
} 