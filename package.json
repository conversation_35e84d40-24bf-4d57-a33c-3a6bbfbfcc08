{"name": "long-term-rental-frontend", "version": "1.0.0", "description": "Long Term Rental Frontend Application", "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build && node scripts/generate-sitemap.js", "test": "react-scripts test", "eject": "react-scripts eject", "generate-sitemap": "node src/components/common/SEOMeta/sitemap-generator.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@keyvaluesystems/react-stepper": "^1.0.4", "@mui/material": "^7.0.2", "@reduxjs/toolkit": "^2.6.1", "@syncfusion/ej2-react-calendars": "^20.2.40", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.3", "bootstrap": "^5.2.0", "flag-icon-css": "^4.1.5", "i18next": "^21.8.16", "i18next-browser-languagedetector": "^6.1.4", "i18next-http-backend": "^1.4.1", "react": "^18.2.0", "react-archer": "^4.4.0", "react-bootstrap": "^2.4.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-i18next": "^11.18.3", "react-icons": "^4.4.0", "react-redux": "^9.2.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-slick": "^0.29.0", "react-vertical-timeline-component": "^3.5.3", "recharts": "^2.12.3", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "slick-carousel": "^1.8.1", "web-vitals": "^2.1.4"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}