import React from "react";
import { Link } from "react-router-dom";
import {
  DatePickerComponent,
  TimePickerComponent,
} from "@syncfusion/ej2-react-calendars";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";
import {
  FaStar,
  FaStarHalfAlt,
  FaCar,
  FaCogs,
  FaTachometerAlt,
  FaEmpire,
  FaDesktop,
  FaKey,
  FaLock,
  FaEye,
} from "react-icons/fa";

import img1 from "../../img/booking.jpg";
import img2 from "../../img/master-card.jpg";
import img3 from "../../img/paypal.jpg";

import "./style.css";

const CarBooking = () => {
  const { t } = useTranslation();

  const handleSubmit = (e) => {
    e.preventDefault();
  };

  const handleClick = (e) => {
    e.preventDefault();
  };

  return (
    <>
      <section className="gauto-car-booking section_70">
        <Container>
          <Row>
            <Col lg={6}>
              <div className="car-booking-image">
                <img src={img1} alt="car" />
              </div>
            </Col>
            <Col lg={6}>
              <div className="car-booking-right">
                <p className="rental-tag">{t("rental")}</p>
                <h3>{t("car_booking.car_model")}</h3>
                <div className="price-rating">
                  <div className="price-rent">
                    <h4>
                      {t("car_booking.car_price")}<span>/ {t("day")}</span>
                    </h4>
                  </div>
                  <div className="car-rating">
                    <ul>
                      <li>
                        <FaStar />
                      </li>
                      <li>
                        <FaStar />
                      </li>
                      <li>
                        <FaStar />
                      </li>
                      <li>
                        <FaStar />
                      </li>
                      <li>
                        <FaStarHalfAlt />
                      </li>
                    </ul>
                    <p>(123 {t("rating")})</p>
                  </div>
                </div>
                <p>
                  {t("car_booking.car_description")}
                </p>
                <div className="car-features clearfix">
                  <ul>
                    <li>
                      <FaCar /> {t("model")}:2017
                    </li>
                    <li>
                      <FaCogs /> {t("automatic")}
                    </li>
                    <li>
                      <FaTachometerAlt /> {t("car_booking.fuel_consumption")}
                    </li>
                    <li>
                      <FaEmpire /> {t("car_booking.engine_type")}
                    </li>
                  </ul>
                  <ul>
                    <li>
                      <FaEye /> {t("car_booking.gps_navigation")}
                    </li>
                    <li>
                      <FaLock /> {t("car_booking.anti_lock_brakes")}
                    </li>
                    <li>
                      <FaKey /> {t("car_booking.remote_keyless")}
                    </li>
                    <li>
                      <FaDesktop /> {t("car_booking.rear_seat_entertainment")}
                    </li>
                  </ul>
                  <ul>
                    <li>
                      <FaCar /> {t("model")}:2017
                    </li>
                    <li>
                      <FaCogs /> {t("automatic")}
                    </li>
                    <li>
                      <FaTachometerAlt /> {t("car_booking.fuel_consumption")}
                    </li>
                    <li>
                      <FaEmpire /> {t("car_booking.engine_type")}
                    </li>
                  </ul>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
      <section className="gauto-booking-form section_70">
        <Container>
          <Row>
            <Col lg={8}>
              <div className="booking-form-left">
                <div className="single-booking">
                  <h3>{t("car_booking.personal_information")}</h3>
                  <form onSubmit={handleSubmit}>
                    <Row>
                      <Col md={6}>
                        <p>
                          <input
                            type="text"
                            placeholder={t("car_booking.first_name")}
                          />
                        </p>
                      </Col>
                      <Col md={6}>
                        <p>
                          <input
                            type="text"
                            placeholder={t("car_booking.last_name")}
                          />
                        </p>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={6}>
                        <p>
                          <input
                            type="email"
                            placeholder={t("car_booking.email")}
                          />
                        </p>
                      </Col>
                      <Col md={6}>
                        <p>
                          <input
                            type="tel"
                            placeholder={t("car_booking.phn")}
                          />
                        </p>
                      </Col>
                    </Row>
                  </form>
                </div>
                <div className="single-booking">
                  <h3>{t("car_booking.booking_details")}</h3>
                  <form>
                    <Row>
                      <Col md={6}>
                        <p>
                          <input type="text" placeholder={t("from_address")} />
                        </p>
                      </Col>
                      <Col md={6}>
                        <p>
                          <input type="text" placeholder={t("to_address")} />
                        </p>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={6}>
                        <p>
                          <select>
                            <option data-display="Select">{t("car_booking.person_options.option1")}</option>
                            <option>{t("car_booking.person_options.option2")}</option>
                            <option>{t("car_booking.person_options.option3")}</option>
                            <option>{t("car_booking.person_options.option4")}</option>
                            <option>{t("car_booking.person_options.option5")}</option>
                          </select>
                        </p>
                      </Col>
                      <Col md={6}>
                        <p>
                          <select>
                            <option data-display="Select">{t("car_booking.luggage_options.option1")}</option>
                            <option>{t("car_booking.luggage_options.option2")}</option>
                            <option>{t("car_booking.luggage_options.option3")}</option>
                            <option>{t("car_booking.luggage_options.option4")}</option>
                          </select>
                        </p>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={6}>
                        <p>
                          <DatePickerComponent
                            id="datepicker"
                            placeholder={t("journey_date")}
                          ></DatePickerComponent>
                        </p>
                      </Col>
                      <Col md={6}>
                        <p>
                          <TimePickerComponent
                            id="timepicker"
                            placeholder={t("journey_time")}
                          ></TimePickerComponent>
                        </p>
                      </Col>
                    </Row>
                    <Row>
                      <Col md={12}>
                        <p>
                          <textarea
                            placeholder={t("car_booking.write_here")}
                            defaultValue={""}
                          />
                        </p>
                      </Col>
                    </Row>
                  </form>
                </div>
              </div>
            </Col>
            <Col lg={4}>
              <div className="booking-right">
                <h3>{t("car_booking.payment_method")}</h3>
                <div className="gauto-payment clearfix">
                  <div className="payment">
                    <input type="radio" id="ss-option" name="selector" />
                    <label htmlFor="ss-option">
                      {t("car_booking.bank_transfer")}
                    </label>
                    <div className="check">
                      <div className="inside" />
                    </div>
                    <p>{t("car_booking.payment_text")}</p>
                  </div>
                  <div className="payment">
                    <input type="radio" id="f-option" name="selector" />
                    <label htmlFor="f-option">
                      {t("car_booking.check_payment")}
                    </label>
                    <div className="check">
                      <div className="inside" />
                    </div>
                  </div>
                  <div className="payment">
                    <input type="radio" id="s-option" name="selector" />
                    <label htmlFor="s-option">
                      {t("car_booking.credit_card")}
                    </label>
                    <div className="check">
                      <div className="inside" />
                    </div>
                    <div className="credit-card-box">
                      <input
                        type="text"
                        className="form-control"
                        name="name"
                        placeholder="Card Number"
                      />
                      <div className="input-group">
                        <input
                          type="text"
                          name="name"
                          className="form-control"
                          placeholder="MM/YYYY"
                        />
                        <input
                          type="text"
                          name="name"
                          className="form-control"
                          placeholder="Security code"
                        />
                      </div>
                      <div className="payment-img">
                        <img src={img2} alt="payment" />
                        <img src={img3} alt="payment" />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="action-btn">
                  <Link to="/" onClick={handleClick} className="gauto-btn">
                    {t("researve_now")}
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </>
  );
};

export default CarBooking;
