import React from "react";
import { Container, <PERSON>, Col } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import { FaMoneyBillWave, FaCog, FaLaptop, FaPercent, FaCar, FaHandshake } from "react-icons/fa";

import "./style.css";

const Advantages = () => {
  const { t } = useTranslation();

  return (
    <section className="advantages-area section_70">
      <Container>
        <Row>
          <Col md={12}>
            <div className="site-heading">
              <h4>{t("promo.subtitle")}</h4>
              <h2>{t("promo.title")}</h2>
            </div>
          </Col>
        </Row>

        <Row className="mt-5">
          <Col lg={4} md={6} sm={12}>
            <div className="single-advantage-box">
              <div className="advantage-box-icon">
                <FaMoneyBillWave />
              </div>
              <div className="advantage-box-text">
                <h3>{t("promo.advantage_1_title")}</h3>
                <p>{t("promo.advantage_1_text")}</p>
              </div>
            </div>
          </Col>

          <Col lg={4} md={6} sm={12}>
            <div className="single-advantage-box">
              <div className="advantage-box-icon">
                <FaCog />
              </div>
              <div className="advantage-box-text">
                <h3>{t("promo.advantage_2_title")}</h3>
                <p>{t("promo.advantage_2_text")}</p>
              </div>
            </div>
          </Col>

          <Col lg={4} md={6} sm={12}>
            <div className="single-advantage-box">
              <div className="advantage-box-icon">
                <FaLaptop />
              </div>
              <div className="advantage-box-text">
                <h3>{t("promo.advantage_3_title")}</h3>
                <p>{t("promo.advantage_3_text")}</p>
              </div>
            </div>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg={4} md={6} sm={12}>
            <div className="single-advantage-box">
              <div className="advantage-box-icon">
                <FaPercent />
              </div>
              <div className="advantage-box-text">
                <h3>{t("promo.advantage_4_title")}</h3>
                <p>{t("promo.advantage_4_text")}</p>
              </div>
            </div>
          </Col>

          <Col lg={4} md={6} sm={12}>
            <div className="single-advantage-box">
              <div className="advantage-box-icon">
                <FaCar />
              </div>
              <div className="advantage-box-text">
                <h3>{t("promo.advantage_5_title")}</h3>
                <p>{t("promo.advantage_5_text")}</p>
              </div>
            </div>
          </Col>

          <Col lg={4} md={6} sm={12}>
            <div className="single-advantage-box">
              <div className="advantage-box-icon">
                <FaHandshake />
              </div>
              <div className="advantage-box-text">
                <h3>{t("promo.advantage_6_title")}</h3>
                <p>{t("promo.advantage_6_text")}</p>
              </div>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default Advantages; 