# Mevcut Tema Bileşenleri Kullanımı

Bu projede, DRY (Don't Repeat Yourself) prensiplerine uygun olarak hareket etmek için mevcut tema bileşenlerini kullanıyoruz. Custom (özel) bileşenler oluşturmak yerine, temanın kendi bileşenlerini tercih ediyoruz.

## Kullanılan Mevcut Tema Bileşenleri

### 1. Butonlar

Buttonlar için tema içerisinde tanımlanan `.gauto-btn` veya `.gauto-theme-btn` sınıflarını kullanıyoruz.

```jsx
<button type="submit" className="gauto-theme-btn">
  {t("register_page.register_now")}
</button>

<Link to="/" className="gauto-btn">
  {t("researve_now")}
</Link>
```

### 2. Form Alanları

Form alanları için, tema içerisindeki standart form yapılarını kullanıyoruz.

```jsx
<div className="account-form-group">
  <input
    type="email"
    name="email"
    value={formData.email}
    onChange={handleChange}
    placeholder={t("login_page.user_email")}
    required
  />
  <FaUser />
</div>
```

### 3. Sosyal Medya Linkleri

Sosyal medya bağlantıları için temanın sağladığı yapıyı kullanıyoruz.

```jsx
<div className="social-links-contact">
  <h4>{t("contact_page.info_follow")}</h4>
  <ul>
    <li>
      <Link to="/" onClick={handleSocialLinkClick}>
        <FaFacebook />
      </Link>
    </li>
    <li>
      <Link to="/" onClick={handleSocialLinkClick}>
        <FaTwitter />
      </Link>
    </li>
    <!-- Diğer sosyal medya bağlantıları -->
  </ul>
</div>
```

### 4. Yıldız Derecelendirme

Yıldız derecelendirme için temanın mevcut yapısını kullanıyoruz.

```jsx
<div className="car-rating">
  <ul>
    <li><FaStar /></li>
    <li><FaStar /></li>
    <li><FaStar /></li>
    <li><FaStar /></li>
    <li><FaStarHalfAlt /></li>
  </ul>
  <p>(123 {t("rating")})</p>
</div>
```

### 5. Bölüm Başlıkları

Bölüm başlıkları için temanın mevcut yapısını kullanıyoruz.

```jsx
<div className="site-heading">
  <h4>{t("see_our")}</h4>
  <h2>{t("latest_service")}</h2>
</div>
```

## Geliştirme Yaklaşımı

1. Her zaman mevcut tema bileşenlerini kullanmaya öncelik verin
2. Yeni CSS dosyaları oluşturmayın, mevcut CSS sınıflarını kullanın
3. Tema stillerini koruyun ve tutarlılığı sağlayın
4. Gereksiz kod tekrarından kaçının ama temanın bileşenlerini kullanın

Bu yaklaşım sayesinde:
- Tema tutarlılığı korunur
- Bakım daha kolay hale gelir
- Kod karmaşıklığı azalır
- Gereksiz CSS yükü önlenir 