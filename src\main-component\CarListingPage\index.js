import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import PageTitle from "../../components/PageTitle";
import CarList from "../../components/CarList";

const CarListingPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle pageTitle={t("header-navigation.car_listing")} pagesub={t("header-navigation.car_listing")} />
      <CarList />
    </Fragment>
  );
};
export default CarListingPage;
