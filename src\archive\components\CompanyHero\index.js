import React from "react";
import { useTranslation } from "react-i18next";
import { Col, Container, Row } from "react-bootstrap";
import "./style.css";

/**
 * Şirket/Tedarikçi kullanıcıları için özel hero bileşeni
 * Bu bileşen sadece karşılama metni gö<PERSON>ir, herhangi bir buton/form içermez
 */
const CompanyHero = ({ title, description }) => {
  const { t } = useTranslation();
  
  // Varsayılan değerler ya da prop değerleri kullanılır
  const heroTitle = title || t("hero.company.title");
  const heroDescription = description || t("hero.company.subtitle");

  return (
    <section className="company-hero-area">
      <Container>
        <Row className="align-items-center">
          <Col lg={10} md={12} sm={12}>
            <div className="hero-content">
              <h1 className="hero-title">{heroTitle}</h1>
              <p className="hero-subtitle">{heroDescription}</p>
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default CompanyHero; 