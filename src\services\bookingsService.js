import api, { useMockAPI } from './api';
import { USER_ROLES } from '../constants/userRoles';

// ----- Gerçek API Çağrıları ----- //

// Yeni rezervasyon oluşturma
const createBooking = (bookingData) => api.post('/bookings', bookingData);

// Kullanıcının rezervasyonlarını getirme
const getUserBookings = () => api.get('/bookings');

// Belirli bir rezervasyonun detaylarını getirme
const getBookingById = (id) => api.get(`/bookings/${id}`);

// Rezervasyon iptal etme
const cancelBooking = (id) => api.delete(`/bookings/${id}`);

// Rezervasyon durumunu güncelleme (örneğin ödeme durumu)
const updateBookingStatus = (id, statusData) => api.put(`/bookings/${id}/status`, statusData);

// ----- Mock API Çağrıları ----- //

// Mock rezervasyon verileri
let mockBookings = [
  {
    id: 1,
    userId: 1,
    vehicleId: 1,
    vehicleDetails: {
      brand: 'Toyota',
      model: 'Alphard',
      year: 2022,
      image: '/assets/img/offer-toyota.png',
    },
    startDate: '2023-05-01',
    endDate: '2024-04-30',
    totalPrice: 24000,
    priceUnit: 'TL',
    status: 'ACTIVE', // ACTIVE, COMPLETED, CANCELLED
    paymentStatus: 'PAID', // PENDING, PAID, FAILED
    paymentMethod: 'CREDIT_CARD',
    createdAt: '2023-04-15T10:30:00Z',
    updatedAt: '2023-04-15T10:30:00Z'
  },
  {
    id: 2,
    userId: 1,
    vehicleId: 3,
    vehicleDetails: {
      brand: 'Audi',
      model: 'Q3',
      year: 2022,
      image: '/assets/img/audi-offer.png',
    },
    startDate: '2023-06-01',
    endDate: '2023-12-31',
    totalPrice: 12500,
    priceUnit: 'TL',
    status: 'CANCELLED',
    paymentStatus: 'REFUNDED',
    paymentMethod: 'CREDIT_CARD',
    createdAt: '2023-05-20T14:45:00Z',
    updatedAt: '2023-05-25T09:15:00Z'
  },
  {
    id: 3,
    userId: 2,
    vehicleId: 2,
    vehicleDetails: {
      brand: 'Nissan',
      model: '370Z',
      year: 2021,
      image: '/assets/img/nissan-offer.png',
    },
    startDate: '2023-07-01',
    endDate: '2023-12-31',
    totalPrice: 18000,
    priceUnit: 'TL',
    status: 'ACTIVE',
    paymentStatus: 'PAID',
    paymentMethod: 'BANK_TRANSFER',
    createdAt: '2023-06-15T11:20:00Z',
    updatedAt: '2023-06-15T11:20:00Z'
  }
];

// İlerleyen zamanda kullanılmak üzere otomatik ID oluşturma
let nextBookingId = mockBookings.length + 1;

// Mock yeni rezervasyon oluşturma
const mockCreateBooking = (bookingData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Temel doğrulama kontrolleri
        if (!bookingData.vehicleId || !bookingData.startDate || !bookingData.endDate) {
          return reject({
            response: { data: { message: 'Eksik rezervasyon bilgileri' } }
          });
        }

        // Kullanıcı ID'sini localStorage'dan al (gerçek uygulamada bu Redux store'dan gelebilir)
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Yeni rezervasyon oluşturma
        const newBooking = {
          id: nextBookingId++,
          userId: user.id,
          vehicleId: bookingData.vehicleId,
          vehicleDetails: bookingData.vehicleDetails || {
            // Örnek bir araç detayı (gerçek uygulamada bu vehiclesService'den gelebilir)
            brand: 'Örnek Marka',
            model: 'Örnek Model',
            year: 2023,
            image: '/assets/img/default-car.png'
          },
          startDate: bookingData.startDate,
          endDate: bookingData.endDate,
          totalPrice: bookingData.totalPrice || 9999,
          priceUnit: bookingData.priceUnit || 'TL',
          status: 'ACTIVE',
          paymentStatus: bookingData.paymentStatus || 'PENDING',
          paymentMethod: bookingData.paymentMethod || 'CREDIT_CARD',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // Rezervasyonu mock veritabanına ekle
        mockBookings.push(newBooking);

        resolve({ data: newBooking });
      } catch (error) {
        reject({
          response: { data: { message: 'Rezervasyon oluşturulurken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock kullanıcının rezervasyonlarını getirme
const mockGetUserBookings = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Kullanıcı ID'sini localStorage'dan al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Kullanıcının rezervasyonlarını filtrele
        const userBookings = mockBookings.filter(booking => booking.userId === user.id);

        resolve({
          data: {
            bookings: userBookings,
            total: userBookings.length
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Rezervasyonlar getirilirken bir hata oluştu' } }
        });
      }
    }, 700);
  });
};

// Mock belirli bir rezervasyonun detaylarını getirme
const mockGetBookingById = (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // ID'ye göre rezervasyonu bul
        const booking = mockBookings.find(b => b.id === parseInt(id));

        if (!booking) {
          return reject({
            response: { data: { message: 'Rezervasyon bulunamadı' } }
          });
        }

        // Kullanıcı ID'sini localStorage'dan al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Kullanıcının kendi rezervasyonuna eriştiğini kontrol et
        if (booking.userId !== user.id && user.roleId !== USER_ROLES.ADMIN.id && user.roleId !== USER_ROLES.CUSTOMER.id) {
          return reject({
            response: { data: { message: 'Bu rezervasyona erişim izniniz yok' } }
          });
        }

        resolve({ data: booking });
      } catch (error) {
        reject({
          response: { data: { message: 'Rezervasyon detayları getirilirken bir hata oluştu' } }
        });
      }
    }, 600);
  });
};

// Mock rezervasyon iptal etme
const mockCancelBooking = (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // ID'ye göre rezervasyonu bul
        const bookingIndex = mockBookings.findIndex(b => b.id === parseInt(id));

        if (bookingIndex === -1) {
          return reject({
            response: { data: { message: 'Rezervasyon bulunamadı' } }
          });
        }

        // Kullanıcı ID'sini localStorage'dan al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Kullanıcının kendi rezervasyonunu iptal ettiğini kontrol et
        if (mockBookings[bookingIndex].userId !== user.id && user.roleId !== USER_ROLES.ADMIN.id) {
          return reject({
            response: { data: { message: 'Bu rezervasyonu iptal etme izniniz yok' } }
          });
        }

        // Rezervasyonu iptal et
        mockBookings[bookingIndex].status = 'CANCELLED';
        mockBookings[bookingIndex].paymentStatus = 'REFUNDED';
        mockBookings[bookingIndex].updatedAt = new Date().toISOString();

        resolve({
          data: {
            success: true,
            booking: mockBookings[bookingIndex]
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Rezervasyon iptal edilirken bir hata oluştu' } }
        });
      }
    }, 800);
  });
};

// Mock rezervasyon durumunu güncelleme
const mockUpdateBookingStatus = (id, statusData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // ID'ye göre rezervasyonu bul
        const bookingIndex = mockBookings.findIndex(b => b.id === parseInt(id));

        if (bookingIndex === -1) {
          return reject({
            response: { data: { message: 'Rezervasyon bulunamadı' } }
          });
        }

        // Kullanıcı ID'sini localStorage'dan al
        const userJson = localStorage.getItem('user') || sessionStorage.getItem('user');
        if (!userJson) {
          return reject({
            response: { data: { message: 'Kullanıcı oturumu bulunamadı' } }
          });
        }

        const user = JSON.parse(userJson);

        // Sadece admin veya şirketin durumu güncelleyebileceğini kontrol et
        if (user.roleId !== USER_ROLES.ADMIN.id && user.roleId !== USER_ROLES.CUSTOMER.id) {
          return reject({
            response: { data: { message: 'Rezervasyon durumunu güncelleme izniniz yok' } }
          });
        }

        // Rezervasyon durumunu güncelle
        if (statusData.status) {
          mockBookings[bookingIndex].status = statusData.status;
        }

        if (statusData.paymentStatus) {
          mockBookings[bookingIndex].paymentStatus = statusData.paymentStatus;
        }

        mockBookings[bookingIndex].updatedAt = new Date().toISOString();

        resolve({
          data: {
            success: true,
            booking: mockBookings[bookingIndex]
          }
        });
      } catch (error) {
        reject({
          response: { data: { message: 'Rezervasyon durumu güncellenirken bir hata oluştu' } }
        });
      }
    }, 700);
  });
};

// Kullanılacak servisleri belirleme (mock mu gerçek mi)
const bookingsService = {
  createBooking: useMockAPI ? mockCreateBooking : createBooking,
  getUserBookings: useMockAPI ? mockGetUserBookings : getUserBookings,
  getBookingById: useMockAPI ? mockGetBookingById : getBookingById,
  cancelBooking: useMockAPI ? mockCancelBooking : cancelBooking,
  updateBookingStatus: useMockAPI ? mockUpdateBookingStatus : updateBookingStatus,
};

export default bookingsService;