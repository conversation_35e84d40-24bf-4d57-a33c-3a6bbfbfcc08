@import '../../styles/colors.css';

/* <PERSON><PERSON><PERSON> ve responsive ba<PERSON>lıklar */
.page-title, .section-title, .tender-bid-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 18px;
  letter-spacing: 0.01em;
  line-height: 1.2;
  position: relative;
  background: none;
  border: none;
  padding-bottom: 6px;
}
.page-title::after, .section-title::after, .tender-bid-title::after {
  content: "";
  display: block;
  width: 36px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 60%, var(--primary-light) 100%);
  border-radius: 2px;
  margin-top: 6px;
  opacity: 0.18;
}

.page-subtitle, .section-subtitle {
  font-size: 15px;
  color: var(--gray-500);
  font-weight: 400;
  margin-bottom: 18px;
  margin-top: -10px;
  letter-spacing: 0.01em;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> */
.tender-bid-page-container {
  background: #f7f7f7;
  min-height: 80vh;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tender-detail-card {
  border: none;
  border-radius: 14px;
  box-shadow: 0 2px 12px var(--primary-shadow);
  padding: 32px 32px 24px 32px;
  margin-bottom: 32px;
  overflow: hidden;
  background: #fff;
  max-width: 900px;
  width: 100%;
  border: 1px solid var(--gray-200);
}

.tender-detail-header {
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  padding: 12px 18px;
  font-weight: 600;
  color: var(--gray-900);
  font-family: "Poppins", sans-serif;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tender-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}
.tender-brand {
  color: var(--primary-color);
}
.tender-model {
  color: var(--secondary-color);
}
.tender-status-badge {
  font-size: 10px;
  padding: 4px 10px;
  border-radius: 16px;
  font-weight: 500;
  text-transform: uppercase;
  background: var(--soft-success) !important;
  color: var(--success) !important;
}

.tender-info-compact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 6px 18px;
  margin-bottom: 6px;
  font-size: 13px;
}
@media (max-width: 900px) {
  .tender-info-compact-grid {
    grid-template-columns: 1fr 1fr;
  }
}
@media (max-width: 700px) {
  .tender-info-compact-grid {
    grid-template-columns: 1fr;
  }
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px dashed var(--gray-100);
}
.detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}
.detail-icon {
  color: var(--primary-color);
  font-size: 15px;
  min-width: 15px;
  margin-top: 2px;
  opacity: 0.8;
}
.detail-content {
  display: flex;
  flex-direction: column;
}
.detail-label {
  color: var(--gray-600);
  font-size: 12px;
  margin-bottom: 2px;
}
.detail-value {
  color: var(--gray-900);
  font-weight: 500;
  font-size: 13px;
}

.tender-bid-form-card {
  border: none;
  border-radius: 14px;
  box-shadow: 0 2px 12px var(--primary-shadow);
  padding: 32px 32px 24px 32px;
  margin-bottom: 32px;
  overflow: hidden;
  background: #fff;
  max-width: 900px;
  width: 100%;
  border: 1px solid var(--gray-200);
}
.form-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--primary-color);
}

.bid-row {
  background: var(--gray-100);
  border-radius: 6px;
  padding: 8px 4px 4px 4px;
  margin-bottom: 7px;
  box-shadow: 0 1px 2px 0 var(--primary-shadow);
}
.add-bid-btn {
  font-weight: 500;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 13px;
}
.remove-bid-btn {
  border-radius: 50%;
  width: 26px;
  height: 26px;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
}
.submit-bids-btn {
  font-weight: 600;
  border-radius: 6px;
  padding: 6px 18px;
  font-size: 14px;
}

/* Form input ve label stilleri */
.form-label {
  font-weight: 500;
  color: var(--gray-700);
  font-size: 12px;
  margin-bottom: 2px;
}
.form-control, .form-select {
  border-radius: 6px;
  border: 1px solid var(--gray-300);
  font-size: 13px;
  padding: 6px 8px;
  height: 32px;
}
.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.08rem var(--primary-transparent);
}

/* Alert ve başarı mesajı */
.alert-success {
  background: var(--soft-success);
  color: var(--success);
  border-radius: 6px;
  font-weight: 500;
  font-size: 13px;
  padding: 8px 12px;
}

/* Responsive başlık ve kutu ayarları */
@media (max-width: 1200px) {
  .tender-bid-form-card, .tender-detail-card {
    max-width: 98vw;
    padding: 24px 16px 18px 16px;
  }
}
@media (max-width: 900px) {
  .tender-bid-page-container {
    padding: 24px 0;
  }
  .tender-bid-form-card, .tender-detail-card {
    padding: 18px 8px 12px 8px;
    margin-bottom: 18px;
  }
  .page-title, .section-title, .tender-bid-title {
    font-size: 18px;
    margin-bottom: 12px;
    padding-bottom: 4px;
  }
  .page-title::after, .section-title::after, .tender-bid-title::after {
    width: 24px;
    height: 2px;
    margin-top: 4px;
  }
  .page-subtitle, .section-subtitle {
    font-size: 13px;
    margin-bottom: 10px;
  }
  .tender-detail-header {
    font-size: 14px;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    padding: 10px 10px;
  }
  .tender-title-row {
    font-size: 14px;
  }
}
@media (max-width: 767px) {
  .tender-detail-header {
    font-size: 13px;
  }
  .section-title {
    font-size: 13px;
    padding-left: 7px;
  }
  .detail-item {
    flex-direction: column;
    gap: 3px;
  }
}
@media (max-width: 600px) {
  .tender-bid-page-container {
    padding: 8px 0;
  }
  .tender-bid-form-card, .tender-detail-card {
    padding: 8px 2px 8px 2px;
    margin-bottom: 10px;
    border-radius: 8px;
  }
  .page-title, .section-title, .tender-bid-title {
    font-size: 16px;
    margin-bottom: 8px;
    padding-bottom: 2px;
  }
  .page-title::after, .section-title::after, .tender-bid-title::after {
    width: 16px;
    height: 2px;
    margin-top: 2px;
  }
  .page-subtitle, .section-subtitle {
    font-size: 11.5px;
    margin-bottom: 6px;
  }
  .tender-title-row {
    font-size: 12px;
    gap: 5px;
  }
  .compact-bid-form {
    padding: 6px;
    gap: 8px;
  }
  .compact-submit-btn {
    width: 100%;
    min-width: 0;
    font-size: 12px;
    height: 40px;
  }
}

.elegant-bid-btn {
  height: 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  padding: 0 28px;
  box-shadow: 0 2px 8px var(--primary-shadow);
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.01em;
}
.elegant-bid-btn:focus,
.elegant-bid-btn:hover {
  background: var(--primary-dark) !important;
  color: #fff !important;
  box-shadow: 0 4px 16px var(--primary-shadow);
}

/* Yeni Form Tasarımı */
.bid-form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 10px;
}

.bid-form-group {
  background: var(--gray-50);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--gray-200);
}

.input-group-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: var(--gray-700);
  font-weight: 500;
  font-size: 14px;
}

.input-group-title .icon {
  color: var(--primary-color);
  font-size: 16px;
}

.input-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  align-items: flex-end;
}

.year-input,
.km-input {
  max-width: 120px;
}

.status-input {
  min-width: 160px;
}

.amount-input {
  max-width: 200px;
}

.amount-input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.amount-input-group .form-control {
  padding-right: 30px;
}

.amount-input-group .currency {
  position: absolute;
  right: 10px;
  color: var(--gray-600);
  font-weight: 500;
}

.submit-btn-wrapper {
  display: flex;
  align-items: flex-end;
}

.submit-bid-btn {
  height: 38px;
  border-radius: 8px;
  padding: 0 24px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--success);
  border-color: var(--success);
  transition: all 0.2s ease;
  min-width: 160px;
  justify-content: center;
}

.submit-bid-btn:hover:not(:disabled) {
  background: color-mix(in srgb, var(--success) 90%, black);
  border-color: color-mix(in srgb, var(--success) 90%, black);
  transform: translateY(-1px);
}

.submit-bid-btn .icon {
  font-size: 14px;
}

.loading-spinner {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner .spinner-border {
  width: 16px;
  height: 16px;
}

/* Form elemanları için genel stiller */
.form-control,
.form-select {
  height: 38px;
  border-radius: 8px;
  border: 1px solid var(--gray-300);
  font-size: 14px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-transparent);
}

.form-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--gray-600);
  margin-bottom: 6px;
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
  .input-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .year-input,
  .km-input,
  .status-input,
  .amount-input {
    max-width: none;
  }

  .submit-btn-wrapper {
    margin-top: 8px;
  }

  .submit-bid-btn {
    width: 100%;
  }
}

/* Yeni Kompakt Form Tasarımı */
.compact-bid-form {
  display: grid;
  grid-template-columns: repeat(5, 1fr) auto;
  gap: 18px;
  background: var(--white);
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid var(--gray-200);
  max-width: 100%;
  margin: 0 auto;
  align-items: end;
  overflow-x: auto;
}

.compact-input {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.compact-input .form-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--gray-600);
  margin-bottom: 4px;
  white-space: nowrap;
}

.compact-input .form-control,
.compact-input .form-select {
  height: 38px;
  padding: 6px 10px;
  border: 1px solid var(--gray-200);
  border-radius: 6px;
  font-size: 13px;
  color: var(--gray-900);
  background-color: var(--white);
  transition: all 0.2s;
  width: 100%;
  box-sizing: border-box;
}

.year-input,
.km-input,
.status-input,
.quantity-input,
.amount-input {
  min-width: 0;
  max-width: 100%;
}

.amount-wrapper {
  position: relative;
}
.amount-wrapper .form-control { padding-right: 26px; }
.amount-wrapper .currency {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-600);
  font-weight: 500;
  font-size: 13px;
  pointer-events: none;
}

.compact-submit-btn {
  height: 38px;
  padding: 0 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  white-space: nowrap;
  transition: all 0.2s;
  min-width: 120px;
  border: none;
  background: var(--primary-color);
  color: #fff;
  box-shadow: 0 1px 4px var(--primary-shadow);
}
.compact-submit-btn:hover:not(:disabled) {
  background: var(--primary-dark);
  color: #fff;
  transform: translateY(-1px);
}
.compact-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive: 900px altında grid 2 satıra bölünsün, buton alta geçsin */
@media (max-width: 900px) {
  .compact-bid-form {
    grid-template-columns: 1fr 1fr 1fr;
    grid-template-rows: auto auto;
    gap: 14px;
  }
  .compact-input:nth-child(4),
  .compact-input:nth-child(5),
  .compact-input:nth-child(6) {
    grid-row: 2;
  }
  .compact-submit-btn {
    grid-column: 1 / -1;
    width: 100%;
    margin-top: 8px;
  }
}

/* 600px altında tamamen dikey dizilim */
@media (max-width: 600px) {
  .compact-bid-form {
    grid-template-columns: 1fr;
    grid-template-rows: none;
    gap: 10px;
    padding: 6px;
  }
  .compact-input, .amount-input, .status-input, .year-input, .km-input, .quantity-input {
    max-width: 100%;
    min-width: 0;
  }
  .compact-submit-btn {
    width: 100%;
    min-width: 0;
    font-size: 12px;
    height: 40px;
  }
}

.compact-bid-form::-webkit-scrollbar { display: none; }
.compact-bid-form { -ms-overflow-style: none; scrollbar-width: none; }

.compact-input .form-control:invalid:focus,
.compact-input .form-select:invalid:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.alert-success {
  margin-bottom: 16px;
  border-radius: 8px;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
}

/* İhale tarihleri ve sayaç kutusu responsive optimizasyonu */
.tender-date-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 24px;
  background: #f8f9fa;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}
.tender-date-box, .tender-remaining-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 120px;
  flex: 1;
}
.tender-date-label {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 4px;
}
.tender-date-value {
  font-size: 15px;
  font-weight: 500;
  color: #212529;
  display: flex;
  align-items: center;
  gap: 6px;
}
.tender-remaining-box {
  min-width: 160px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  padding: 6px 12px;
  align-items: flex-start;
}
.tender-remaining-label {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 4px;
}
.tender-remaining-value {
  font-size: 15px;
  font-weight: 600;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 6px;
}

@media (max-width: 700px) {
  .tender-date-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 8px 6px;
    margin-bottom: 14px;
  }
  .tender-date-box, .tender-remaining-box {
    min-width: 0;
    width: 100%;
    padding: 6px 8px;
    border-radius: 6px;
    margin-bottom: 0;
  }
  .tender-date-label, .tender-remaining-label {
    font-size: 12px;
    margin-bottom: 2px;
  }
  .tender-date-value, .tender-remaining-value {
    font-size: 11.5px;
    gap: 2px;
    flex-wrap: wrap;
    word-break: break-all;
  }
  .tender-date-value svg,
  .tender-remaining-value svg {
    font-size: 13px !important;
  }
}

@media (max-width: 480px) {
  .tender-date-row {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    align-items: stretch;
  }
  .tender-date-box {
    flex: 1 1 48%;
    min-width: 120px;
    order: 1;
    margin-bottom: 0;
  }
  .tender-remaining-box {
    flex: 1 1 100%;
    width: 100%;
    order: 2;
    margin-top: 4px;
    min-width: 0;
  }
} 