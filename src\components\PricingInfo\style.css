@import url('../../styles/colors.css');

.pricing-info-section {
  background-color: #f8f9fa;
  padding: 2rem 0;
}

.pricing-card {
  margin-bottom: 30px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  background-color: #fff;
  border: 2px solid var(--primary-color);
}

.pricing-card .card-header {
  background: linear-gradient(90deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 20px 25px;
}

.pricing-card .card-header h5 {
  font-weight: 700;
  color: #001238;
  position: relative;
  display: inline-block;
  padding-left: 15px;
}

.pricing-card .card-header h5:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 20px;
  background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
  border-radius: 3px;
}

.pricing-card .card-body {
  padding: 25px;
  background-color: #ffffff;
}

.pricing-section-title {
  margin-bottom: 20px;
  color: #001238;
  font-weight: 600;
  position: relative;
  display: inline-block;
  padding-bottom: 8px;
}

.pricing-section-title:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.pricing-package {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  height: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid #e9ecef;
}

.pricing-package:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.pricing-package.featured {
  border: 2px solid #007bff;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.package-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: #007bff;
  color: white;
  padding: 5px 15px;
  font-size: 0.75rem;
  border-radius: 20px;
  font-weight: 600;
}

.pricing-package h5 {
  margin-bottom: 15px;
  color: #343a40;
  font-weight: 600;
}

.pricing-package .price {
  font-size: 2rem;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 20px;
}

.package-features {
  list-style: none;
  padding-left: 0;
  margin-bottom: 20px;
}

.package-features li {
  padding: 8px 0;
  position: relative;
  padding-left: 25px;
}

.package-features li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.pricing-link {
  color: #007bff;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
}

.pricing-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

@media (max-width: 767px) {
  .pricing-package {
    margin-bottom: 20px;
  }
}

.pricing-package.featured .pricing-header {
  background: linear-gradient(180deg, var(--primary-color), var(--primary-light));
  color: #fff;
}

.pricing-action .btn-pricing-action {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  color: #fff;
  border: none;
  padding: 10px 25px;
  border-radius: 5px;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.pricing-card .pricing-header {
  background: var(--primary-color);
  color: #fff;
  padding: 30px 20px;
  text-align: center;
}

.pricing-card .pricing-features li:before {
  content: "\2713";
  margin-right: 8px;
  color: var(--primary-color);
  font-weight: bold;
}

.pricing-card .btn-pricing {
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  background-color: transparent;
  border-radius: 30px;
  padding: 10px 25px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
  text-decoration: none;
} 