@import url('../../styles/colors.css');

/* HeroVisitor Bileşeni - Ultra Minimal <PERSON> */

/* Ana <PERSON> Container */
.visitor-hero {
  position: relative;
  background: var(--primary-color);
  padding: 60px 0;
  overflow: hidden;
  z-index: 1;
}

/* Hero <PERSON> */
.visitor-hero .hero-content {
  position: relative;
  z-index: 5;
  text-align: left;
  max-width: 550px;
}

/* <PERSON> */
.visitor-hero .hero-title {
  color: #fff;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 15px;
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Hero <PERSON>ma */
.visitor-hero .hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 25px;
  animation: slideIn 0.5s ease-out 0.1s both;
}

/* <PERSON> */
.visitor-hero .hero-buttons {
  display: flex;
  gap: 15px;
  animation: slideIn 0.5s ease-out 0.2s both;
}

/* Buton <PERSON>tak Stilleri */
.visitor-hero .hero-buttons a {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

/* Detaylı Bilgi Butonu */
.visitor-hero .hero-buttons .gauto-btn-white {
  background-color: transparent;
  color: #fff;
  border: 2px solid rgba(255, 255, 255, 0.7);
  position: relative;
}

.visitor-hero .hero-buttons .gauto-btn-white:before,
.visitor-hero .hero-buttons .gauto-btn-white:after {
  content: "";
  display: block;
  position: absolute;
  border-color: #fff;
  box-sizing: border-box;
  border-style: solid;
  width: 1em;
  height: 1em;
  transition: all 0.3s ease-in-out;
}

.visitor-hero .hero-buttons .gauto-btn-white:before {
  top: -6px;
  left: -6px;
  border-width: 2px 0 0 2px;
  z-index: 5;
}

.visitor-hero .hero-buttons .gauto-btn-white:after {
  bottom: -6px;
  right: -6px;
  border-width: 0 2px 2px 0;
}

.visitor-hero .hero-buttons .gauto-btn-white:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: #fff;
  color: #fff;
}

.visitor-hero .hero-buttons .gauto-btn-white:hover:before,
.visitor-hero .hero-buttons .gauto-btn-white:hover:after {
  width: calc(100% + 12px);
  height: calc(100% + 12px);
  border-color: #fff;
}

/* Hemen Kaydol Butonu */
.visitor-hero .hero-buttons .gauto-btn {
  background-color: #fff;
  color: var(--primary-color);
  border: 2px solid #fff;
  position: relative;
}

.visitor-hero .hero-buttons .gauto-btn:before,
.visitor-hero .hero-buttons .gauto-btn:after {
  content: "";
  display: block;
  position: absolute;
  border-color: #fff;
  box-sizing: border-box;
  border-style: solid;
  width: 1em;
  height: 1em;
  transition: all 0.3s ease-in-out;
}

.visitor-hero .hero-buttons .gauto-btn:before {
  top: -6px;
  left: -6px;
  border-width: 2px 0 0 2px;
  z-index: 5;
}

.visitor-hero .hero-buttons .gauto-btn:after {
  bottom: -6px;
  right: -6px;
  border-width: 0 2px 2px 0;
}

.visitor-hero .hero-buttons .gauto-btn:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.visitor-hero .hero-buttons .gauto-btn:hover:before,
.visitor-hero .hero-buttons .gauto-btn:hover:after {
  width: calc(100% + 12px);
  height: calc(100% + 12px);
  border-color: #fff;
}

/* Avantajlar Paneli */
.visitor-hero .hero-advantages-panel {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 10;
}

/* Avantaj Panel başlık */
.visitor-hero .hero-features-title {
  text-align: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.visitor-hero .hero-features-subtitle {
  font-size: 0.9rem;
  color: #fff;
  font-weight: 500;
}

/* Avantajlar Grid */
.visitor-hero .advantages-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 15px;
  flex: 1;
}

/* Avantaj Kartı */
.visitor-hero .advantage-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  padding: 12px 10px;
  text-align: center;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeInUp 0.6s ease-out backwards;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.visitor-hero .advantage-card:nth-child(1) {
  animation-delay: 0.2s;
}

.visitor-hero .advantage-card:nth-child(2) {
  animation-delay: 0.3s;
}

.visitor-hero .advantage-card:nth-child(3) {
  animation-delay: 0.4s;
}

.visitor-hero .advantage-card:nth-child(4) {
  animation-delay: 0.5s;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Avantaj kartı hover efektleri */
.visitor-hero .advantage-card:hover {
  transform: translateY(-1px);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.visitor-hero .advantage-card:hover .advantage-icon {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.2);
}

.visitor-hero .advantage-card:hover .advantage-title {
  color: var(--white);
  font-weight: 600;
}

.visitor-hero .advantage-card::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.8), rgba(255,255,255,0));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.visitor-hero .advantage-card:hover::after {
  transform: scaleX(1);
}

/* Avantaj İkonu */
.visitor-hero .advantage-icon {
  background: rgba(255, 255, 255, 0.1);
  width: 40px;
  height: 40px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

/* Avantaj Başlık */
.visitor-hero .advantage-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.8rem;
  font-weight: 400;
  line-height: 1.3;
  transition: color 0.3s ease;
}

/* Panel CTA Butonu */
.visitor-hero .panel-cta-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  color: var(--primary-color);
  border: 2px solid #fff;
  position: relative;
  font-weight: 500;
  padding: 10px 20px;
  border-radius: 0;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
  text-decoration: none;
}

.visitor-hero .panel-cta-button:hover {
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: var(--primary-color);
}

/* gauto-btn panel-cta-button */
.visitor-hero .gauto-btn.panel-cta-button:before,
.visitor-hero .gauto-btn.panel-cta-button:after {
  content: "";
  display: block;
  position: absolute;
  border-color: #fff;
  box-sizing: border-box;
  border-style: solid;
  width: 1em;
  height: 1em;
  transition: all 0.3s ease-in-out;
}

.visitor-hero .gauto-btn.panel-cta-button:before {
  top: -6px;
  left: -6px;
  border-width: 2px 0 0 2px;
  z-index: 5;
}

.visitor-hero .gauto-btn.panel-cta-button:after {
  bottom: -6px;
  right: -6px;
  border-width: 0 2px 2px 0;
}

.visitor-hero .gauto-btn.panel-cta-button:hover:before,
.visitor-hero .gauto-btn.panel-cta-button:hover:after {
  width: calc(100% + 12px);
  height: calc(100% + 12px);
  border-color: #fff;
}

/* Responsive Düzenlemeler */
@media (max-width: 991px) {
  .visitor-hero {
    padding: 40px 0;
  }
  
  .visitor-hero .hero-content {
    max-width: 100%;
    text-align: center;
    margin-bottom: 25px;
  }
  
  .visitor-hero .hero-buttons {
    justify-content: center;
  }
  
  .visitor-hero .hero-advantages-panel {
    max-width: 500px;
    margin: 0 auto;
  }
}

@media (max-width: 767px) {
  .visitor-hero .hero-title {
    font-size: 2rem;
  }
  
  .visitor-hero .hero-subtitle {
    font-size: 0.9rem;
  }
  
  .visitor-hero .hero-buttons {
    flex-direction: column;
    max-width: 250px;
    margin: 0 auto;
  }
  
  .visitor-hero .hero-buttons .gauto-btn,
  .visitor-hero .hero-buttons .gauto-btn-white {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .visitor-hero {
    padding: 30px 0;
  }
  
  .visitor-hero .hero-title {
    font-size: 1.6rem;
  }
} 