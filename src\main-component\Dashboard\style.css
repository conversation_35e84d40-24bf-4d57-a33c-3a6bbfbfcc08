/* Dashboard Styles */
/* :root değ<PERSON>ş<PERSON><PERSON>i kaldırıldı, global renkler styles/colors.css'den geliyor */

/* Hero Section */
.dashboard-hero-section {
  background-color: #f5f7fa;
  padding: 30px 0;
  margin-bottom: 30px;
}

.dashboard-hero-section:before {
  content: '';
  position: absolute;
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  top: -75px;
  right: 10%;
}

.dashboard-hero-section:after {
  content: '';
  position: absolute;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  bottom: -50px;
  left: 15%;
}

.dashboard-hero-content {
  text-align: center;
}

.dashboard-hero-content h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.dashboard-hero-content p {
  font-size: 1.1rem;
  color: #666;
  max-width: 700px;
  margin: 0 auto;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.3);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

/* Button Styling */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  padding: 10px 20px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
  letter-spacing: 0.5px;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px var(--primary-shadow);
}

.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: #fff;
  border-radius: 6px;
}

.btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(23, 162, 184, 0.3);
}

.btn-sm {
  padding: 0.35rem 0.8rem;
  font-size: 0.875rem;
  border-radius: 4px;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
  border-radius: 6px;
}

.btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
}

.btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  border-radius: 6px;
}

.btn-warning:hover {
  background-color: #e0a800;
  border-color: #d39e00;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(255, 193, 7, 0.3);
}

/* Badge Styling */
.badge {
  padding: 6px 12px;
  font-weight: 500;
  border-radius: 30px;
  letter-spacing: 0.5px;
  font-size: 12px;
}

.badge-success {
  background-color: #28a745;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-secondary {
  background-color: #6c757d;
}

/* Empty states */
.text-center.py-4 {
  padding: 40px 0;
}

.text-center.py-4 p {
  margin-bottom: 20px;
  color: #666;
  font-size: 16px;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .dashboard-hero-content h1 {
    font-size: 1.6rem;
  }
  
  .dashboard-hero-content p {
    font-size: 1rem;
  }
  
  .dashboard-content-row > [class*="col-"] {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-hero-section {
    padding: 20px 0;
  }
  
  .dashboard-hero-content h1 {
    font-size: 1.4rem;
  }
  
  .dashboard-main-content {
    padding-bottom: 30px;
  }
}

/* Yükleme konteynerı için stil */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
  color: #666;
}

.loading-container p {
  padding: 20px;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.loading-container p:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background: linear-gradient(to right, transparent, var(--primary-color), transparent);
  animation: loading-bar 2s infinite ease-in-out;
}

@keyframes loading-bar {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.dashboard-main-content {
  margin-top: 50px;
  padding-bottom: 50px;
}

.dashboard-content-row {
  margin-bottom: 30px;
}

/* Yeni sınıflar - Dashboard yerleşimi için */
.main-content-column, .sidebar-column {
  margin-bottom: 30px;
}

.sidebar-column {
  display: flex;
  flex-direction: column;
}

.sidebar-widget {
  margin-bottom: 25px;
}

.sidebar-widget:last-child {
  margin-bottom: 0;
}

/* Önerilen İhaleler ve Son Aktiviteler bölümleri için yükseklik ayarları */
.sidebar-widget .suggested-tenders-section,
.sidebar-widget .recent-activities-section {
  height: 100%;
} 