import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchTenderById } from '../../../redux/slices/tendersSlice';
import { getTenderDetails, getCompanyData, getBidResponses } from '../../../services/tenderBidService';
import { useMockAPI } from '../../../services/api';

/**
 * İhale verilerini yönetmek için özel hook
 * @param {string} id - İhale ID'si
 * @returns {object} - İhale, şirket, kullanıcı teklifi ve yükleme/hata durumları
 */
const useTenderData = (id) => {
  const dispatch = useDispatch();
  const { currentTender, loading: reduxLoading, error: reduxError } = useSelector((state) => state.tenders);
  
  const [tender, setTender] = useState(null);
  const [company, setCompany] = useState(null);
  const [userBid, setUserBid] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    let mounted = true;
    
    if (!id) {
      setError("İhale ID'si bulunamadı. Lütfen geçerli bir ihale seçin.");
      setIsLoading(false);
      return;
    }
    
    const loadTenderDetails = async () => {
      try {
        // Redux üzerinden ihale verilerini getir
        dispatch(fetchTenderById(id))
          .unwrap()
          .then(tenderData => {
            if (!mounted) return;
            setTender(tenderData);
            loadAdditionalData(tenderData);
          })
          .catch(err => {
            if (!mounted) return;
            console.error("Redux'tan ihale detayları alınırken hata:", err);
            fallbackToServiceCall();
          });
      } catch (err) {
        if (!mounted) return;
        setError(`İhale detaylarını yüklerken bir sorun oluştu.`);
        setIsLoading(false);
      }
    };
    
    // Redux ile veri alınamazsa servis üzerinden almayı dene
    const fallbackToServiceCall = async () => {
      try {
        const response = await getTenderDetails(id);
        
        if (!mounted) return;
        
        if (response.success && response.data) {
          setTender(response.data);
          loadAdditionalData(response.data);
        } else {
          throw new Error("İhale verisi alınamadı");
        }
      } catch (err) {
        if (!mounted) return;
        
        // Geliştirme modunda mock veri kullan
        if (process.env.NODE_ENV === 'development' || useMockAPI) {
          // Mevcut TenderBidDetail'deki MOCK_TENDER ve MOCK_COMPANY burada kullanılamaz
          // Burada direkt servis içindeki mockData'ya erişmek daha doğru olur
          console.error("İhale verisi alınamadı ve mock veri kullanılamadı:", err);
          setError("İhale detayları alınamadı: " + err.message);
        } else {
          setError("İhale detayları alınamadı");
        }
        setIsLoading(false);
      }
    };
    
    // Şirket bilgilerini ve kullanıcı tekliflerini getir
    const loadAdditionalData = async (tenderData) => {
      try {
        // Şirket verilerini getir
        const companyResponse = await getCompanyData();
        if (mounted && companyResponse && companyResponse.success) {
          setCompany(companyResponse.data);
        }
        
        // Kullanıcı tekliflerini getir
        const bidsResponse = await getBidResponses(id);
        if (mounted && bidsResponse && bidsResponse.success) {
          // Kullanıcının ihaleye verdiği teklifleri kontrol et
          const userBids = bidsResponse.data.filter(bid => bid.tenderId === parseInt(id));
          if (userBids.length > 0) {
            setUserBid(userBids[0]);
          }
        }
        
        if (mounted) {
          setIsLoading(false);
        }
      } catch (err) {
        if (mounted) {
          console.error("Ek veriler yüklenirken hata:", err);
          setIsLoading(false);
        }
      }
    };
    
    loadTenderDetails();
    
    return () => {
      mounted = false;
    };
  }, [id, dispatch]);
  
  // Redux'taki loading ve error durumlarını senkronize et
  useEffect(() => {
    if (reduxLoading !== undefined) {
      setIsLoading(reduxLoading);
    }
  }, [reduxLoading]);
  
  useEffect(() => {
    if (reduxError) {
      setError(reduxError);
    }
  }, [reduxError]);
  
  // Redux'taki tender değişimini senkronize et
  useEffect(() => {
    if (currentTender && !tender) {
      setTender(currentTender);
      setIsLoading(false);
    }
  }, [currentTender, tender]);
  
  return { tender, company, userBid, isLoading, error };
};

export default useTenderData; 