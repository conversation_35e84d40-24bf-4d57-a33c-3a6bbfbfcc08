import React, { Fragment, useState, useEffect } from 'react';
import { Container, Row, Col } from 'react-bootstrap';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// <PERSON><PERSON> bileşenler
import DashboardStats from '../../components/DashboardStats';
import DashboardActionBar from '../../components/DashboardActionBar';
import ActiveTenders from '../../components/ActiveTenders';
import SuggestedTenders from '../../components/SuggestedTenders';
import RecentActivities from '../../components/RecentActivities';
import CompanyHero from '../../components/CompanyHero';

import './style.css';
import dashboardService from '../../services/dashboardService';

const Dashboard = () => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Auth durumunu konsola yazdır
  useEffect(() => {

  }, [isAuthenticated, user]);

  // Kullanıcı giriş yapmamışsa, login sayfasına yönlendir
  useEffect(() => {
    if (isAuthenticated === false) {

      navigate('/login', { state: { from: '/dashboard' } });
    }
  }, [isAuthenticated, navigate]);

  // Birleştirilmiş dashboard verileri
  const [dashboardData, setDashboardData] = useState({
    myTenders: 0,
    openTenders: 0,
    activeBids: 0,
    approvedBids: 0,
    contracts: 0,
    vehicles: 0
  });

  useEffect(() => {
    // Kullanıcı giriş yapmamışsa, veri yüklenmemeli
    if (!isAuthenticated) {

      return;
    }
    
    
    // API'den dashboard verilerini çek
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        const response = await dashboardService.getDashboardCards();
        if (response && response.isSuccess && response.result) {
          setDashboardData({
            myTenders: response.result.activeTenders,
            openTenders: response.result.completedTenders, // örnek: verilen teklifler completedTenders olarak eşlendi
            activeBids: response.result.wonTenders,
            // Son 7 gün artışları
            lastWeekIncreaseActiveTenders: response.result.lastWeekIncreaseActiveTenders,
            lastWeekIncreaseCompletedTenders: response.result.lastWeekIncreaseCompletedTenders,
            lastWeekIncreaseWonTenders: response.result.lastWeekIncreaseWonTenders,
            // Toplam işlem
            totalProcess: response.result.totalProcess,
            lastMonthIncreaseTotalProcess: response.result.lastMonthIncreaseTotalProcess,
            // Diğer alanlar gerekirse eklenebilir
          });
        } else {
          setError(response?.message || 'Veriler yüklenemedi');
        }
        setIsLoading(false);
      } catch (err) {
        console.error('Dashboard verileri yüklenirken hata oluştu:', err);
        setError(err.message || 'Veriler yüklenirken bir hata oluştu');
        setIsLoading(false);
      }
    };
    
    loadDashboardData();
  }, [user?.id, isAuthenticated]);

  // Hero bölümü için metinler
  const heroTitle = t("dashboard.welcome", { name: user?.name || t('common.user') });
  const heroDescription = t("dashboard.subtitle") || t('dashboard.description');

  // Kullanıcı giriş yapmamışsa, yükleme ekranı göster
  if (isAuthenticated === false) {
    return <div className="loading-container"><p>{t('common.redirecting')}</p></div>;
  }

  if (isLoading) {
    return <div className="loading-container"><p>{t('common.loading')}</p></div>;
  }
  if (error) {
    return <div className="loading-container"><p>{t('common.error')}: {error}</p></div>;
  }

  return (
    <Fragment>
      <CompanyHero 
        title={heroTitle}
        description={heroDescription}
      />
      
      <div className="dashboard-main-content">
        <Container fluid="lg">
          {/* Dashboard Stats */}
          <DashboardStats dashboardData={dashboardData} />
          
          {/* Dashboard Action Bar */}
          <DashboardActionBar />
          
          {/* Main Content with Sidebar */}
          <Row className="dashboard-content-row">
            {/* Sol sütun - Aktif İhalelerim */}
            <Col lg={8} className="main-content-column">
              <ActiveTenders />
            </Col>
            
            {/* Sağ sütun - Önerilen İhaleler ve Son Aktiviteler */}
            <Col lg={4} className="sidebar-column">
              {/* Önerilen İhaleler */}
              <div className="sidebar-widget">
                <SuggestedTenders />
              </div>
              
              {/* Son Aktiviteler */}
              <div className="sidebar-widget">
                <RecentActivities />
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </Fragment>
  );
};

export default Dashboard; 