import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import BlogList from "../../components/BlogList";

const BlogPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.blog")}
        pagesub={t("header-navigation.blog")}
      />
      <BlogList />
    </Fragment>
  );
};
export default BlogPage;
