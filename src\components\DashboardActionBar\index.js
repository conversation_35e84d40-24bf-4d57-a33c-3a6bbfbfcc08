import React from 'react';
import { Container, <PERSON>, Col } from 'react-bootstrap';
import { FaPlus, FaSearch, FaChartBar, FaQuestionCircle } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import './style.css';

const DashboardActionBar = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  
  // Action handlers
  const handleCreateTender = () => {
    navigate('/tenders/start');
  };
  
  const handleExplore = () => {
    navigate('/available-tenders');
  };
  
  const handleReports = () => {
    navigate('/reports');
  };
  
  const handleHelp = () => {
    navigate('/ihale-sureci-detay');
  };
  
  return (
    <section className="dashboard-action-bar">
      <Container>
        <Row className="action-buttons-container">
          <Col xs={3}>
            <button 
              className="action-button create-button"
              onClick={handleCreateTender}
            >
              <FaPlus className="action-icon" />
              <span>{t('dashboard.actions.create_tender')}</span>
            </button>
          </Col>
          <Col xs={3}>
            <button 
              className="action-button explore-button"
              onClick={handleExplore}
            >
              <FaSearch className="action-icon" />
              <span>{t('dashboard.actions.explore')}</span>
            </button>
          </Col>
          <Col xs={3}>
            <button 
              className="action-button reports-button"
              onClick={handleReports}
            >
              <FaChartBar className="action-icon" />
              <span>{t('dashboard.actions.reports')}</span>
            </button>
          </Col>
          <Col xs={3}>
            <button 
              className="action-button help-button"
              onClick={handleHelp}
            >
              <FaQuestionCircle className="action-icon" />
              <span>{t('dashboard.actions.help')}</span>
            </button>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default DashboardActionBar; 