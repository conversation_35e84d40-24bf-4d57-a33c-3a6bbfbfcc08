# Değişiklik Günlüğü

<PERSON>, LongTermRental Frontend projesi üzerinde yapılan yapısal değişiklikleri belgelemektedir.

## [1.0.0] - 2024 (Ya<PERSON><PERSON><PERSON>lemele<PERSON>)

### Eklenenler
- `WebUI/src/STRUCTURE.md` - Kaynak kod organizasyonunu açıklayan belge
- `WebUI/src/components/README.md` - Atomik bileşenler için standartlar
- `WebUI/src/main-component/README.md` - Sayfa bileşenleri için standartlar
- `WebUI/STRUCTURE_VISUAL.md` - Projenin görsel dosya yapısı
- `CHANGELOG.md` - Bu değişiklik günlüğü dosyası
- `WebUI/README.md` - Güncellenmiş proje açıklaması

### Değiştirilmiş
- `package.json` - Proje adı standardize edildi ve test komutu eklendi
- `WebUI/package.json` - <PERSON><PERSON> adı "gauto" -> "long-term-rental-frontend-webui" olarak değiştirildi, versiyon 0.2.0 -> 1.0.0 olarak güncellendi, eksik bağımlılık (@keyvaluesystems/react-stepper) eklendi

### Düzeltmeler
- Package.json dosyaları arasındaki tutarsızlıklar giderildi
- Proje ismi tüm dosyalarda "long-term-rental-frontend" olarak standardize edildi

### Yapısal İyileştirmeler
- Proje yapısı ve dosya organizasyonu dokümante edildi
- Komponent vs sayfa bileşenleri için standartlar netleştirildi

### Refaktörleme Önerileri
- AboutPage, BlogPage gibi çift kullanımlı bileşenlerin yeniden adlandırılması
- Sayfa bileşenlerinin sadece main-component klasöründe tutulması
- Atomik bileşenlerin components klasöründe tutulması 

## [1.1.0] - 2024 (Proje Yapısı Refaktörü)

### Değişiklikler
- Package.json dosyaları tek ve merkezi hale getirildi
- WebUI klasöründeki tüm içerik ana dizine taşındı
- React uygulaması kök dizinden çalışacak şekilde yapılandırıldı

### Faydalar
- Daha basit ve anlaşılır proje yapısı
- Tek bir package.json ile bağımlılık yönetimi kolaylaştı
- Gereksiz iç içe yapı kaldırıldı 