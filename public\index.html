<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#FF6200" />
    <meta
      name="description"
      content="İhaleden Kirala, Türkiye'nin ilk ihale bazlı araç kiralama platformu. Şirketiniz için en uygun fiyatlı uzun dönem araç ve filo kiralama hizmetleri."
    />
    <meta name="keywords" content="ihale ile araç kiralama, ihaleden araba kiralama, uzun dönem filo kiralama, kurumsal araç kiralama, ihale sistemi" />
    <meta name="author" content="İhaleden Kirala" />
    <meta name="robots" content="index, follow" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://www.ihaledenkirala.com" />
    
    <!-- Dil Alternatifleri -->
    <link rel="alternate" hreflang="tr-TR" href="https://www.ihaledenkirala.com" />
    <link rel="alternate" hreflang="en-US" href="https://www.ihaledenkirala.com/en" />
    <link rel="alternate" hreflang="x-default" href="https://www.ihaledenkirala.com" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.ihaledenkirala.com" />
    <meta property="og:site_name" content="İhaleden Kirala" />
    <meta property="og:locale" content="tr_TR" />
    <meta property="og:locale:alternate" content="en_US" />
    <meta property="og:title" content="İhaleden Kirala | İhale Sistemi ile Araç Kiralama" />
    <meta property="og:description" content="Türkiye'nin yenilikçi ihale sistemi ile araç kiralama platformu. Şirketiniz için en uygun fiyat garantisi ile uzun dönem araç ve filo kiralama." />
    <meta property="og:image" content="https://www.ihaledenkirala.com/assets/images/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://www.ihaledenkirala.com" />
    <meta property="twitter:title" content="İhaleden Kirala | İhale Sistemi ile Araç Kiralama" />
    <meta property="twitter:description" content="Türkiye'nin yenilikçi ihale sistemi ile araç kiralama platformu. Şirketiniz için en uygun fiyat garantisi ile uzun dönem araç ve filo kiralama." />
    <meta property="twitter:image" content="https://www.ihaledenkirala.com/assets/images/og-image.jpg" />
    
    <!-- Charlevoix Font Preload -->
    <link rel="preload" href="%PUBLIC_URL%/fonts/CharlevoixPro-Regular.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="%PUBLIC_URL%/fonts/CharlevoixPro-Bold.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="%PUBLIC_URL%/fonts/CharlevoixPro-Medium.otf" as="font" type="font/otf" crossorigin="anonymous">
    <link rel="preload" href="%PUBLIC_URL%/fonts/CharlevoixPro-Black.otf" as="font" type="font/otf" crossorigin="anonymous">
    
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>İhaleden Kirala | İhale Sistemi ile Araç Kiralama</title>
    <link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
/>
    <meta name="mobile-web-app-capable" content="yes" />
  </head>
  <body>
    <noscript>Bu web sitesini görüntülemek için JavaScript'i etkinleştirmeniz gerekmektedir.</noscript>
    <div id="root"></div>
  </body>
</html>
