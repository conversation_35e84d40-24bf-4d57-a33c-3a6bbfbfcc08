import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import Gallery from "../../components/Gallery";

const GalleryPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.gallery")}
        pagesub={t("header-navigation.gallery")}
      />
      <Gallery />
    </Fragment>
  );
};
export default GalleryPage;
