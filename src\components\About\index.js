import React from "react";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";
import { FaCheck } from "react-icons/fa";
import Img1 from "../../img/new-images/HomePage/ihaleden-kirala-anasayfa-kirmizi-araba.png";
import SectionTitle from "../common/SectionTitle";
import "./style.css";

const About = () => {
  const { t } = useTranslation();
  return (
    <section className="gauto-about-area section_70">
      <Container>
        <Row>
          <Col lg={7}>
            <div className="about-left">
              <SectionTitle
                subtitle={t("about_us_title")}
                title={t("welcome_title")}
                alignment="left"
              />
              <p>{t("about_text")}</p>
              <div className="about-list">
                <ul>
                  <li>
                    <span>
                      <FaCheck />
                    </span>
                    {t("trusted_name")}
                  </li>
                  <li>
                    <span>
                      <FaCheck />
                    </span>
                    {t("deal_brands")}
                  </li>
                  <li>
                    <span>
                      <FaCheck />
                    </span>
                    {t("larger_stocks")}
                  </li>
                  <li>
                    <span>
                      <FaCheck />
                    </span>
                    {t("worldwide_location")}
                  </li>
                </ul>
              </div>
            </div>
          </Col>
          <Col lg={5}>
            <div className="about-right">
              <img src={Img1} alt="car" />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default About;
