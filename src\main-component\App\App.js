import React, { Fragment } from "react";
import { Helmet } from 'react-helmet-async';
import AllRoute from "../router";
import "./App.css";

const App = () => {
  return (
    <Fragment>
      <Helmet>
        <meta charSet="utf-8" />
        <title>Uzun Dönem Araç Kiralama</title>
        <meta name="description" content="Uzun dönem araç kiralama, filo kiralama ve ihale sistemi. En uygun fiyatları ve araç modellerini bulun." />
        <meta name="keywords" content="uzun dönem araç kiralama, filo kiralama, araç kiralama, ihale sistemi" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="canonical" href={window.location.href} />
      </Helmet>
      <AllRoute />
    </Fragment>
  );
};

export default App;
