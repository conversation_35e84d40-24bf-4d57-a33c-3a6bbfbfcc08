/**
 * İhale ve teklif durumlarını görsel öğelere dönüştüren yardımcı fonksiyonlar
 */
import i18n from '../../../i18n';

// İhale durumuna göre CSS sınıfını belirle
export const getStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case "active":
      return "tender-status-active";
    case "pending":
      return "tender-status-pending";
    case "completed":
      return "tender-status-completed";
    case "cancelled":
      return "tender-status-cancelled";
    case "draft":
      return "tender-status-draft";
    case "expired":
      return "tender-status-expired";
    default:
      return "tender-status-pending";
  }
};

// Teklif durumuna göre renk belirle
export const getStatusColor = (status) => {
  if (!status) return 'secondary';

  switch (status.toUpperCase()) {
    case "SUBMITTED":
      return "primary";
    case "PENDING":
      return "warning";
    case "REJECTED":
      return "danger";
    case "APPROVED":
      return "success";
    case "DRAFT":
      return "secondary";
    default:
      return "secondary";
  }
};

// İhale durumuna göre metin belirle
export const getStatusText = (status) => {
  if (!status) return '';

  switch (status.toLowerCase()) {
    case "active":
      return i18n.t('status.active', 'Aktif');
    case "pending":
      return i18n.t('status.pending', 'Beklemede');
    case "completed":
      return i18n.t('status.completed', 'Tamamlandı');
    case "cancelled":
      return i18n.t('status.cancelled', 'İptal Edildi');
    case "draft":
      return i18n.t('status.draft', 'Taslak');
    case "expired":
      return i18n.t('status.expired', 'Süresi Doldu');
    case "submitted":
      return i18n.t('status.submitted', 'Gönderildi');
    case "approved":
      return i18n.t('status.approved', 'Onaylandı');
    case "rejected":
      return i18n.t('status.rejected', 'Reddedildi');
    default:
      return status;
  }
};

export default {
  getStatusClass,
  getStatusColor,
  getStatusText,
};