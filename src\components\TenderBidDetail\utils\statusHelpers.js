/**
 * İhale ve teklif durumlarını görsel öğelere dönüştüren yardımcı fonksiyonlar
 */

// İhale durumuna göre CSS sınıfını belirle
export const getStatusClass = (status) => {
  switch (status?.toLowerCase()) {
    case "active":
      return "tender-status-active";
    case "pending":
      return "tender-status-pending";
    case "completed":
      return "tender-status-completed";
    case "cancelled":
      return "tender-status-cancelled";
    case "draft":
      return "tender-status-draft";
    case "expired":
      return "tender-status-expired";
    default:
      return "tender-status-pending";
  }
};

// Teklif durumuna göre renk belirle
export const getStatusColor = (status) => {
  if (!status) return 'secondary';
  
  switch (status.toUpperCase()) {
    case "SUBMITTED":
      return "primary";
    case "PENDING":
      return "warning";
    case "REJECTED":
      return "danger";
    case "APPROVED":
      return "success";
    case "DRAFT":
      return "secondary";
    default:
      return "secondary";
  }
};

// İhale durumuna göre metin belirle
export const getStatusText = (status) => {
  if (!status) return '';
  
  switch (status.toLowerCase()) {
    case "active":
      return "Aktif";
    case "pending":
      return "Beklemede";
    case "completed":
      return "Tamamlandı";
    case "cancelled":
      return "İptal Edildi";
    case "draft":
      return "Taslak";
    case "expired":
      return "Süresi Doldu";
    default:
      return status;
  }
};

export default {
  getStatusClass,
  getStatusColor,
  getStatusText,
}; 