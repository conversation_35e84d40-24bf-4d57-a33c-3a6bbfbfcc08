import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Container, <PERSON>, Col, Button, Pagination } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { 
  FaCar, 
  FaCogs, 
  FaGasPump, 
  FaCalendarAlt, 
  FaUserFriends, 
  FaClock, 
  FaEye, 
  FaPencilAlt, 
  FaCheck, 
  FaTimes,
  FaFilter,
  FaPlus,
  FaSort,
  FaSearch
} from "react-icons/fa";
import tenderService from "../../services/tenderService";

import "./style.css";

const TenderList = ({ userId, auctionState }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [tenders, setTenders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filterState, setFilterState] = useState(auctionState || "ALL");
  
  // Pagination için state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [totalPages, setTotalPages] = useState(1);
  
  // Arama ve sıralama için state
  const [searchQuery, setSearchQuery] = useState("");
  const [sortOption, setSortOption] = useState("newest");

  // İhaleleri yükle
  useEffect(() => {
    const loadTenders = async () => {
      setLoading(true);
      try {
        let fetchedTenders = [];

        // Eğer userId geçerliyse (null, undefined veya 0 değilse), kullanıcıya ait ihaleleri getir
        if (userId) {
          // Eğer aynı zamanda state filtresi de varsa, ona göre getir
          if (filterState && filterState !== "ALL") {
            fetchedTenders = await tenderService.getUserTendersByState(userId, filterState);
          } else {
            // Tüm durumları getir
            fetchedTenders = await tenderService.getUserTenders(userId);
          }
          
          // Fallback: Eğer kullanıcıya ait ihale bulunamazsa ve ALL filtresi seçiliyse, tüm ihaleleri göster
          if (fetchedTenders.length === 0 && filterState === "ALL") {
            fetchedTenders = await tenderService.getAllTenders();
          }
        } else {
          // Kullanıcı belirtilmemişse, tüm ihaleleri getir veya duruma göre filtrele
          if (filterState && filterState !== "ALL") {
            fetchedTenders = await tenderService.getTendersByState(filterState);
          } else {
            fetchedTenders = await tenderService.getAllTenders();
          }
        }

        // Filtreleme sonuçlarını kontrol et - hata durumunda manuel filtreleme yap
        if (filterState && filterState !== "ALL" && fetchedTenders.length > 0) {
          const filteredResults = fetchedTenders.filter(tender => tender.state === filterState);
          
          // Eğer servis filtrelemesi düzgün çalışmadıysa manuel filtrele
          if (filteredResults.length !== fetchedTenders.length) {
            fetchedTenders = filteredResults;
          }
        }
        
        // Arama filtreleme
        if (searchQuery) {
          fetchedTenders = fetchedTenders.filter(tender => 
            tender.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
            tender.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
            tender.model.toLowerCase().includes(searchQuery.toLowerCase())
          );
        }
        
        // Sıralama
        const sortedTenders = [...fetchedTenders];
        switch(sortOption) {
          case "newest":
            sortedTenders.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));
            break;
          case "oldest":
            sortedTenders.sort((a, b) => new Date(a.startDate) - new Date(b.startDate));
            break;
          case "mostOffers":
            sortedTenders.sort((a, b) => b.companyResponses - a.companyResponses);
            break;
          default:
            break;
        }

        // Pagination hesapla
        const totalItems = sortedTenders.length;
        const totalPageCount = Math.ceil(totalItems / itemsPerPage);
        setTotalPages(totalPageCount);
        
        // İlk sayfa dışında bir sayfadaysak ve toplam sayfa sayısı azaldıysa
        // (örneğin filtre değişti ve daha az sonuç var), ilk sayfaya dön
        if (currentPage > totalPageCount && totalPageCount > 0) {
          setCurrentPage(1);
        }

        setTenders(sortedTenders);
        setError(null);
      } catch (err) {
        setError(err.message || "İhaleler yüklenirken bir hata oluştu.");
        setTenders([]);
      } finally {
        setLoading(false);
      }
    };

    loadTenders();
  }, [userId, filterState, searchQuery, sortOption, currentPage]);
  
  // Sayfa değiştirme
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Durum filtresini değiştir
  const handleFilterChange = (e) => {
    const newState = e.target.value;
    setFilterState(newState);
    setCurrentPage(1); // Filtre değişince ilk sayfaya dön
  };
  
  // Sıralama değiştir
  const handleSortChange = (e) => {
    setSortOption(e.target.value);
    setCurrentPage(1); // Sıralama değişince ilk sayfaya dön
  };
  
  // Arama sorgusu değiştir
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Arama değişince ilk sayfaya dön
  };

  // İhale durumuna göre CSS sınıfı ve metni belirle
  const getTenderStatusClass = (status) => {
    switch (status) {
      case "ACTIVE":
        return {
          className: "tender-status-active",
          text: t("tenders_page.active"),
          rowClass: "active"
        };
      case "COMPLETED":
        return {
          className: "tender-status-completed",
          text: t("tenders_page.completed"),
          rowClass: "completed"
        };
      case "CANCELLED":
        return {
          className: "tender-status-cancelled",
          text: t("tenders_page.cancelled"),
          rowClass: "cancelled"
        };
      default:
        return {
          className: "",
          text: status,
          rowClass: ""
        };
    }
  };

  // Tarih formatı
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('tr-TR', options);
  };

  // İhale detaylarına git
  const handleViewDetails = (tenderId) => {
    navigate(`/tender-details/${tenderId}`);
  };
  
  // Sayfalama için ihaleleri böl
  const getCurrentPageItems = () => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return tenders.slice(indexOfFirstItem, indexOfLastItem);
  };
  
  // Sayfalayıcı oluştur
  const renderPagination = () => {
    if (totalPages <= 1) return null;
    
    const paginationItems = [];
    const maxPagesToShow = 5; // Toplam kaç sayfa numarası gösterileceği
    
    // Önceki sayfa butonu
    paginationItems.push(
      <Pagination.Prev 
        key="prev" 
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
      />
    );
    
    // Sayfa numaralarını hesapla
    let startPage = 1;
    let endPage = totalPages;
    let showStartEllipsis = false;
    let showEndEllipsis = false;
    
    if (totalPages > maxPagesToShow) {
      // Orta aralığı hesapla (ortada gösterilecek sayfa sayısı)
      const middleSize = maxPagesToShow - 2; // İlk ve son sayfalar hariç gösterilecek sayfa sayısı
      
      if (currentPage <= Math.ceil(middleSize / 2) + 1) {
        // Başlangıçtayız - ilk 1 + middleSize sayfayı göster
        startPage = 1;
        endPage = middleSize + 1;
        showEndEllipsis = true;
      } else if (currentPage >= totalPages - Math.floor(middleSize / 2) - 1) {
        // Sondayız - son middleSize + 1 sayfayı göster
        startPage = totalPages - middleSize;
        endPage = totalPages;
        showStartEllipsis = true;
      } else {
        // Ortadayız - ortada current page + sağ/sol sayfaları göster
        startPage = currentPage - Math.floor(middleSize / 2);
        endPage = currentPage + Math.floor(middleSize / 2);
        showStartEllipsis = true;
        showEndEllipsis = true;
      }
    }
    
    // İlk sayfa
    paginationItems.push(
      <Pagination.Item 
        key={1} 
        active={currentPage === 1}
        onClick={() => handlePageChange(1)}
      >
        1
      </Pagination.Item>
    );
    
    // Başlangıç ellipsis
    if (showStartEllipsis && startPage > 2) {
      paginationItems.push(<Pagination.Ellipsis key="ellipsis1" />);
    }
    
    // Orta sayfalar
    for (let i = Math.max(2, startPage); i <= Math.min(endPage, totalPages - 1); i++) {
      paginationItems.push(
        <Pagination.Item 
          key={i} 
          active={currentPage === i}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </Pagination.Item>
      );
    }
    
    // Bitiş ellipsis
    if (showEndEllipsis && endPage < totalPages - 1) {
      paginationItems.push(<Pagination.Ellipsis key="ellipsis2" />);
    }
    
    // Son sayfa (eğer 1 sayfadan fazlaysa)
    if (totalPages > 1) {
      paginationItems.push(
        <Pagination.Item 
          key={totalPages} 
          active={currentPage === totalPages}
          onClick={() => handlePageChange(totalPages)}
        >
          {totalPages}
        </Pagination.Item>
      );
    }
    
    // Sonraki sayfa butonu
    paginationItems.push(
      <Pagination.Next 
        key="next" 
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      />
    );
    
    return (
      <div className="pagination-container">
        <div className="pagination-top-row">
          <div className="pagination-info">
            {t("pagination.showing")} {Math.min((currentPage - 1) * itemsPerPage + 1, tenders.length)} - {Math.min(currentPage * itemsPerPage, tenders.length)} {t("pagination.of")} {tenders.length} {t("pagination.items")}
          </div>
          
          <div className="items-per-page">
            <span>{t("pagination.items_per_page")}: </span>
            <select 
              value={itemsPerPage} 
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="form-select items-select"
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>
        
        <Pagination>{paginationItems}</Pagination>
      </div>
    );
  };

  return (
    <div className="tender-list-area">
      <Container>
        
        <div className="filters-container">
          <Row>
            <Col lg={6} md={12} className="mb-3">
              <div className="search-filter">
                <div className="filter-label">
                  <FaSearch /> {t("tenders_page.search")}
                </div>
                <input 
                  type="text" 
                  className="form-control search-input" 
                  placeholder={t("tenders_page.search_placeholder")}
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
            </Col>
            
            <Col lg={3} md={6} className="mb-3">
              <div className="status-filter">
                <div className="filter-label">
                  <FaFilter /> {t("tenders_page.state_filter")}
                </div>
                <select 
                  id="stateFilter" 
                  value={filterState} 
                  onChange={handleFilterChange}
                  className="form-select custom-select"
                >
                  <option value="ALL">{t("tenders_page.all")}</option>
                  <option value="ACTIVE">{t("tenders_page.active")}</option>
                  <option value="COMPLETED">{t("tenders_page.completed")}</option>
                  <option value="CANCELLED">{t("tenders_page.cancelled")}</option>
                </select>
              </div>
            </Col>
            
            <Col lg={3} md={6} className="mb-3">
              <div className="sort-filter">
                <div className="filter-label">
                  <FaSort /> {t("tenders_page.sort_by")}
                </div>
                <select 
                  id="sortOption" 
                  value={sortOption} 
                  onChange={handleSortChange}
                  className="form-select custom-select"
                >
                  <option value="newest">{t("tenders_page.newest")}</option>
                  <option value="oldest">{t("tenders_page.oldest")}</option>
                  <option value="mostOffers">{t("tenders_page.most_offers")}</option>
                </select>
              </div>
            </Col>
          </Row>
          
          <Row className="align-items-center mt-3">
            <Col>
              <div className="tender-count">
                {!loading && <span>{tenders.length} {t("tenders_page.tender_count")}</span>}
              </div>
            </Col>
          </Row>
        </div>

        {loading ? (
          <div className="tender-loading">
            <div className="spinner-border text-danger" role="status">
              <span className="visually-hidden">{t("tenders_page.loading")}</span>
            </div>
          </div>
        ) : error ? (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        ) : tenders.length === 0 ? (
          <div className="tender-empty">
            <div className="empty-icon-container">
              <FaCar className="empty-icon" />
            </div>
            <h3>{t("tenders_page.empty_tenders")}</h3>
            <p>{t("tenders_page.empty_message")}</p>
            <button className="new-tender-btn gauto-theme-btn">
              <span className="btn-icon"><FaPlus /></span>
              <span className="btn-text">{t("tenders_page.start_new_tender")}</span>
            </button>
          </div>
        ) : (
          <>
            <div className="advanced-list-container">
              {getCurrentPageItems().map((tender) => {
                const statusInfo = getTenderStatusClass(tender.state);
                
                return (
                  <div key={tender.id} className={`list-item ${statusInfo.rowClass}`}>
                    <div className="status-indicator"></div>
                    
                    <div className="list-item-content">
                      <div className="list-item-header">
                        <div className="tender-title-area">
                          <h4>{tender.title}</h4>
                          <div className="tender-status-area">
                            <span className={`tender-status ${statusInfo.className}`}>
                              {statusInfo.text}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="car-model">
                        <FaCar /> {tender.brand} {tender.model}
                      </div>
                      
                      <div className="list-item-details">
                        <div className="details-group">
                          <div className="details-label">{t("tenders_page.specs")}</div>
                          <div className="list-item-specs">
                            <div className="list-spec">
                              <FaGasPump title="Yakıt Tipi" /> {tender.fuelType}
                            </div>
                            <div className="list-spec">
                              <FaCogs title="Şanzıman" /> {tender.transmission}
                            </div>
                            <div className="list-spec">
                              <FaCalendarAlt title="Kiralama Süresi" /> {tender.rentalPeriod} {t("tenders_page.month")}
                            </div>
                          </div>
                        </div>
                        
                        <div className="details-group">
                          <div className="details-label">{t("tenders_page.tender_date")}</div>
                          <div className="date-value">{formatDate(tender.startDate)}</div>
                        </div>
                        
                        <div className="details-group">
                          <div className="details-label">{t("tenders_page.offers")}</div>
                          <div className="count-badge">
                            <FaUserFriends />
                            <span>{tender.companyResponses}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="list-item-actions">
                      <button 
                        className="action-btn details-btn"
                        onClick={() => handleViewDetails(tender.id)}
                        title={t("tenders_page.details")}
                      >
                        <FaEye />
                      </button>
                      
                      {tender.state === "ACTIVE" && (
                        <button 
                          className="action-btn edit-btn"
                          title={t("tenders_page.edit")}
                        >
                          <FaPencilAlt />
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
            
            {renderPagination()}
          </>
        )}
      </Container>
    </div>
  );
};

export default TenderList; 