import React from "react";
import {
  createBrowser<PERSON>outer,
  RouterProvider,
  Outlet
} from "react-router-dom";
import ProtectedRoute from "../../components/ProtectedRoute/ProtectedRoute";
import { USER_ROLES } from "../../constants/userRoles";
import AppLayout from "../../components/AppLayout";

import Homepage from "../HomePage";
import AboutPage from "../AboutPage";
import ServicePage from "../ServicePage";
import ServiceSingle from "../ServiceDetails";
import CarListingPage from "../CarListingPage";
import CarBookingPage from "../CarBookingPage";
import GalleryPage from "../GalleryPage";
import ProductPage from "../ProductPage";
import ProductSinglePage from "../ProductSingle";
import CartPage from "../CartPage";
import Checkout from "../Checkout";
import BlogPage from "../BlogPage";
import BlogSinglePage from "../BlogSinglePage";
import ErrorPage from "../ErrorPage";
import LoginPage from "../LoginPage";
import RegisterPage from "../RegisterPage";
import ContactPage from "../ContactPage";
import ProfilePage from "../ProfilePage";
import MyBookingsPage from "../MyBookingsPage";
import TenderStartPage from "../TenderStartPage";
import MyTendersPage from "../MyTendersPage";
import TenderDetailsPage from "../TenderDetailsPage";
import VerificationPage from "../VerificationPage";
import TenderProcessDetailPage from "../TenderProcessDetailPage";
import TenderBidPage from "../TenderBidPage";
import AvailableTendersPage from "../AvailableTendersPage";

// Kullanıcı tipi tabanlı Dashboard sayfaları
import AdminDashboard from "../admin/AdminDashboard";

// Birleştirilmiş Dashboard
import Dashboard from "../Dashboard";
import UnauthorizedPage from "../UnauthorizedPage";

// React Router v7 için gelecek bayrakları (future flags)
const routerOptions = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

// AppLayout wrapper ile sayfaları render eden yardımcı fonksiyon
// Bu fonksiyon sayfalara props geçmemizi sağlar
const renderWithProps = (Component, props = {}) => {
  return <Component {...props} />;
};

// React Router v7 gelecek bayraklarını etkinleştiren router oluşturuyoruz
const router = createBrowserRouter([
  {
    path: "/",
    element: <AppLayout />,
    children: [
      {
        path: "",
        element: <Homepage />
      },
      {
        path: "home",
        element: <Homepage />
      },
      // Genel sayfalar
      {
        path: "about",
        element: <AboutPage />
      },
      {
        path: "service",
        element: <ServicePage />
      },
      {
        path: "service-single",
        element: <ServiceSingle />
      },
      {
        path: "car-listing",
        element: <CarListingPage />
      },
      {
        path: "gallery",
        element: <GalleryPage />
      },
      {
        path: "product",
        element: <ProductPage />
      },
      {
        path: "product-single",
        element: <ProductSinglePage />
      },
      {
        path: "blog",
        element: <BlogPage />
      },
      {
        path: "blog-single",
        element: <BlogSinglePage />
      },
      {
        path: "contact",
        element: <ContactPage />
      },
      // İhale Süreci Detay Sayfası
      {
        path: "ihale-sureci-detay",
        element: <TenderProcessDetailPage />
      },
      // Kullanıcı Sayfaları (CUSTOMER rolü)
      {
        path: "dashboard",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><Dashboard /></ProtectedRoute>
      },
      {
        path: "tenders/start",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><TenderStartPage /></ProtectedRoute>
      },
      {
        path: "tender-details",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><TenderDetailsPage /></ProtectedRoute>
      },
      {
        path: "my-tenders",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><MyTendersPage /></ProtectedRoute>
      },
      {
        path: "tender-bid",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><TenderBidPage /></ProtectedRoute>
      },
      {
        path: "unauthorized",
        element: <UnauthorizedPage />
      },
      // Kimlik doğrulama sayfaları
      {
        path: "login",
        element: <LoginPage />
      },
      {
        path: "register",
        element: <RegisterPage />
      },
      // Doğrulama sayfası
      {
        path: "verification",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><VerificationPage /></ProtectedRoute>
      },
      // Yönetici (ADMIN) Sayfaları
      {
        path: "admin/dashboard",
        element: <ProtectedRoute permissions={[USER_ROLES.ADMIN.id]}><AdminDashboard /></ProtectedRoute>
      },
      // Müşteri Hizmetleri (CUSTOMER_SERVICE) Sayfaları
      {
        path: "cs/dashboard",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER_SERVICE.id]}><AdminDashboard /></ProtectedRoute>
      },
      // Kullanıcı hesap işlemleri
      {
        path: "profile",
        element: <ProtectedRoute><ProfilePage /></ProtectedRoute>
      },
      // Rezervasyon işlemleri
      {
        path: "car-booking",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><CarBookingPage /></ProtectedRoute>
      },
      {
        path: "my-bookings",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><MyBookingsPage /></ProtectedRoute>
      },
      {
        path: "cart",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><CartPage /></ProtectedRoute>
      },
      {
        path: "checkout",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><Checkout /></ProtectedRoute>
      },
      {
        path: "available-tenders",
        element: <ProtectedRoute permissions={[USER_ROLES.CUSTOMER.id]}><AvailableTendersPage /></ProtectedRoute>
      },
      // ESKİ ROTALAR KALDIRILDI
      // 404 Sayfası
      {
        path: "*",
        element: <ErrorPage />
      }
    ]
  }
], routerOptions);

const AllRoute = () => {
  return (
    <div>
      <RouterProvider router={router} future={routerOptions.future} />
    </div>
  );
};

export default AllRoute;
