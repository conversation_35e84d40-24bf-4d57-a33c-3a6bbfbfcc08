import React, { useState } from 'react';
import { Tab, Nav, <PERSON><PERSON>, <PERSON><PERSON>, Table, Card } from 'react-bootstrap';
import { FaExclamationTriangle, FaFileAlt, FaEye, FaHandshake, FaCheck, FaTimes } from 'react-icons/fa';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import './style.css';

/**
 * Teklif yönetimi bileşeni
 * İhale taleplerini ve teklifleri gösterir
 */
const TenderManagement = ({ data }) => {
  const [activeTab, setActiveTab] = useState('new');
  const { t } = useTranslation();
  const navigate = useNavigate();

  // İşlem fonksiyonları
  const handleViewDocuments = (id) => {
    // Evrak inceleme sayfasına yönlendir
    navigate(`/company/tender/${id}?tab=documents`);
  };

  const handleMakeOffer = (id) => {
    // Teklif verme formuna yönlendir
    navigate(`/company/tender/${id}?tab=createBid`);
  };

  const handleCreateContract = (id) => {
    // Sözleşme oluşturma formuna yönlendir
    navigate(`/company/contract/create/${id}`);
  };

  if (!data) {
    return (
      <div className="text-center p-4">
        <p>{t('tender.data_loading_error')}</p>
      </div>
    );
  }

  return (
    <div className="tender-management">
      <Tab.Container defaultActiveKey={activeTab} onSelect={(k) => setActiveTab(k)}>
        <div className="mb-4">
          <Nav variant="pills" className="tender-nav">
            <Nav.Item>
              <Nav.Link eventKey="new" className="d-flex align-items-center">
                {t('tender.tabs.new_requests')}
                {data.newTenders && data.newTenders.length > 0 && (
                  <Badge bg="danger" pill className="ms-2">{data.newTenders.length}</Badge>
                )}
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="pending">{t('tender.tabs.pending_offers')}</Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="approved">{t('tender.tabs.approved_offers')}</Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="rejected">{t('tender.tabs.rejected_offers')}</Nav.Link>
            </Nav.Item>
          </Nav>
        </div>

        <Tab.Content>
          <Tab.Pane eventKey="new">
            <Card className="border-0 shadow-sm mb-4">
              <Card.Body className="p-0">
                <div className="alert alert-info m-3">
                  <FaExclamationTriangle className="me-2" />
                  {t('tender.response_time_warning')}
                </div>
                <div className="table-responsive">
                  <Table hover className="align-middle tender-table mb-0">
                    <thead className="bg-light">
                      <tr>
                        <th className="ps-4">{t('tender.table.request_no')}</th>
                        <th>{t('tender.table.customer')}</th>
                        <th>{t('tender.table.date')}</th>
                        <th>{t('tender.table.vehicle_count')}</th>
                        <th>{t('tender.table.segment')}</th>
                        <th>{t('tender.table.duration')}</th>
                        <th>{t('tender.table.remaining_time')}</th>
                        <th className="text-center">{t('tender.table.actions')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.newTenders && data.newTenders.length > 0 ? data.newTenders.map(tender => (
                        <tr key={tender.id}>
                          <td className="ps-4">
                            <div className="d-flex align-items-center">
                              <span className="text-primary fw-medium">{tender.id}</span>
                              {tender.documents && <FaFileAlt className="ms-2 text-success" title={t('tender.documents.complete')} />}
                            </div>
                          </td>
                          <td className="fw-medium">{tender.customerName}</td>
                          <td>{tender.startDate}</td>
                          <td className="text-center">{tender.vehicleCount}</td>
                          <td>
                            <Badge className="brand-badge">{tender.segment}</Badge>
                          </td>
                          <td>{tender.duration}</td>
                          <td>
                            <Badge bg={
                              tender.remainingTime.includes(t('common.time.hour')) ? 
                                parseInt(tender.remainingTime) < 6 ? 'danger' : 'warning' 
                                : 'info'
                            }>
                              {tender.remainingTime}
                            </Badge>
                          </td>
                          <td className="text-center">
                            <Button 
                              variant="brand" 
                              size="sm" 
                              className="rounded-pill px-3" 
                              onClick={() => handleMakeOffer(tender.id)}
                            >
                              <FaHandshake className="me-2" />
                              {t('tender.actions.make_offer')}
                            </Button>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="8" className="text-center py-4">
                            <p className="text-muted mb-0">{t('tender.no_new_requests')}</p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Tab.Pane>

          <Tab.Pane eventKey="pending">
            <Card className="border-0 shadow-sm mb-4">
              <Card.Body className="p-0">
                <div className="table-responsive">
                  <Table hover className="align-middle tender-table mb-0">
                    <thead className="bg-light">
                      <tr>
                        <th className="ps-4">{t('tender.table.offer_no')}</th>
                        <th>{t('tender.table.customer')}</th>
                        <th>{t('tender.table.date')}</th>
                        <th>{t('tender.table.vehicle_count')}</th>
                        <th>{t('tender.table.segment')}</th>
                        <th>{t('tender.table.duration')}</th>
                        <th>{t('tender.table.offer_amount')}</th>
                        <th>{t('tender.table.status')}</th>
                        <th className="text-center">{t('tender.table.actions')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.pendingTenders && data.pendingTenders.length > 0 ? data.pendingTenders.map(tender => (
                        <tr key={tender.id}>
                          <td className="ps-4">
                            <span className="text-primary fw-medium">{tender.id}</span>
                          </td>
                          <td className="fw-medium">{tender.customerName}</td>
                          <td>{tender.startDate}</td>
                          <td className="text-center">{tender.vehicleCount}</td>
                          <td>
                            <Badge className="brand-badge">{tender.segment}</Badge>
                          </td>
                          <td>{tender.duration}</td>
                          <td className="fw-bold">{tender.offerAmount}</td>
                          <td>
                            <Badge bg="warning" className="status-badge">
                              {tender.statusText || t('tender.status.customer_reviewing')}
                            </Badge>
                          </td>
                          <td className="text-center">
                            <Button
                              variant="outline-brand"
                              size="sm"
                              className="rounded-pill px-3"
                              onClick={() => navigate(`/company/tender/${tender.id}`)}
                            >
                              <FaEye className="me-1" />
                              {t('tender.actions.view_details')}
                            </Button>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="9" className="text-center py-4">
                            <p className="text-muted mb-0">{t('tender.no_pending_offers')}</p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Tab.Pane>

          <Tab.Pane eventKey="approved">
            <Card className="border-0 shadow-sm mb-4">
              <Card.Body className="p-0">
                <div className="table-responsive">
                  <Table hover className="align-middle tender-table mb-0">
                    <thead className="bg-light">
                      <tr>
                        <th className="ps-4">{t('tender.table.offer_no')}</th>
                        <th>{t('tender.table.customer')}</th>
                        <th>{t('tender.table.date')}</th>
                        <th>{t('tender.table.vehicle_count')}</th>
                        <th>{t('tender.table.segment')}</th>
                        <th>{t('tender.table.duration')}</th>
                        <th>{t('tender.table.offer_amount')}</th>
                        <th>{t('tender.table.approval_date')}</th>
                        <th className="text-center">{t('tender.table.actions')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.approvedTenders && data.approvedTenders.length > 0 ? data.approvedTenders.map(tender => (
                        <tr key={tender.id}>
                          <td className="ps-4">
                            <span className="text-primary fw-medium">{tender.id}</span>
                          </td>
                          <td className="fw-medium">{tender.customerName}</td>
                          <td>{tender.startDate}</td>
                          <td className="text-center">{tender.vehicleCount}</td>
                          <td>
                            <Badge className="brand-badge">{tender.segment}</Badge>
                          </td>
                          <td>{tender.duration}</td>
                          <td className="fw-bold">{tender.offerAmount}</td>
                          <td>
                            <span className="text-success">
                              <FaCheck className="me-1" />{tender.approvalDate}
                            </span>
                          </td>
                          <td className="text-center">
                            <Button
                              variant="brand"
                              size="sm"
                              className="rounded-pill px-3"
                              onClick={() => handleCreateContract(tender.id)}
                            >
                              <FaFileAlt className="me-1" />
                              {t('tender.actions.create_contract')}
                            </Button>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="9" className="text-center py-4">
                            <p className="text-muted mb-0">{t('tender.no_approved_offers')}</p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Tab.Pane>

          <Tab.Pane eventKey="rejected">
            <Card className="border-0 shadow-sm mb-4">
              <Card.Body className="p-0">
                <div className="table-responsive">
                  <Table hover className="align-middle tender-table mb-0">
                    <thead className="bg-light">
                      <tr>
                        <th className="ps-4">{t('tender.table.offer_no')}</th>
                        <th>{t('tender.table.customer')}</th>
                        <th>{t('tender.table.date')}</th>
                        <th>{t('tender.table.vehicle_count')}</th>
                        <th>{t('tender.table.segment')}</th>
                        <th>{t('tender.table.duration')}</th>
                        <th>{t('tender.table.offer_amount')}</th>
                        <th>{t('tender.table.rejection_reason')}</th>
                        <th className="text-center">{t('tender.table.actions')}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.rejectedTenders && data.rejectedTenders.length > 0 ? data.rejectedTenders.map(tender => (
                        <tr key={tender.id}>
                          <td className="ps-4">
                            <span className="text-primary fw-medium">{tender.id}</span>
                          </td>
                          <td className="fw-medium">{tender.customerName}</td>
                          <td>{tender.startDate}</td>
                          <td className="text-center">{tender.vehicleCount}</td>
                          <td>
                            <Badge className="brand-badge">{tender.segment}</Badge>
                          </td>
                          <td>{tender.duration}</td>
                          <td className="fw-bold">{tender.offerAmount}</td>
                          <td>
                            <span className="text-danger">
                              <FaTimes className="me-1" />
                              {tender.rejectionReason}
                            </span>
                          </td>
                          <td className="text-center">
                            <Button
                              variant="outline-brand"
                              size="sm"
                              className="rounded-pill px-3"
                              onClick={() => navigate(`/company/tender/${tender.id}`)}
                            >
                              <FaEye className="me-1" />
                              {t('tender.actions.view_details')}
                            </Button>
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan="9" className="text-center py-4">
                            <p className="text-muted mb-0">{t('tender.no_rejected_offers')}</p>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </div>
              </Card.Body>
            </Card>
          </Tab.Pane>
        </Tab.Content>
      </Tab.Container>
    </div>
  );
};

TenderManagement.propTypes = {
  data: PropTypes.shape({
    newTenders: PropTypes.array,
    pendingTenders: PropTypes.array,
    approvedTenders: PropTypes.array,
    rejectedTenders: PropTypes.array
  })
};

export default TenderManagement; 