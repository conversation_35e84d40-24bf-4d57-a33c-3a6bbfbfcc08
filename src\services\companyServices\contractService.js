import api, { useMockAPI } from '../api';
import contractsData from '../../mockData/contracts';

/**
 * Sözleşme yönetimi için API servisi
 */
class ContractService {
  /**
   * Tüm sözleşmeleri getir
   * @param {Object} params - Filtre parametreleri
   * @returns {Promise} - API yanıtı
   */
  async getContracts(params = {}) {
    try {
      // Mock API kullanımı aktifse mock veri kullan
      if (useMockAPI) {
        // Filtreleme işlemleri (gerekirse)
        let filteredContracts = [...contractsData];
        
        if (params.status) {
          filteredContracts = filteredContracts.filter(
            contract => contract.status === params.status
          );
        }
        
        // API yanıtını simüle et
        return {
          data: filteredContracts,
          status: 200,
          message: 'Sözleşmeler başarıyla alındı'
        };
      }
      
      // Gerçek API çağrısı
      const response = await api.get('/contracts', { params });
      return response.data;
    } catch (error) {
      throw this._handleError(error);
    }
  }

  /**
   * Belirli bir sözleşmeyi getir
   * @param {string} id - Sözleşme ID'si
   * @returns {Promise} - API yanıtı
   */
  async getContractById(id) {
    try {
      // Mock API kullanımı aktifse mock veri kullan
      if (useMockAPI) {
        const contract = contractsData.find(c => c.id === id);
        
        if (!contract) {
          throw new Error('Sözleşme bulunamadı');
        }
        
        // API yanıtını simüle et
        return {
          data: contract,
          status: 200,
          message: 'Sözleşme başarıyla alındı'
        };
      }
      
      // Gerçek API çağrısı
      const response = await api.get(`/contracts/${id}`);
      return response.data;
    } catch (error) {
      throw this._handleError(error);
    }
  }

  /**
   * Yeni sözleşme oluştur
   * @param {Object} contractData - Sözleşme verileri
   * @returns {Promise} - API yanıtı
   */
  async createContract(contractData) {
    try {
      // Gerçek API çağrısı
      const response = await api.post('/contracts', contractData);
      return response.data;
    } catch (error) {
      throw this._handleError(error);
    }
  }

  /**
   * Sözleşmeyi güncelle
   * @param {string} id - Sözleşme ID'si
   * @param {Object} contractData - Güncellenecek veriler
   * @returns {Promise} - API yanıtı
   */
  async updateContract(id, contractData) {
    try {
      // Gerçek API çağrısı
      const response = await api.put(`/contracts/${id}`, contractData);
      return response.data;
    } catch (error) {
      throw this._handleError(error);
    }
  }

  /**
   * Sözleşme yükle
   * @param {string} id - Sözleşme ID'si
   * @param {File} file - Yüklenecek dosya
   * @returns {Promise} - API yanıtı
   */
  async uploadContractFile(id, file) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      // Gerçek API çağrısı
      const response = await api.post(`/contracts/${id}/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw this._handleError(error);
    }
  }

  /**
   * Hata yönetimi
   * @param {Error} error - Hata nesnesi
   * @returns {Error} - İşlenmiş hata
   * @private
   */
  _handleError(error) {
    const errorMsg = 
      error.response?.data?.message || 
      error.message || 
      'Sözleşme servisinde bir hata oluştu';
    
    console.error('Contract Service Error:', errorMsg);
    
    return {
      message: errorMsg,
      status: error.response?.status || 500,
      error: true
    };
  }
}

export default new ContractService(); 