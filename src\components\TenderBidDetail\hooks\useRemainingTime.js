import { useState, useEffect } from 'react';

/**
 * Verilen bitiş tarihine göre kalan süreyi hesaplayan ve formatlayan hook
 * @param {string} endDate - Bitiş tarihi (ISO formatında)
 * @returns {string|null} - Formatlanmış kalan süre
 */
const useRemainingTime = (endDate) => {
  const [remainingTime, setRemainingTime] = useState(null);
  
  useEffect(() => {
    if (!endDate) return;

    const endTime = new Date(endDate).getTime();
    let timeInterval;
    
    const updateRemainingTime = () => {
      const currentTime = new Date().getTime();
      const difference = endTime - currentTime;
      
      if (difference <= 0) {
        setRemainingTime("Süre doldu");
        if (timeInterval) {
          clearInterval(timeInterval);
        }
      } else {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);
        
        // Gün varsa onu da ekleyelim
        if (days > 0) {
          setRemainingTime(`${days}g ${hours}s ${minutes}d ${seconds}sn`);
        } else {
          setRemainingTime(`${hours}s ${minutes}d ${seconds}sn`);
        }
      }
    };
    
    updateRemainingTime();
    timeInterval = setInterval(updateRemainingTime, 1000);
    
    return () => {
      if (timeInterval) {
        clearInterval(timeInterval);
      }
    };
  }, [endDate]);
  
  return remainingTime;
};

export default useRemainingTime; 