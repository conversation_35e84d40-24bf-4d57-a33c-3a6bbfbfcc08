@import url('../../styles/colors.css');

.verification-container {
  background-color: #f9f9f9;
  padding: 50px 0;
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verification-card {
  background-color: var(--primary-color);
  color: #fff;
  border-radius: 10px;
  overflow: hidden;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  margin: 0 auto;
}

.verification-card .card-header {
  background-color: var(--primary-color);
  color: #fff;
  padding: 15px 20px;
  border-bottom: none;
}

.verification-card .card-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.verification-card .card-body {
  padding: 30px;
}

.verification-info h5 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.document-upload-section h4,
.findex-section h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.document-upload-section p,
.findex-section p {
  margin-bottom: 20px;
  color: #666;
  font-size: 15px;
}

.upload-box {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  position: relative;
  transition: all 0.3s ease;
}

.upload-box:hover {
  border-color: var(--primary-color);
  background-color: rgba(255, 98, 0, 0.05);
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 2;
}

.upload-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.upload-icon {
  font-size: 40px;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.selected-file {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: #e9ecef;
  border-radius: 5px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.warning-icon {
  font-size: 24px;
  color: #ffc107;
}

.findex-checkbox {
  margin-top: 20px;
}

.findex-checkbox label {
  font-weight: 500;
  cursor: pointer;
}

.verification-submit-btn {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  padding: 10px 25px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s;
}

.verification-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 98, 0, 0.3);
}

/* Doğrulanmış kullanıcı ekranı */
.already-verified {
  text-align: center;
}

.verified-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #28a745;
  color: #fff;
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.already-verified h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #28a745;
}

.already-verified p {
  font-size: 16px;
  color: #666;
}

.verification-form-group {
  margin-bottom: 20px;
}

.verification-form-group .verification-form-control {
  width: 100%;
  padding: 10px 15px;
  font-size: 16px;
  border: 2px dashed #ddd;
  border-radius: 5px;
  background-color: #f8f9fa;
  transition: all 0.3s;
}

.verification-form-group .verification-form-control:focus {
  outline: none;
  border-color: var(--primary-color);
}

.resend-code-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: underline;
  font-weight: 500;
  transition: all 0.3s;
}

@media (max-width: 768px) {
  .verification-card .card-body {
    padding: 20px;
  }
  
  .upload-box {
    padding: 20px;
  }
  
  .verification-submit-btn {
    width: 100%;
  }
} 