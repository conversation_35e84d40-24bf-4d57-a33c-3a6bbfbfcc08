/* <PERSON><PERSON> s<PERSON>re bi<PERSON> stiller */
.remaining-time {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.25s ease;
  margin-bottom: 0;
  position: relative;
}

.remaining-time:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.remaining-time-header {
  position: relative;
  background-color: transparent;
  color: #334155;
  text-align: left;
  padding: 12px 12px 6px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: auto;
  border-bottom: none;
}

.remaining-time-header h6 {
  font-size: 0.8rem;
  margin: 0;
  font-weight: 500;
  color: #64748b;
  letter-spacing: 0.5px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  text-transform: none;
}

.remaining-time-header h6 svg {
  margin-right: 6px;
  font-size: 0.9rem;
  color: #dc2626;
}

.remaining-time-body {
  padding: 0 12px 12px;
  text-align: center;
}

.remaining-time-counter {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 0;
  flex-wrap: wrap;
}

.time-unit {
  background-color: #fef2f2;
  border-radius: 6px;
  padding: 8px 6px;
  min-width: 52px;
  text-align: center;
  position: relative;
  border: 1px solid rgba(220, 38, 38, 0.1);
}

.time-unit:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #dc2626;
}

.time-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #dc2626;
  line-height: 1;
  margin-bottom: 4px;
}

.time-label {
  font-size: 0.65rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.remaining-time-text {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 8px;
  font-style: italic;
} 