import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Con<PERSON>er, <PERSON><PERSON>, Row, Col, Pagination } from "react-bootstrap";
import { FaPlus, FaSearch, FaSort, FaEye } from "react-icons/fa";

import PageTitle from "../../components/PageTitle";
import tenderService from "../../services/tenderService";
import "./style.css";

const TENDER_STATUSES = [
  { id: 0, label: "tenders_page.all" },
  { id: 100, label: "tenders_page.draft" },
  { id: 120, label: "tenders_page.review" },
  { id: 300, label: "tenders_page.live" },
  { id: 320, label: "tenders_page.relive" },
  { id: 400, label: "tenders_page.reject" },
  { id: 500, label: "tenders_page.expired" },
  { id: 600, label: "tenders_page.cancelled" },
  { id: 700, label: "tenders_page.completed" },
];

const SORT_OPTIONS = [
  { value: "createddate", label: "tenders_page.sort_created_date" },
  { value: "startdate", label: "tenders_page.sort_start_date" },
  { value: "offercount", label: "tenders_page.sort_offer_count" },
  { value: "minimumoffer", label: "tenders_page.sort_minimum_offer" },
];

const MyTendersPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated } = useSelector((state) => state.auth);

  // State'ler
  const [tenders, setTenders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [statusId, setStatusId] = useState(0); // 0: Tümü
  const [search, setSearch] = useState("");
  const [sort, setSort] = useState("createddate");
  const [isAscending, setIsAscending] = useState(true);

  // Giriş yapmamış kullanıcıları giriş sayfasına yönlendir
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  // API'dan ihaleleri çek
  useEffect(() => {
    const fetchTenders = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await tenderService.getTenders({
          pageNumber,
          pageSize,
          statusId,
          sort: sort || "createddate",
          isAscending,
          search: search || null,
        });
        if (response.data && response.data.result) {
          setTenders(response.data.result);
          setTotalCount(response.data.totalCount || 0);
        } else {
          setTenders([]);
          setTotalCount(0);
        }
      } catch (err) {
        setError("İhaleler yüklenirken hata oluştu.");
        setTenders([]);
        setTotalCount(0);
      } finally {
        setLoading(false);
      }
    };
    fetchTenders();
  }, [pageNumber, pageSize, statusId, sort, isAscending, search]);

  // İhale Başlat sayfasına yönlendir
  const handleStartTender = () => {
    navigate("/tenders/start");
  };

  // Sayfa değiştir
  const handlePageChange = (newPage) => {
    setPageNumber(newPage);
  };

  // Sıralama yönünü değiştir
  const handleSortDirection = () => {
    setIsAscending((prev) => !prev);
  };

  return (
    <>
      <PageTitle
        pageTitle={t("tenders_page.title")}
        pagesub={t("tenders_page.subtitle")}
      />
      <div className="tenders-page-container">
        <Container>
          <Row className="align-items-center mb-3">
            <Col xs={12} md="auto" className="mb-2 mb-md-0">
              <Button
                className="start-tender-btn w-100"
                onClick={handleStartTender}
                style={{ minWidth: 180 }}
              >
                <FaPlus className="btn-icon" /> {t("tenders_page.start_tender")}
              </Button>
            </Col>
            <Col xs={12} md>
              <div className="filter-card shadow-sm p-3 mb-4 bg-white rounded-4">
                <Row className="gy-2 gx-3 align-items-center">
                  <Col xs={12} md={4}>
                    <label className="form-label mb-1">{t("tenders_page.search")}</label>
                    <div className="input-group">
                      <span className="input-group-text"><FaSearch /></span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder={t("tenders_page.search_placeholder")}
                        value={search}
                        onChange={(e) => {
                          setSearch(e.target.value);
                          setPageNumber(1);
                        }}
                      />
                    </div>
                  </Col>
                  <Col xs={6} md={2}>
                    <label className="form-label mb-1">{t("tenders_page.state_filter")}</label>
                    <select
                      className="form-select"
                      value={statusId}
                      onChange={(e) => {
                        const value = Number(e.target.value);
                        setStatusId(value === 0 ? 0 : value);
                        setPageNumber(1);
                      }}
                    >
                      {TENDER_STATUSES.map((status) => (
                        <option key={status.id} value={status.id}>
                          {t(status.label)}
                        </option>
                      ))}
                    </select>
                  </Col>
                  <Col xs={6} md={4}>
                    <label className="form-label mb-1">{t("tenders_page.sort_by")}</label>
                    <select
                      className="form-select"
                      value={sort}
                      onChange={(e) => {
                        setSort(e.target.value);
                        setPageNumber(1);
                      }}
                    >
                      {SORT_OPTIONS.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                          {t(opt.label)}
                        </option>
                      ))}
                    </select>
                  </Col>
                  <Col xs={12} md={2}>
                    <label className="form-label mb-1 d-block">&nbsp;</label>
                    <Button
                      variant={isAscending ? "outline-secondary" : "outline-dark"}
                      className="sort-toggle-btn d-flex align-items-center gap-1 w-100"
                      onClick={handleSortDirection}
                      title={isAscending ? t("tenders_page.ascending") : t("tenders_page.descending")}
                      style={{ height: 38, minWidth: 60, padding: "0 6px" }}
                    >
                      <FaSort style={{ transform: isAscending ? "scaleY(1)" : "scaleY(-1)" }} />
                      <span style={{ fontSize: 13 }}>
                        {isAscending ? t("tenders_page.ascending") : t("tenders_page.descending")}
                      </span>
                    </Button>
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>

          {loading ? (
            <div className="tender-loading text-center py-5">
              <span className="spinner-border text-danger" role="status"></span>
            </div>
          ) : error ? (
            <div className="alert alert-danger" role="alert">
              {error}
            </div>
          ) : tenders.length === 0 ? (
            <div className="tender-empty text-center py-5">
              <h3>{t("tenders_page.empty_tenders")}</h3>
              <p>{t("tenders_page.empty_message")}</p>
              <Button className="start-tender-btn mt-3" onClick={handleStartTender}>
                <FaPlus className="btn-icon" /> {t("tenders_page.start_new_tender")}
              </Button>
            </div>
          ) : (
            <>
              <div className="advanced-list-container mb-4" style={{maxHeight: 500, overflowY: 'auto'}}>
                {tenders.map((tender) => (
                  <div
                    key={tender.id}
                    className="list-item d-flex flex-column flex-md-row align-items-start align-items-md-center gap-3 mb-3 p-3 bg-white rounded shadow-sm"
                  >
                    <img
                      src={tender.model?.picture || '/assets/no-image.png'}
                      alt={tender.model?.name || ''}
                      className="tender-img flex-shrink-0"
                      style={{ width: 90, height: 60, objectFit: 'cover', borderRadius: 8, background: '#f8f8f8' }}
                    />
                    <div className="flex-grow-1 w-100">
                      <div className="d-flex align-items-center gap-2 mb-1">
                        <span className="fw-bold">{tender.model?.brandName}</span>
                        <span className="text-secondary">{tender.model?.name}</span>
                        <span className="badge bg-light text-dark ms-2">{tender.tenderStatus?.statusName}</span>
                      </div>
                      <div className="text-muted small mb-1">
                        {t('tenders_page.start_date')}: {tender.startDate ? new Date(tender.startDate).toLocaleDateString('tr-TR') : '-'}
                        {" | "}
                        {t('tender_details.end_date')}: {tender.endDate ? new Date(tender.endDate).toLocaleDateString('tr-TR') : '-'}
                      </div>
                      <div className="d-flex gap-3 flex-wrap">
                        <span className="badge bg-primary bg-opacity-10 text-white">
                          {t('tenders_page.vehicle_count')}: {tender.vehicleQuantity}
                        </span>
                        <span className="badge bg-success bg-opacity-10 text-success">
                          {t('tenders_page.offers')}: {tender.offerCount}
                        </span>
                        <span className="badge bg-warning bg-opacity-10 text-warning">
                          {t('tenders_page.minimum_offer')}: {tender.minimumOffer !== null ? `₺${tender.minimumOffer}` : t('tenders_page.no_offer')}
                        </span>
                      </div>
                    </div>
                    <div className="mt-2 mt-md-0 ms-md-3 w-100 w-md-auto d-flex justify-content-md-end">
                      <Button
                        variant="outline-primary"
                        className="detail-btn-responsive"
                        onClick={() => navigate('/tender-details', { state: { tenderId: tender.id } })}
                      >
                        <FaEye /> {t('tenders_page.details')}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              {/* Sayfalama */}
              <div className="pagination-wrapper d-flex justify-content-center align-items-center gap-2">
                <Pagination>
                  <Pagination.Prev
                    onClick={() => handlePageChange(pageNumber - 1)}
                    disabled={pageNumber === 1}
                  />
                  {Array.from({ length: Math.ceil(totalCount / pageSize) }, (_, i) => i + 1).map((page) => (
                    <Pagination.Item
                      key={page}
                      active={page === pageNumber}
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </Pagination.Item>
                  ))}
                  <Pagination.Next
                    onClick={() => handlePageChange(pageNumber + 1)}
                    disabled={pageNumber === Math.ceil(totalCount / pageSize) || totalCount === 0}
                  />
                </Pagination>
                <div className="d-flex align-items-center ms-3">
                  <span className="me-2">{t("pagination.items_per_page")}:</span>
                  <select
                    className="form-select"
                    style={{ width: 70 }}
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value));
                      setPageNumber(1);
                    }}
                  >
                    {[5, 10, 20, 50, 100].map((size) => (
                      <option key={size} value={size}>{size}</option>
                    ))}
                  </select>
                </div>
              </div>
            </>
          )}
        </Container>
      </div>
    </>
  );
};

export default MyTendersPage; 