/**
 * JWT token yardımcı fonksiyonları
 */

/**
 * JWT token'ı çözümler
 * @param {string} token - JWT token
 * @returns {Object|null} - Çözümlenmiş token içeriği veya hata durumunda null
 */
export const parseJwt = (token) => {
  try {
    // Token formatını kontrol et
    if (!token || typeof token !== 'string') {
      return null;
    }

    // "Bearer " önekini kaldır (birden fazla olabilir)
    while (token.startsWith('Bearer ')) {
      token = token.slice(7);
    }

    // Base64Url formatındaki token'ı çözümle
    const base64Url = token.split('.')[1];
    if (!base64Url) {
      return null;
    }

    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );

    const parsedToken = JSON.parse(jsonPayload);

    return parsedToken;
  } catch (error) {
    return null;
  }
};

/**
 * JWT token'dan kullanıcı bilgilerini çıkarır
 * @param {string} token - JWT token
 * @returns {Object} - Kullanıcı bilgileri
 */
export const extractUserFromToken = (token) => {
  const tokenData = parseJwt(token);

  if (!tokenData) {
    return null;
  }

  // Token içeriğinden kullanıcı bilgilerini çıkar
  return {
    name: tokenData['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/sid'] || '',
    email: tokenData.email || '',
    companyId: tokenData.companyid || null,
    roles: Array.isArray(tokenData.role) ? tokenData.role : [tokenData.role],
    // Token süresi bilgileri
    exp: tokenData.exp,
    iat: tokenData.iat
  };
};

/**
 * Kullanıcı rollerinden roleId değerini belirler
 * @param {Array} roles - Kullanıcı rolleri
 * @returns {number} - Rol ID'si
 */
export const determineRoleId = (roles) => {
  // Bu fonksiyon artık constants/userRoles.js içindeki getRoleIdFromApiRoles fonksiyonunu kullanmalı
  // Geriye uyumluluk için burada tutuldu
  if (!roles || !Array.isArray(roles)) {
    return 30; // Varsayılan olarak CUSTOMER rolü
  }

  // Role10 rolü varsa ADMIN olarak kabul et
  if (roles.includes('Role10')) {
    return 10; // ADMIN rolü
  }

  // Role20 rolü varsa CUSTOMER_SERVICE olarak kabul et
  if (roles.includes('Role20')) {
    return 20; // CUSTOMER_SERVICE rolü
  }

  // Role30 rolü varsa CUSTOMER olarak kabul et
  if (roles.includes('Role30')) {
    return 30; // CUSTOMER rolü
  }

  return 30; // Varsayılan olarak CUSTOMER rolü
};
