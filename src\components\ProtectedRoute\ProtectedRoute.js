import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { USER_ROLES } from '../../constants/userRoles';

/**
 * Ko<PERSON>a gerektiren rotalar için bir wrapper bileşen
 * @param {Object} props
 * @param {React.ReactNode} props.children - <PERSON><PERSON><PERSON>
 * @param {Array<number>} props.permissions - İzin verilen rol ID'leri dizisi
 * @returns {React.ReactNode}
 */
const ProtectedRoute = ({ children, permissions = [] }) => {
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const location = useLocation();

  // Kullanıcı giriş yapmamışsa login sayfasına yönlendir
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Eğer permissions boşsa, tüm giriş yapmış kullanıcılara izin ver
  if (permissions.length === 0) {
    return children;
  }

  // Kullanıcı giriş yapmış ama izin verilen yetkisi yoksa yetkisiz sayfasına yönlendir
  if (!permissions.includes(user.roleId)) {
    return <Navigate to="/unauthorized" replace />;
  }

  // Kullanıcı giriş yapmış ve doğru yetkiye sahipse, korumalı içeriği göster
  return children;
};

export default ProtectedRoute; 