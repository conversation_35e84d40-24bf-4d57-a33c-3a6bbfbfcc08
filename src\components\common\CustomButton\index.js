import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import './style.css';

/**
 * CustomButton - Projedeki tüm butonlar için kullanılacak standart bileşen
 * 
 * @param {Object} props
 * @param {string} props.variant - Butonun tipi: "primary", "secondary", "outline", "white" vb.
 * @param {string} props.size - Butonun boyutu: "sm", "md", "lg"
 * @param {boolean} props.fullWidth - Butonun tam genişlikte olup olmayacağı
 * @param {string} props.to - Link olarak kullanılacaksa hedef URL (isteğe bağlı)
 * @param {string} props.type - Button type (button, submit, reset)
 * @param {boolean} props.disabled - Buton devre dışı mı?
 * @param {function} props.onClick - Tıkla<PERSON> olayı işleyicisi (isteğe bağlı)
 * @param {Node} props.children - But<PERSON> içeriği
 * @param {string} props.className - Ek CSS sınıfları (isteğe bağlı)
 * @param {Object} props.icon - Buton içindeki ikon (isteğe bağlı)
 * @param {boolean} props.iconPosition - İkon pozisyonu ("left" veya "right", varsayılan: "left")
 * @returns {React.ReactElement}
 */
const CustomButton = ({
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  to,
  type = 'button',
  disabled = false,
  onClick,
  children,
  className = '',
  icon,
  iconPosition = 'left',
  ...rest
}) => {
  // Temel CSS sınıflarını oluştur
  const buttonClasses = [
    'custom-btn',
    `btn-${variant}`,
    `btn-${size}`,
    fullWidth ? 'btn-full-width' : '',
    className
  ].filter(Boolean).join(' ');

  // İkon içeriğini oluştur
  const renderIcon = () => {
    if (!icon) return null;
    return <span className={`btn-icon ${iconPosition === 'right' ? 'icon-right' : 'icon-left'}`}>{icon}</span>;
  };

  // Buton içeriğini oluştur
  const buttonContent = (
    <>
      {iconPosition === 'left' && renderIcon()}
      <span className="btn-text">{children}</span>
      {iconPosition === 'right' && renderIcon()}
    </>
  );

  // Link olarak mı, button olarak mı render edilecek?
  if (to) {
    return (
      <Link 
        to={to}
        className={buttonClasses}
        onClick={onClick}
        {...rest}
      >
        {buttonContent}
      </Link>
    );
  }

  return (
    <button
      type={type}
      className={buttonClasses}
      disabled={disabled}
      onClick={onClick}
      {...rest}
    >
      {buttonContent}
    </button>
  );
};

CustomButton.propTypes = {
  variant: PropTypes.oneOf(['primary', 'secondary', 'outline', 'white', 'success', 'danger', 'warning']),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  fullWidth: PropTypes.bool,
  to: PropTypes.string,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  disabled: PropTypes.bool,
  onClick: PropTypes.func,
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  icon: PropTypes.node,
  iconPosition: PropTypes.oneOf(['left', 'right'])
};

export default CustomButton; 