@import url('../../styles/colors.css');

.tender-start-container {
  padding: 70px 0;
  min-height: 400px;
  background-color: #f8f9fa;
}

.tender-start-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s ease;
}

.tender-start-card .card-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
  padding: 16px 20px;
  border: none;
}

.tender-start-card .card-header h5 {
  font-weight: 600;
  letter-spacing: 0.3px;
  font-size: 18px;
}

/* <PERSON><PERSON><PERSON> */
.info-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.info-button:hover, 
.info-button:focus {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

.tender-start-card .card-body {
  padding: 25px;
}

.tender-intro-text {
  font-size: 16px;
  color: #6b739c;
  line-height: 1.6;
  max-width: 800px;
}

.parameter-item {
  padding: 15px;
  background-color: #f9f9f9;
  border-left: none;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
  height: 100%;
}

.parameter-item:hover {
  background-color: #fff;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transform: translateY(-2px);
}

.parameter-item strong {
  color: #001238;
  display: block;
  margin-bottom: 6px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

/* İhale Süreci Adım Kartları - Compact Tasarım */
.tender-process-steps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 0.5rem 0.5rem;
  position: relative;
}

/* Bağlayıcı çizgi */
.tender-process-steps::before {
  content: '';
  position: absolute;
  top: 0;
  left: 25px;
  height: 100%;
  width: 2px;
  background: rgba(255, 98, 0, 0.15);
  z-index: 0;
}

.process-step-card {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
  z-index: 1;
  border-left: none;
  padding-left: 2rem;
  align-items: flex-start;
}

.process-step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 98, 0, 0.1);
}

/* İkon konumlandırma */
.card-icon {
  position: absolute;
  top: 50%;
  left: -8px;
  transform: translateY(-50%);
  background: var(--primary-color);
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.card-icon svg {
  width: 10px;
  height: 10px;
}

.success-icon {
  background-color: #28a745;
}

.process-step-card .step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.process-step-card .step-content h5 {
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  position: relative;
  padding-bottom: 4px;
  border-bottom: 1px solid rgba(255, 98, 0, 0.15);
  width: 100%;
  text-align: left;
}

.process-step-card .step-content p {
  color: #666;
  margin-bottom: 0;
  font-size: 13px;
  line-height: 1.4;
  text-align: left;
  width: 100%;
}

/* Mobil ekranlar için adım kartı düzenlemeleri */
@media (max-width: 576px) {
  .tender-process-steps::before {
    left: 20px;
  }
  
  .process-step-card {
    padding: 0.7rem;
    padding-left: 1.8rem;
  }
  
  .card-icon {
    width: 18px;
    height: 18px;
    font-size: 9px;
    left: -7px;
  }
  
  .card-icon svg {
    width: 9px;
    height: 9px;
  }
  
  .process-step-card .step-content h5 {
    font-size: 14px;
    margin-bottom: 3px;
    padding-bottom: 3px;
  }
  
  .process-step-card .step-content p {
    font-size: 12px;
    line-height: 1.3;
  }
}

/* Tab stilleri */
.nav-tabs {
  border-bottom: 2px solid #f0f0f0;
}

.nav-tabs .nav-link {
  border: none;
  padding: 12px 20px;
  color: #7c8a97;
  font-weight: 500;
  border-radius: 0;
  transition: all 0.3s ease;
  position: relative;
}

.nav-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: transparent;
  border-bottom: 3px solid var(--primary-color);
  margin-bottom: -2px;
}

.nav-tabs .nav-link:hover {
  color: var(--primary-color);
  border-color: transparent;
}

.tab-content {
  padding-top: 20px;
}

/* Form elemanları stilleri */
.form-control {
  border-radius: 6px;
  border: 1px solid #e1e5eb;
  height: 45px;
  padding: 10px 15px;
  color: #495057;
  background-color: #fff;
  transition: all 0.3s ease;
  font-size: 14px;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(255, 98, 0, 0.1);
}

.form-control:disabled {
  background-color: #f8f9fa;
  opacity: 0.65;
}

.form-control::placeholder {
  color: #adb5bd;
}

.form-label {
  color: #495057;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 14px;
}

.form-select, 
.form-control[type="select"] {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 8px 10px;
  padding-right: 1.75rem;
}

/* Form grupları arası boşluk */
.form-group {
  margin-bottom: 1.25rem;
}

/* Modal Buton Stilleri */
.modal-footer .btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.modal-footer .btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.modal-footer .btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
}

.modal-footer .btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modal-header {
  border-bottom: 1px solid #eee;
  padding: 15px 20px;
}

.modal-header .modal-title {
  font-weight: 600;
  color: #444;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  border-top: 1px solid #eee;
  padding: 15px 20px;
}

/* İhale detayları */
.car-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: #f8f9fa;
  border-radius: 8px;
  color: #7c8a97;
}

.car-booking-image {
  border-radius: 8px;
  overflow: hidden;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  margin-bottom: 20px;
}

.car-booking-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.rental-tag {
  display: inline-block;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 10px;
}

.price-rating {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 15px 0;
}

.price-rent h4 {
  font-size: 24px;
  color: var(--primary-color);
  font-weight: 700;
}

.car-features ul {
  float: left;
  width: 50%;
}

.car-features ul li {
  margin-bottom: 8px;
  color: #4d5a68;
  font-size: 14px;
}

.car-features ul li svg {
  color: var(--primary-color);
  margin-right: 8px;
}

/* Özet Sayfası Stilleri */
.summary-section {
  margin-bottom: 25px;
  background-color: #fcfcfc;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.03);
  border: none;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  border-left: none !important;
  padding-left: 0 !important;
  color: #333;
  background: none;
}

/* 3. adım için özel stil düzenlemeleri */
.tender-start-card .section-title,
.summary-section .section-title,
.tender-rules .section-title {
  border-left: none !important;
  padding-left: 0 !important;
  border-bottom: 1px solid #eee;
  background: none;
}

.summary-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
}

.item-label {
  font-weight: 500;
  color: #6c757d;
}

.item-value {
  font-weight: 500;
  color: #333;
}

.tender-rules {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border-left: none;
}

.rules-content {
  padding: 10px;
  color: #6c757d;
}

/* Adım Butonları */
.secondary-btn {
  background-color: #6c757d;
  border-color: #6c757d;
}

.secondary-btn:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Geliştirilmiş Navigasyon Butonları */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  width: 100%;
  max-width: 100%;
}

.navigation-buttons.justify-content-end {
  justify-content: flex-end;
}

.gauto-theme-btn {
  padding: 8px 12px !important;
  font-size: 13px !important;
  margin: 0 !important;
  min-width: auto !important;
  max-width: fit-content !important;
  line-height: 1.3 !important;
}

.btn-icon {
  margin: 0 5px !important;
  font-size: 11px !important;
}

.gauto-theme-btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

/* Mobil Responsive Stepper */
@media (max-width: 768px) {
  .custom-stepper {
    padding: 15px 10px;
    margin-bottom: 15px;
  }
  
  .custom-stepper .MuiStepLabel-label {
    font-size: 14px;
  }
  
  .custom-stepper .step-description {
    font-size: 12px;
  }
  
  /* Form alanları için mobil iyileştirmeler */
  .tender-start-card {
    margin-bottom: 15px;
  }
  
  .tender-start-card .card-header h5 {
    font-size: 16px;
  }
  
  .tender-intro-text {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 15px;
  }
  
  .form-label {
    font-size: 13px;
    margin-bottom: 4px;
  }
  
  .form-control {
    height: 40px;
    font-size: 13px;
    padding: 8px 12px;
  }
  
  .summary-section {
    padding: 10px;
    margin-bottom: 15px;
  }
  
  .section-title {
    font-size: 15px;
    margin-bottom: 10px;
  }
  
  .summary-item {
    flex-direction: column;
    padding: 3px 0;
  }
  
  .item-label {
    margin-bottom: 3px;
  }
  
  /* Butonlar için mobil iyileştirmeler */
  .navigation-buttons {
    padding: 10px 0;
  }
  
  .gauto-theme-btn {
    padding: 7px 10px !important;
    font-size: 12px !important;
  }
  
  /* İhale partnerları slider bölümü için mobil iyileştirmeler */
  .partner-brands-section {
    padding: 25px 0;
    margin: 15px 0;
  }
  
  .partner-brands-heading h3 {
    font-size: 20px;
  }
  
  .partner-brands-heading p {
    font-size: 13px;
  }
}

.tender-rules .form-check {
  margin-top: 15px;
  font-weight: 500;
}

.tender-rules .form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.navigation-buttons .gauto-theme-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.timeline-step.active {
  color: var(--primary-color);
  font-weight: 600;
  border-bottom: 3px solid var(--primary-color);
}

.timeline-step.active .step-number {
  color: var(--primary-color);
}

.gradient-border {
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  height: 3px;
  margin: 20px 0;
  border-radius: 2px;
}

.ts-button-primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #fff !important;
}

.ts-text-primary {
  color: var(--primary-color) !important;
}

/* Stepper bileşeni stilleri */
.custom-stepper {
  margin-bottom: 1.5rem;
  padding: 1.5rem 0;
  background-color: #f9f9fa;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

/* Üst gradient çizgi */
.custom-stepper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
}

/* Material UI stepper özelleştirmeleri */
.custom-stepper .MuiStepLabel-label {
  font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
}

.custom-stepper .step-description {
  font-size: 14px;
  color: #777;
  margin-top: 4px;
}

/* Mobil görünüm için stepper ayarları */
@media (max-width: 768px) {
  .custom-stepper {
    padding: 15px 10px;
    margin-bottom: 15px;
  }
  
  .custom-stepper .MuiStepLabel-label {
    font-size: 14px;
  }
  
  .custom-stepper .step-description {
    font-size: 12px;
  }
}

.input-label-icon {
  color: var(--primary-color);
  margin-right: 6px;
  vertical-align: middle;
} 