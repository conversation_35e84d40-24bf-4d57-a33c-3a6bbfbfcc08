import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import PageTitle from "../../components/PageTitle";
import ServiceList from "../../components/ServiceList";

const ServicePage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle pageTitle={t("header-navigation.service")} pagesub={t("header-navigation.service")} />
      <ServiceList />
    </Fragment>
  );
};
export default ServicePage;
