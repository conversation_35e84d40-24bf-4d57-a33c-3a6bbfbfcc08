import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, Card, Form, <PERSON><PERSON>, Alert, Spinner, Dropdown } from "react-bootstrap";
import { 
  FaEnvelope, 
  FaIdCard, 
  FaPhone, 
  FaEdit, 
  FaMapMarkerAlt,
  FaGlobe,
  FaTimes,
  FaTruck,
  FaFacebook,
  FaTwitter,
  FaInstagram,
  FaLinkedin,
  FaCheckCircle,
  FaExclamationTriangle,
  FaPencilAlt,
  FaCheck,
  FaPlus,
  FaTrash,
  FaInfoCircle,
  FaTh,
  FaBuilding,
  FaCalendarAlt,
  FaCar,
  FaMoneyBillWave,
  FaClock
} from "react-icons/fa";
import { USER_ROLES } from "../../constants/userRoles";
import companyService from "../../services/companyServices/companyService";
import "./style.css";

const CompanyProfile = () => {
  const { t } = useTranslation();
  const { user: storeUser, isLoading: authLoading } = useSelector((state) => state.auth);
  
  // Kullanıcı bilgisi
  const user = storeUser;
  
  // Durum yönetimi
  const [activeTab, setActiveTab] = useState('info');
  const [isEditing, setIsEditing] = useState(false);
  const [companyData, setCompanyData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [carBrands, setCarBrands] = useState([]);
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [isLoadingBrands, setIsLoadingBrands] = useState(false);
  const [rentalPeriods, setRentalPeriods] = useState([]);
  const [isLoadingPeriods, setIsLoadingPeriods] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Şirket verilerini yükleme
  useEffect(() => {
    if (user && user.id) {
      loadCompanyProfile(user.id);
      loadCarBrands();
      loadRentalPeriods();
    }
  }, [user]);
  
  // Kiralama sürelerini yükleme
  const loadRentalPeriods = async () => {
    try {
      setIsLoadingPeriods(true);
      const response = await companyService.getRentalPeriods();
      setRentalPeriods(response.data);
      setError(null);
    } catch (err) {
      console.error("Kiralama süreleri yüklenirken hata oluştu:", err);
      setError("Kiralama süreleri yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
    } finally {
      setIsLoadingPeriods(false);
    }
  };
  
  // Araç markalarını yükleme
  const loadCarBrands = async () => {
    try {
      setIsLoadingBrands(true);
      const response = await companyService.getCarBrands();
      setCarBrands(response.data);
      setError(null);
    } catch (err) {
      console.error("Araç markaları yüklenirken hata oluştu:", err);
      setError("Araç markaları yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
    } finally {
      setIsLoadingBrands(false);
    }
  };
  
  const loadCompanyProfile = async (userId) => {
    try {
      setIsLoading(true);
      const response = await companyService.getCompanyProfile(userId);
      const profileData = response.data;
      
      // Eğer araç kiralama seçenekleri varsa, marka listesini başlat
      if (profileData.carRentalOptions && profileData.carRentalOptions.availableBrands) {
        setSelectedBrands(profileData.carRentalOptions.availableBrands);
      }
      
      setCompanyData(profileData);
      setError(null);
    } catch (err) {
      console.error("Şirket profili yüklenirken hata oluştu:", err);
      setError("Şirket profili yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCompanyData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSocialMediaChange = (e) => {
    const { name, value } = e.target;
    setCompanyData(prev => ({
      ...prev,
      socialMedia: {
        ...prev.socialMedia,
        [name]: value
      }
    }));
  };
  
  const handleServicesChange = (index, value) => {
    const updatedServices = [...companyData.services];
    updatedServices[index] = value;
    setCompanyData(prev => ({
      ...prev,
      services: updatedServices
    }));
  };
  
  const addService = () => {
    setCompanyData(prev => ({
      ...prev,
      services: [...prev.services, '']
    }));
  };
  
  const removeService = (index) => {
    const updatedServices = [...companyData.services];
    updatedServices.splice(index, 1);
    setCompanyData(prev => ({
      ...prev,
      services: updatedServices
    }));
  };
  
  const handleDeliveryOptionsChange = (index, value) => {
    const updatedOptions = [...companyData.deliveryOptions];
    updatedOptions[index] = value;
    setCompanyData(prev => ({
      ...prev,
      deliveryOptions: updatedOptions
    }));
  };
  
  const addDeliveryOption = () => {
    setCompanyData(prev => ({
      ...prev,
      deliveryOptions: [...prev.deliveryOptions, '']
    }));
  };
  
  const removeDeliveryOption = (index) => {
    const updatedOptions = [...companyData.deliveryOptions];
    updatedOptions.splice(index, 1);
    setCompanyData(prev => ({
      ...prev,
      deliveryOptions: updatedOptions
    }));
  };
  
  const handleCarRentalTypeChange = (index, value) => {
    const updatedTypes = [...companyData.carRentalOptions.vehicleTypes];
    updatedTypes[index] = value;
    setCompanyData(prev => ({
      ...prev,
      carRentalOptions: {
        ...prev.carRentalOptions,
        vehicleTypes: updatedTypes
      }
    }));
  };
  
  const addCarRentalType = () => {
    setCompanyData(prev => ({
      ...prev,
      carRentalOptions: {
        ...prev.carRentalOptions,
        vehicleTypes: [...prev.carRentalOptions.vehicleTypes, '']
      }
    }));
  };
  
  const removeCarRentalType = (index) => {
    const updatedTypes = [...companyData.carRentalOptions.vehicleTypes];
    updatedTypes.splice(index, 1);
    setCompanyData(prev => ({
      ...prev,
      carRentalOptions: {
        ...prev.carRentalOptions,
        vehicleTypes: updatedTypes
      }
    }));
  };
  
  const handleRentalPeriodChange = (periodId) => {
    // İlgili periyodu rentalPeriods dizisinden bulalım
    const periodItem = rentalPeriods.find(period => period.id === periodId);
    
    if (!periodItem) return;
    
    // Eğer zaten seçili değilse, ekleyelim
    if (!companyData.carRentalOptions.rentalPeriods.includes(periodItem.value)) {
      setCompanyData(prev => ({
        ...prev,
        carRentalOptions: {
          ...prev.carRentalOptions,
          rentalPeriods: [...prev.carRentalOptions.rentalPeriods, periodItem.value].sort((a, b) => a - b)
        }
      }));
    } else {
      // Seçili ise, çıkaralım
      const updatedPeriods = companyData.carRentalOptions.rentalPeriods
        .filter(p => p !== periodItem.value);
      
      setCompanyData(prev => ({
        ...prev,
        carRentalOptions: {
          ...prev.carRentalOptions,
          rentalPeriods: updatedPeriods
        }
      }));
    }
  };
  
  const handleCarBrandChange = (brandId) => {
    if (!selectedBrands.includes(brandId)) {
      setSelectedBrands([...selectedBrands, brandId]);
      setCompanyData(prev => ({
        ...prev,
        carRentalOptions: {
          ...prev.carRentalOptions,
          availableBrands: [...selectedBrands, brandId]
        }
      }));
    } else {
      const updatedBrands = selectedBrands.filter(id => id !== brandId);
      setSelectedBrands(updatedBrands);
      setCompanyData(prev => ({
        ...prev,
        carRentalOptions: {
          ...prev.carRentalOptions,
          availableBrands: updatedBrands
        }
      }));
    }
  };
  
  const handleCarRentalOptionChange = (option, value) => {
    setCompanyData(prev => ({
      ...prev,
      carRentalOptions: {
        ...prev.carRentalOptions,
        [option]: value
      }
    }));
  };
  
  const handleLogoChange = async (e) => {
    if (e.target.files && e.target.files[0]) {
      try {
        const logoFile = e.target.files[0];
        const response = await companyService.uploadCompanyLogo(user.id, logoFile);
        
        setCompanyData(prev => ({
          ...prev,
          logo: response.data.logoUrl
        }));
        
      } catch (err) {
        console.error("Logo yüklenirken hata oluştu:", err);
        setError("Logo yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
      }
    }
  };
  
  // Sosyal medya URL'sini formatla - https:// kontrolü ekle
  const formatSocialMediaUrl = (url) => {
    if (!url) return '';
    return url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`;
  };
  
  // Servis verisini standartlaştır (string veya obje)
  const standardizeServiceData = (service) => {
    return typeof service === 'object' && service.name ? service.name : service;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setIsSaving(true);
      
      // Profil bilgilerini güncelle
      const profileResponse = await companyService.updateCompanyProfile(user.id, companyData);
      
      // Hizmetleri güncelle
      await companyService.updateCompanyServices(user.id, companyData.services);
      
      // Teslimat seçeneklerini güncelle
      await companyService.updateDeliveryOptions(user.id, companyData.deliveryOptions);
      
      // Araç kiralama seçeneklerini güncelle (eğer mevcutsa)
      if (companyData.carRentalOptions) {
        await companyService.updateCarRentalOptions(user.id, companyData.carRentalOptions);
      }
      
      // Güncellenmiş profil verilerini state'e yansıt
      setCompanyData(profileResponse.data);
      setIsEditing(false);
      setError(null);
    } catch (err) {
      console.error("Profil güncellenirken hata oluştu:", err);
      setError("Profil güncellenirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.");
    } finally {
      setIsSaving(false);
    }
  };
  
  // Araç kiralama seçenekleri bölümü
  const renderCarRentalOptions = () => {
    if (!companyData.services.some(service => 
      standardizeServiceData(service).includes('Araç Kiralama')
    )) {
      return null;
    }
    
    // Araç kiralama seçenekleri yoksa varsayılan değerler atama
    if (!companyData.carRentalOptions) {
      companyData.carRentalOptions = {
        vehicleTypes: [],
        rentalPeriods: [],
        availableBrands: [],
        includesInsurance: false,
        includesMaintenance: false,
        deliveryAvailable: false
      };
    }
    
    return (
      <>
        <h4 className="section-title mt-4">Araç Kiralama Seçenekleri</h4>
        
        {isEditing ? (
          <div>
            <Form.Group className="mb-4">
              <Form.Label><strong>Araç Tipleri</strong></Form.Label>
              <div className="d-flex flex-wrap">
                {companyData.carRentalOptions.vehicleTypes.map((type, index) => (
                  <div className="mb-2 d-flex align-items-center me-2" key={index}>
                    <Form.Control
                      type="text"
                      className="form-input"
                      value={type}
                      onChange={(e) => handleCarRentalTypeChange(index, e.target.value)}
                    />
                    <Button
                      variant="outline-danger"
                      size="sm"
                      className="ms-2"
                      onClick={() => removeCarRentalType(index)}
                    >
                      <FaTrash />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline-primary"
                  size="sm"
                  onClick={addCarRentalType}
                >
                  <FaPlus className="me-2" />
                  Araç Tipi Ekle
                </Button>
              </div>
            </Form.Group>
            
            <Form.Group className="mb-4">
              <Form.Label><strong>Kiralama Süreleri (Ay)</strong></Form.Label>
              {isLoadingPeriods ? (
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <span>Kiralama süreleri yükleniyor...</span>
                </div>
              ) : (
                <div className="rental-periods-options">
                  {rentalPeriods.map(period => (
                    <Form.Check
                      key={period.id}
                      type="checkbox"
                      id={`period-${period.id}`}
                      label={period.label}
                      checked={companyData.carRentalOptions.rentalPeriods.includes(period.value)}
                      onChange={() => handleRentalPeriodChange(period.id)}
                      className="me-3"
                    />
                  ))}
                </div>
              )}
              <small className="text-muted">* Minimum kiralama süresi 6 aydır.</small>
            </Form.Group>
            
            <Form.Group className="mb-4">
              <Form.Label><strong>Araç Markaları</strong></Form.Label>
              {isLoadingBrands ? (
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <span>Markalar yükleniyor...</span>
                </div>
              ) : (
                <Row>
                  {carBrands.map(brand => (
                    <Col xs={12} sm={6} md={4} key={brand.id} className="mb-2">
                      <Form.Check
                        type="checkbox"
                        id={`brand-${brand.id}`}
                        label={brand.name}
                        checked={selectedBrands.includes(brand.id)}
                        onChange={() => handleCarBrandChange(brand.id)}
                      />
                    </Col>
                  ))}
                </Row>
              )}
            </Form.Group>
            
            <Row className="mb-3">
              <Col md={4}>
                <Form.Check
                  type="switch"
                  id="insurance-switch"
                  label="Sigorta Dahil"
                  checked={companyData.carRentalOptions.includesInsurance}
                  onChange={(e) => handleCarRentalOptionChange('includesInsurance', e.target.checked)}
                />
              </Col>
              <Col md={4}>
                <Form.Check
                  type="switch"
                  id="maintenance-switch"
                  label="Bakım Dahil"
                  checked={companyData.carRentalOptions.includesMaintenance}
                  onChange={(e) => handleCarRentalOptionChange('includesMaintenance', e.target.checked)}
                />
              </Col>
              <Col md={4}>
                <Form.Check
                  type="switch"
                  id="delivery-switch"
                  label="Teslimat Mevcut"
                  checked={companyData.carRentalOptions.deliveryAvailable}
                  onChange={(e) => handleCarRentalOptionChange('deliveryAvailable', e.target.checked)}
                />
              </Col>
            </Row>
          </div>
        ) : (
          <Card className="mb-4">
            <Card.Body>
              <Row>
                <Col md={6}>
                  <h5 className="sub-section-title">Araç Tipleri</h5>
                  {companyData.carRentalOptions.vehicleTypes && companyData.carRentalOptions.vehicleTypes.length > 0 ? (
                    <div className="d-flex flex-wrap">
                      {companyData.carRentalOptions.vehicleTypes.map((type, index) => (
                        <div className="service-item me-2 mb-2" key={index}>
                          <FaCheckCircle className="service-icon me-2" />
                          {type}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-data-message">
                      <FaInfoCircle className="me-2" />
                      Henüz araç tipi eklenmemiş
                    </div>
                  )}
                </Col>
                <Col md={6}>
                  <h5 className="sub-section-title">Kiralama Süreleri</h5>
                  {companyData.carRentalOptions.rentalPeriods && companyData.carRentalOptions.rentalPeriods.length > 0 ? (
                    <div className="d-flex flex-wrap">
                      {companyData.carRentalOptions.rentalPeriods.map((periodValue, index) => {
                        // Seçili olan dönem için rentalPeriods dizisinden label değerini bulalım
                        const periodItem = rentalPeriods.find(p => p.value === periodValue);
                        const displayText = periodItem ? periodItem.label : `${periodValue} Ay`;
                        
                        return (
                          <div className="service-item me-2 mb-2" key={index}>
                            <FaCheckCircle className="service-icon me-2" />
                            {displayText}
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="empty-data-message">
                      <FaInfoCircle className="me-2" />
                      Henüz kiralama süresi eklenmemiş
                    </div>
                  )}
                </Col>
              </Row>
              
              <Row className="mt-4">
                <Col md={6}>
                  <h5 className="sub-section-title">Araç Markaları</h5>
                  {companyData.carRentalOptions.availableBrands && companyData.carRentalOptions.availableBrands.length > 0 ? (
                    <div className="d-flex flex-wrap">
                      {companyData.carRentalOptions.availableBrands && carBrands
                        .filter(brand => companyData.carRentalOptions.availableBrands.includes(brand.id))
                        .map((brand) => (
                          <div className="service-item me-2 mb-2" key={brand.id}>
                            <FaCar className="service-icon me-2" />
                            {brand.name}
                          </div>
                      ))}
                    </div>
                  ) : (
                    <div className="empty-data-message">
                      <FaInfoCircle className="me-2" />
                      Henüz araç markası eklenmemiş
                    </div>
                  )}
                </Col>
                  
                <Col md={6}>
                  <h5 className="sub-section-title">Ek Hizmetler</h5>
                  <ul className="list-unstyled">
                    {companyData.carRentalOptions.includesInsurance && (
                      <li><FaCheckCircle className="text-success me-2" /> Sigorta Dahil</li>
                    )}
                    {companyData.carRentalOptions.includesMaintenance && (
                      <li><FaCheckCircle className="text-success me-2" /> Bakım Dahil</li>
                    )}
                    {companyData.carRentalOptions.deliveryAvailable && (
                      <li><FaCheckCircle className="text-success me-2" /> Araç Teslimatı Mevcut</li>
                    )}
                    {!companyData.carRentalOptions.includesInsurance && 
                     !companyData.carRentalOptions.includesMaintenance && 
                     !companyData.carRentalOptions.deliveryAvailable && (
                      <li className="text-muted"><FaInfoCircle className="me-2" /> Henüz ek hizmet eklenmemiş</li>
                    )}
                  </ul>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        )}
      </>
    );
  };
  
  // Hizmetler sekmesi içeriği
  const renderServicesTab = () => {
    return (
      <div className="tab-pane fade show active">
        <h4 className="section-title">Sunulan Hizmetler</h4>
        
        {isEditing ? (
          <div>
            {companyData.services.map((service, index) => {
              const serviceValue = standardizeServiceData(service);
              
              return (
                <div className="mb-3 d-flex align-items-center" key={index}>
                  <Form.Control
                    type="text"
                    className="form-input"
                    value={serviceValue}
                    onChange={(e) => handleServicesChange(index, e.target.value)}
                  />
                  <Button
                    variant="outline-danger"
                    size="sm"
                    className="ms-2"
                    onClick={() => removeService(index)}
                  >
                    <FaTrash />
                  </Button>
                </div>
              );
            })}
            <Button
              variant="outline-primary"
              size="sm"
              onClick={addService}
            >
              <FaPlus className="me-2" />
              Hizmet Ekle
            </Button>
          </div>
        ) : (
          companyData.services && companyData.services.length > 0 ? (
            <Row>
              {companyData.services.map((service, index) => {
                const serviceDisplay = standardizeServiceData(service);
                
                return (
                  <Col md={6} className="mb-3" key={index}>
                    <div className="service-item">
                      <FaCheckCircle className="service-icon me-2" />
                      {serviceDisplay}
                    </div>
                  </Col>
                );
              })}
            </Row>
          ) : (
            <div className="empty-data-message">
              <FaInfoCircle className="me-2" />
              Henüz hizmet eklenmemiş
            </div>
          )
        )}

        {renderCarRentalOptions()}
        
        <h4 className="section-title mt-4">Teslimat Seçenekleri</h4>
        
        {isEditing ? (
          <div>
            {companyData.deliveryOptions.map((option, index) => (
              <div className="mb-3 d-flex align-items-center" key={index}>
                <Form.Control
                  type="text"
                  className="form-input"
                  value={option}
                  onChange={(e) => handleDeliveryOptionsChange(index, e.target.value)}
                />
                <Button
                  variant="outline-danger"
                  size="sm"
                  className="ms-2"
                  onClick={() => removeDeliveryOption(index)}
                >
                  <FaTrash />
                </Button>
              </div>
            ))}
            <Button
              variant="outline-primary"
              size="sm"
              onClick={addDeliveryOption}
            >
              <FaPlus className="me-2" />
              Teslimat Seçeneği Ekle
            </Button>
          </div>
        ) : (
          companyData.deliveryOptions && companyData.deliveryOptions.length > 0 ? (
            <Row>
              {companyData.deliveryOptions.map((option, index) => (
                <Col md={6} className="mb-3" key={index}>
                  <div className="service-item">
                    <FaTruck className="service-icon me-2" />
                    {option}
                  </div>
                </Col>
              ))}
            </Row>
          ) : (
            <div className="empty-data-message">
              <FaInfoCircle className="me-2" />
              Henüz teslimat seçeneği eklenmemiş
            </div>
          )
        )}
      </div>
    );
  };
  
  if (authLoading || isLoading) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" variant="success" />
        <p className="mt-3">{t("common.loading")}</p>
      </Container>
    );
  }

  if (!user) {
    return (
      <Container className="py-5">
        <Alert variant="warning">
          <FaExclamationTriangle className="me-2" />
          {t("profile_page.userNotFound")}
        </Alert>
      </Container>
    );
  }
  
  if (!companyData) {
    return (
      <Container className="py-5">
        <Alert variant="info">
          <FaInfoCircle className="me-2" />
          Şirket profili henüz oluşturulmamış. Lütfen profil bilgilerinizi doldurun.
        </Alert>
      </Container>
    );
  }

  return (
    <div className="company-profile-area">
      <Container>
        {error && (
          <Alert variant="danger" className="mb-4">
            <FaExclamationTriangle className="me-2" />
            {error}
          </Alert>
        )}
        
        <Row>
          <Col md={4}>
            <Card className="cp-profile-card">
              <Card.Body>
                <div className="company-logo-wrapper">
                  {companyData.logo ? (
                    <img src={companyData.logo} alt={companyData.name} className="company-logo" />
                  ) : (
                    <div className="company-logo-placeholder">
                      <FaBuilding />
                    </div>
                  )}
                  
                  {isEditing && (
                    <div className="mt-3">
                      <Form.Control
                        type="file"
                        onChange={handleLogoChange}
                        accept="image/*"
                      />
                    </div>
                  )}
                  
                  <div className={`verification-status ${companyData.verificationStatus}`}>
                    {companyData.verificationStatus === 'verified' ? (
                      <>
                        <FaCheckCircle className="me-1" />
                        Doğrulanmış Şirket
                      </>
                    ) : (
                      <>
                        <FaExclamationTriangle className="me-1" />
                        Doğrulanmamış
                      </>
                    )}
                  </div>
                </div>
                
                {!isEditing ? (
                  <h4 className="text-center mb-4">{companyData.name}</h4>
                ) : (
                  <div className="mb-4">
                    <Form.Control
                      type="text"
                      className="form-input text-center"
                      name="name"
                      value={companyData.name}
                      onChange={handleInputChange}
                    />
                  </div>
                )}
                
                <div className="company-info-list">
                  <div className="company-info-item">
                    <span className="cp-info-icon"><FaPhone /></span>
                    {!isEditing ? companyData.phone : (
                      <Form.Control
                        type="text"
                        className="form-input"
                        name="phone"
                        value={companyData.phone}
                        onChange={handleInputChange}
                      />
                    )}
                  </div>
                  
                  <div className="company-info-item">
                    <span className="cp-info-icon"><FaIdCard /></span>
                    {!isEditing ? companyData.taxId : (
                      <Form.Control
                        type="text"
                        className="form-input"
                        name="taxId"
                        value={companyData.taxId}
                        onChange={handleInputChange}
                      />
                    )}
                  </div>
                  
                  <div className="company-info-item">
                    <span className="cp-info-icon"><FaMapMarkerAlt /></span>
                    {!isEditing ? companyData.address : (
                      <Form.Control
                        type="text"
                        className="form-input"
                        name="address"
                        value={companyData.address}
                        onChange={handleInputChange}
                      />
                    )}
                  </div>
                  
                  <div className="company-info-item">
                    <span className="cp-info-icon"><FaGlobe /></span>
                    {!isEditing ? companyData.website : (
                      <Form.Control
                        type="text"
                        className="form-input"
                        name="website"
                        value={companyData.website}
                        onChange={handleInputChange}
                      />
                    )}
                  </div>
                  
                  <div className="company-info-item">
                    <FaCalendarAlt className="cp-info-icon" />
                    {!isEditing ? `Kuruluş Yılı: ${companyData.foundedYear}` : (
                      <div className="d-flex align-items-center w-100">
                        <span className="me-2">Kuruluş Yılı:</span>
                        <Form.Control
                          type="text"
                          className="form-input"
                          name="foundedYear"
                          value={companyData.foundedYear}
                          onChange={handleInputChange}
                        />
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="social-media-links">
                  {companyData.socialMedia.facebook && (
                    <a href={formatSocialMediaUrl(companyData.socialMedia.facebook)} target="_blank" rel="noopener noreferrer">
                      <FaFacebook />
                    </a>
                  )}
                  {companyData.socialMedia.twitter && (
                    <a href={formatSocialMediaUrl(companyData.socialMedia.twitter)} target="_blank" rel="noopener noreferrer">
                      <FaTwitter />
                    </a>
                  )}
                  {companyData.socialMedia.instagram && (
                    <a href={formatSocialMediaUrl(companyData.socialMedia.instagram)} target="_blank" rel="noopener noreferrer">
                      <FaInstagram />
                    </a>
                  )}
                  {companyData.socialMedia.linkedin && (
                    <a href={formatSocialMediaUrl(companyData.socialMedia.linkedin)} target="_blank" rel="noopener noreferrer">
                      <FaLinkedin />
                    </a>
                  )}
                </div>
                
                <div className="mt-4 d-flex justify-content-center">
                  <Button 
                    variant={isEditing ? "outline-secondary" : "outline-danger"}
                    className="cp-edit-button" 
                    onClick={handleEditToggle}
                  >
                    {isEditing ? (
                      <>
                        <span className="cp-edit-icon cancel"></span>
                        İptal
                      </>
                    ) : (
                      <>
                        <span className="cp-edit-icon"></span>
                        Profili Düzenle
                      </>
                    )}
                  </Button>
                  
                  {isEditing && (
                    <Button 
                      variant="success" 
                      className="ms-2 d-flex align-items-center btn-save"
                      onClick={handleSubmit}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <>
                          <div className="save-spinner" aria-hidden="true"></div>
                          Kaydediliyor...
                        </>
                      ) : (
                        <>
                          <FaCheck className="me-2" />
                          Kaydet
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </Card.Body>
            </Card>
          </Col>
          
          <Col md={8}>
            <Card className="cp-profile-card">
              <Card.Header>
                <h5>{companyData.name} - Şirket Profili</h5>
              </Card.Header>
              
              <Card.Body>
                <ul className="nav cp-profile-tabs mb-4">
                  <li className="nav-item">
                    <Button
                      variant="link"
                      className={`nav-link ${activeTab === 'info' ? 'active' : ''}`}
                      onClick={() => setActiveTab('info')}
                    >
                      <FaInfoCircle className="me-2" />
                      Şirket Bilgileri
                    </Button>
                  </li>
                  <li className="nav-item">
                    <Button
                      variant="link"
                      className={`nav-link ${activeTab === 'services' ? 'active' : ''}`}
                      onClick={() => setActiveTab('services')}
                    >
                      <FaTh className="me-2" />
                      Hizmetler
                    </Button>
                  </li>
                  <li className="nav-item">
                    <Button
                      variant="link"
                      className={`nav-link ${activeTab === 'contact' ? 'active' : ''}`}
                      onClick={() => setActiveTab('contact')}
                    >
                      <FaEnvelope className="me-2" />
                      İletişim
                    </Button>
                  </li>
                </ul>
                
                <div className="tab-content">
                  {/* Şirket Bilgileri Sekmesi */}
                  {activeTab === 'info' && (
                    <div className="tab-pane fade show active">
                      <h4 className="section-title">Şirket Açıklaması</h4>
                      {!isEditing ? (
                        <p>{companyData.description || "Henüz açıklama eklenmemiş."}</p>
                      ) : (
                        <Form.Control
                          as="textarea"
                          className="form-textarea mb-4"
                          name="description"
                          value={companyData.description || ""}
                          onChange={handleInputChange}
                          rows={4}
                        />
                      )}
                      
                      <h4 className="section-title">Sosyal Medya Hesapları</h4>
                      {isEditing ? (
                        <Row>
                          <Col md={6} className="mb-3">
                            <div className="input-group">
                              <span className="input-group-text">
                                <FaFacebook />
                              </span>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="facebook"
                                value={companyData.socialMedia.facebook || ""}
                                onChange={handleSocialMediaChange}
                                placeholder="Facebook URL"
                              />
                            </div>
                          </Col>
                          <Col md={6} className="mb-3">
                            <div className="input-group">
                              <span className="input-group-text">
                                <FaTwitter />
                              </span>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="twitter"
                                value={companyData.socialMedia.twitter || ""}
                                onChange={handleSocialMediaChange}
                                placeholder="Twitter URL"
                              />
                            </div>
                          </Col>
                          <Col md={6} className="mb-3">
                            <div className="input-group">
                              <span className="input-group-text">
                                <FaInstagram />
                              </span>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="instagram"
                                value={companyData.socialMedia.instagram || ""}
                                onChange={handleSocialMediaChange}
                                placeholder="Instagram URL"
                              />
                            </div>
                          </Col>
                          <Col md={6} className="mb-3">
                            <div className="input-group">
                              <span className="input-group-text">
                                <FaLinkedin />
                              </span>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="linkedin"
                                value={companyData.socialMedia.linkedin || ""}
                                onChange={handleSocialMediaChange}
                                placeholder="LinkedIn URL"
                              />
                            </div>
                          </Col>
                        </Row>
                      ) : (
                        <Row>
                          {companyData.socialMedia && (
                            Object.entries(companyData.socialMedia).some(([_, value]) => value) ? (
                              Object.entries(companyData.socialMedia).map(([key, value]) => {
                                if (!value) return null;
                                let Icon;
                                let colorClass;
                                
                                switch(key) {
                                  case 'facebook':
                                    Icon = FaFacebook;
                                    colorClass = 'text-primary';
                                    break;
                                  case 'twitter':
                                    Icon = FaTwitter;
                                    colorClass = 'text-info';
                                    break;
                                  case 'instagram':
                                    Icon = FaInstagram;
                                    colorClass = 'text-danger';
                                    break;
                                  case 'linkedin':
                                    Icon = FaLinkedin;
                                    colorClass = 'text-primary';
                                    break;
                                  default:
                                    Icon = FaGlobe;
                                    colorClass = 'text-secondary';
                                }
                                
                                return (
                                  <Col md={6} className="mb-3" key={key}>
                                    <div className="cp-info-list-item">
                                      <Icon className={`me-2 ${colorClass}`} />
                                      {value}
                                    </div>
                                  </Col>
                                );
                              })
                            ) : (
                              <Col xs={12}>
                                <div className="empty-data-message">
                                  <FaInfoCircle className="me-2" />
                                  Henüz sosyal medya hesabı eklenmemiş
                                </div>
                              </Col>
                            )
                          )}
                        </Row>
                      )}
                      
                      <h4 className="section-title">Tedarik Bilgileri</h4>
                      {!isEditing ? (
                        <Row>
                          <Col md={6} className="mb-3">
                            <div className="supplier-info-card">
                              <div className="supplier-info-title">
                                <div className="supplier-info-icon">
                                  <FaBuilding />
                                </div>
                                Tedarikçi Tipi
                              </div>
                              <div className="supplier-info-content">
                                {companyData.supplierType || "Belirtilmemiş"}
                              </div>
                            </div>
                          </Col>
                          <Col md={6} className="mb-3">
                            <div className="supplier-info-card">
                              <div className="supplier-info-title">
                                <div className="supplier-info-icon">
                                  <FaCalendarAlt />
                                </div>
                                Çalışma Saatleri
                              </div>
                              <div className="supplier-info-content">
                                {companyData.workingHours || "Belirtilmemiş"}
                              </div>
                            </div>
                          </Col>
                          <Col md={6} className="mb-3">
                            <div className="supplier-info-card">
                              <div className="supplier-info-title">
                                <div className="supplier-info-icon">
                                  <FaMoneyBillWave />
                                </div>
                                Minimum Sipariş Tutarı
                              </div>
                              <div className="supplier-info-content">
                                {companyData.minOrderAmount || "Belirtilmemiş"}
                              </div>
                            </div>
                          </Col>
                          <Col md={6} className="mb-3">
                            <div className="supplier-info-card">
                              <div className="supplier-info-title">
                                <div className="supplier-info-icon">
                                  <FaClock />
                                </div>
                                Ortalama Yanıt Süresi
                              </div>
                              <div className="supplier-info-content">
                                {companyData.avgResponseTime || "Belirtilmemiş"}
                              </div>
                            </div>
                          </Col>
                        </Row>
                      ) : (
                        <Row>
                          <Col md={6} className="mb-3">
                            <Form.Group>
                              <Form.Label className="d-flex align-items-center">
                                <div className="supplier-info-icon">
                                  <FaBuilding />
                                </div>
                                <strong>Tedarikçi Tipi</strong>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="supplierType"
                                value={companyData.supplierType || ""}
                                onChange={handleInputChange}
                              />
                            </Form.Group>
                          </Col>
                          <Col md={6} className="mb-3">
                            <Form.Group>
                              <Form.Label className="d-flex align-items-center">
                                <div className="supplier-info-icon">
                                  <FaCalendarAlt />
                                </div>
                                <strong>Çalışma Saatleri</strong>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="workingHours"
                                value={companyData.workingHours || ""}
                                onChange={handleInputChange}
                              />
                            </Form.Group>
                          </Col>
                          <Col md={6} className="mb-3">
                            <Form.Group>
                              <Form.Label className="d-flex align-items-center">
                                <div className="supplier-info-icon">
                                  <FaMoneyBillWave />
                                </div>
                                <strong>Minimum Sipariş Tutarı</strong>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="minOrderAmount"
                                value={companyData.minOrderAmount || ""}
                                onChange={handleInputChange}
                              />
                            </Form.Group>
                          </Col>
                          <Col md={6} className="mb-3">
                            <Form.Group>
                              <Form.Label className="d-flex align-items-center">
                                <div className="supplier-info-icon">
                                  <FaClock />
                                </div>
                                <strong>Ortalama Yanıt Süresi</strong>
                              </Form.Label>
                              <Form.Control
                                type="text"
                                className="form-input"
                                name="avgResponseTime"
                                value={companyData.avgResponseTime || ""}
                                onChange={handleInputChange}
                              />
                            </Form.Group>
                          </Col>
                        </Row>
                      )}
                    </div>
                  )}
                  
                  {/* Hizmetler Sekmesi */}
                  {activeTab === 'services' && renderServicesTab()}
                  
                  {/* İletişim Sekmesi */}
                  {activeTab === 'contact' && (
                    <div className="tab-pane fade show active">
                      <h4 className="section-title">İletişim Bilgileri</h4>
                      
                      <div className="contact-info mb-4">
                        <div className="contact-item">
                          <div className="d-flex">
                            <div className="contact-icon">
                              <FaPhone />
                            </div>
                            <div className="contact-details ms-3">
                              <h5 className="mb-1">Telefon</h5>
                              <p className="mb-0">{companyData.phone}</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="contact-item">
                          <div className="d-flex">
                            <div className="contact-icon">
                              <FaMapMarkerAlt />
                            </div>
                            <div className="contact-details ms-3">
                              <h5 className="mb-1">Adres</h5>
                              <p className="mb-0">{companyData.address}</p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="contact-item">
                          <div className="d-flex">
                            <div className="contact-icon">
                              <FaGlobe />
                            </div>
                            <div className="contact-details ms-3">
                              <h5 className="mb-1">Web Sitesi</h5>
                              <p className="mb-0">
                                <a href={`https://${companyData.website}`} target="_blank" rel="noopener noreferrer">
                                  {companyData.website}
                                </a>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default CompanyProfile; 