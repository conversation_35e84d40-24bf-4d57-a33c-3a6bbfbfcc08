@import url('../../styles/colors.css');

/* ===================== AVANTAJLAR BÖLÜMÜ ======================*/
.advantages-section {
  padding: 100px 0;
  background-color: #f9f9f9;
  position: relative;
}

/* Arka Plan Deseni */
.advantages-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(#eee 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.4;
}

/* Header */
.advantages-header {
  text-align: center;
  margin-bottom: 70px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 2;
}

.advantages-header h2 {
  font-size: 32px;
  font-weight: 500;
  color: #222;
  margin-bottom: 16px;
  letter-spacing: -0.5px;
}

.advantages-header p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
  max-width: 550px;
  margin: 0 auto;
}

/* Avantajlar Grid */
.advantages-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

/* Avantaj Kartı */
.advantage-item {
  background-color: #fff;
  border-radius: 8px;
  padding: 35px 30px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
  border: 1px solid #f0f0f0;
}

.advantage-item:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.07);
  border-color: #e8e8e8;
}

.advantage-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  transition: width 0.4s ease;
}

.advantage-item:hover::after {
  width: 100%;
}

.advantage-icon {
  margin-bottom: 20px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  color: var(--primary-color);
  background-color: rgba(var(--primary-color-rgb), 0.07);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.advantage-item:hover .advantage-icon {
  background-color: var(--primary-color);
  color: #fff;
  transform: scale(1.05);
}

.advantage-content {
  width: 100%;
}

.advantage-item h3 {
  font-size: 18px;
  color: #222;
  margin-bottom: 12px;
  font-weight: 600;
  letter-spacing: -0.3px;
}

.advantage-item p {
  font-size: 14px;
  line-height: 1.5;
  color: #666;
  margin: 0;
}

/* CTA Bölümü */
.advantages-cta {
  text-align: center;
  margin-top: 30px;
  position: relative;
  z-index: 2;
}

.cta-button {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  font-size: 16px;
  font-weight: 500;
  padding: 14px 36px;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 8px 15px var(--primary-shadow);
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.cta-button:hover {
  background-color: var(--primary-dark);
  box-shadow: 0 10px 25px var(--primary-shadow);
  color: white;
}

.cta-button:hover::before {
  left: 100%;
}

/* Responsive Düzenlemeler */
@media (max-width: 1199px) {
  .advantages-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .advantage-item {
    padding: 30px 25px;
  }
}

@media (max-width: 991px) {
  .advantages-section {
    padding: 80px 0;
  }
  
  .advantages-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .advantages-header {
    margin-bottom: 50px;
  }
  
  .advantages-header h2 {
    font-size: 30px;
  }
  
  .advantage-item h3 {
    font-size: 17px;
  }
}

@media (max-width: 767px) {
  .advantages-section {
    padding: 60px 0;
  }
  
  .advantages-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .advantages-header {
    margin-bottom: 40px;
  }
  
  .advantages-header h2 {
    font-size: 26px;
  }
  
  .advantages-header p {
    font-size: 15px;
  }
  
  .advantage-item {
    padding: 25px;
  }
} 