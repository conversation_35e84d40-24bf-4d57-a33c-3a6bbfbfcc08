import React, { useEffect } from 'react';
import { Table, Badge, But<PERSON>, Spinner } from 'react-bootstrap';
import { FaFileAlt, FaFileUpload } from 'react-icons/fa';
import { useDispatch, useSelector } from 'react-redux';
import { fetchContracts, uploadContractFile } from '../../redux/slices/contractsSlice';
import PropTypes from 'prop-types';
import './style.css';

/**
 * Sözleşme yönetimi bileşeni
 * Sözleşme listesi ve işlemleri gösterir
 */
const ContractManagement = () => {
  const dispatch = useDispatch();
  const { contracts, loading, error } = useSelector((state) => state.contracts);

  useEffect(() => {
    // Bileşen yüklendiğinde sözleşmeleri getir
    dispatch(fetchContracts());
  }, [dispatch]);

  // Hata durumunu göster
  if (error) {
    return (
      <div className="alert alert-danger">
        <strong>Hata:</strong> {error}
      </div>
    );
  }

  // Yükleme durumunu göster
  if (loading) {
    return (
      <div className="text-center p-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">Sözleşmeler yükleniyor...</p>
      </div>
    );
  }

  // Veri yoksa boş durum mesajını göster
  if (!contracts || !contracts.length) {
    return (
      <div className="text-center p-4">
        <p>Henüz bir sözleşme bulunmamaktadır.</p>
      </div>
    );
  }

  return (
    <div className="contract-management">
      <div className="table-responsive">
        <Table hover>
          <thead>
            <tr>
              <th>Sözleşme No</th>
              <th>Müşteri</th>
              <th>Başlangıç</th>
              <th>Bitiş</th>
              <th>Araç Sayısı</th>
              <th>Segment</th>
              <th>Toplam Tutar</th>
              <th>Durum</th>
              <th>İşlem</th>
            </tr>
          </thead>
          <tbody>
            {contracts.map(contract => (
              <tr key={contract.id}>
                <td>{contract.id}</td>
                <td>{contract.customerName}</td>
                <td>{contract.startDate}</td>
                <td>{contract.endDate}</td>
                <td>{contract.vehicleCount}</td>
                <td>{contract.segment}</td>
                <td>{contract.totalAmount}</td>
                <td>
                  <Badge bg={contract.status === 'İmzalandı' ? 'success' : 'warning'}>
                    {contract.status}
                  </Badge>
                </td>
                <td>
                  {contract.status === 'İmza Bekleniyor' ? (
                    <Button variant="primary" size="sm" onClick={() => handleUploadContract(contract.id, dispatch)}>
                      <FaFileUpload className="me-1" /> Sözleşme Yükle
                    </Button>
                  ) : (
                    <Button variant="outline-secondary" size="sm" onClick={() => handleViewContract(contract.id)}>
                      <FaFileAlt className="me-1" /> Görüntüle
                    </Button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </div>
    </div>
  );
};

// İşlem fonksiyonları
const handleUploadContract = (id, dispatch) => {
  
  // Gerçek bir uygulama için dosya seçicisi açılacak
  // Burada örnek olarak dosya seçilmiş gibi davranıyoruz
  const mockFile = new File(['(mock file content)'], 'contract.pdf', { type: 'application/pdf' });
  
  // Dosyayı yüklemek için Redux action'ını dispatch et
  dispatch(uploadContractFile({ contractId: id, file: mockFile }));
};

const handleViewContract = (id) => {
  // Sözleşme detay ekranına yönlendirilecek
};

export default ContractManagement; 