server {
    listen 80;
    server_name localhost;

    root /usr/share/nginx/html;
    index index.html;

    # Gzip sıkıştırma ayarları
    gzip on;
    gzip_vary on;
    gzip_min_length 10240;
    gzip_proxied expired no-cache no-store private auth;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/x-javascript application/xml application/json;
    gzip_disable "MSIE [1-6]\.";

    # Güvenlik başlıkları
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # SPA uygulaması için tüm istekleri index.html'e yönlendirme
    location / {
        try_files $uri $uri/ /index.html;
    }

    # SPA rotaları için özel yönlendirme (404 hatasını önlemek için)
    location /ihale-sureci-detay {
        try_files $uri /index.html;
    }

    # API istekleri için örnek yönlendirme (kendi API adresinizi buraya ekleyin)
    # location /api {
    #     proxy_pass http://backend-api-service:8080;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection 'upgrade';
    #     proxy_set_header Host $host;
    #     proxy_cache_bypass $http_upgrade;
    # }

    # Statik dosyalar için cache ayarları
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # 404 hataları için index.html'e yönlendirme
    error_page 404 =200 /index.html;
} 