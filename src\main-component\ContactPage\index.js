import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";
import PageTitle from "../../components/PageTitle";
import Contact from "../../components/Contact";

const ContactPage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle pageTitle={t("header-navigation.contact")} pagesub={t("header-navigation.contact")} />
      <Contact />
    </Fragment>
  );
};
export default ContactPage;
