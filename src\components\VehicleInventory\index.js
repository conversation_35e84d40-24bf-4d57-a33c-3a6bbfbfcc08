import React from 'react';
import { Row, Col, Button, Table, Badge } from 'react-bootstrap';
import { FaCarAlt } from 'react-icons/fa';
import PropTypes from 'prop-types';
import './style.css';

/**
 * Araç envanteri bileşeni
 * Şirkete ait tüm araçları listeler
 */
const VehicleInventory = ({ vehicles }) => {
  if (!vehicles || !vehicles.length) {
    return (
      <div className="text-center p-4">
        <p>Herhangi bir araç kaydı bulunmamaktadır.</p>
      </div>
    );
  }

  return (
    <div className="vehicle-inventory">
      <Row className="mb-3">
        <Col>
          <Button variant="success" onClick={handleAddVehicle}>
            <FaCarAlt className="me-2" /> <PERSON><PERSON>
          </Button>
        </Col>
      </Row>
      <div className="table-responsive">
        <Table hover>
          <thead>
            <tr>
              <th>Araç ID</th>
              <th>Marka</th>
              <th>Model</th>
              <th>Segment</th>
              <th>Yıl</th>
              <th>Durum</th>
              <th>Konum</th>
              <th>İşlem</th>
            </tr>
          </thead>
          <tbody>
            {vehicles.map(vehicle => (
              <tr key={vehicle.id}>
                <td>{vehicle.id}</td>
                <td>{vehicle.brand}</td>
                <td>{vehicle.model}</td>
                <td>{vehicle.segment}</td>
                <td>{vehicle.year}</td>
                <td>
                  <Badge bg={vehicle.status === 'Uygun' ? 'success' : 'secondary'}>
                    {vehicle.status}
                  </Badge>
                </td>
                <td>{vehicle.location}</td>
                <td>
                  <Button 
                    variant="outline-primary" 
                    size="sm" 
                    className="me-1"
                    onClick={() => handleEditVehicle(vehicle.id)}
                  >
                    Düzenle
                  </Button>
                  <Button 
                    variant="outline-secondary" 
                    size="sm"
                    onClick={() => handleViewVehicle(vehicle.id)}
                  >
                    Detay
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      </div>
    </div>
  );
};

// İşlem fonksiyonları
const handleAddVehicle = () => {
  // Araç ekleme formuna yönlendirilecek
};

const handleEditVehicle = (id) => {
  // Araç düzenleme formuna yönlendirilecek
};

const handleViewVehicle = (id) => {
  // Araç detay sayfasına yönlendirilecek
};

VehicleInventory.propTypes = {
  vehicles: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      brand: PropTypes.string.isRequired,
      model: PropTypes.string.isRequired,
      segment: PropTypes.string.isRequired,
      year: PropTypes.number.isRequired,
      status: PropTypes.string.isRequired,
      location: PropTypes.string.isRequired
    })
  )
};

export default VehicleInventory; 