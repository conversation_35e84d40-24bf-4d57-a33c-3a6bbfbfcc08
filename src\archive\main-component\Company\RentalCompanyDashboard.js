import React, { Fragment, useState, useEffect } from 'react';
import { Container, Row, Col, Card, Nav, Tab, Badge } from 'react-bootstrap';
import { useSelector } from 'react-redux';
import { FaFileAlt, FaHandshake, FaCarAlt } from 'react-icons/fa';
import Header from "../../components/Header";
import Footer from "../../components/Footer";
import CompanyHero from '../../components/CompanyHero';
import ContractManagement from '../../components/ContractManagement';
import DashboardStats from '../../components/DashboardStats';
import TenderManagement from '../../components/TenderManagement';
import VehicleInventory from '../../components/VehicleInventory';
import PricingInfo from '../../components/PricingInfo';
import { useTranslation } from 'react-i18next';
import { getTendersByCompany } from '../../services/tendersService';
import './RentalCompanyDashboard.css';

const RentalCompanyDashboard = () => {
  const { user } = useSelector((state) => state.auth);
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(t('dashboard.tabs.tenders'));
  const [tenderData, setTenderData] = useState({
    newTenders: [],
    pendingTenders: [],
    rejectedTenders: [],
    approvedTenders: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Hero bölümü için metinler
  const heroTitle = t("hero.company.title", { name: user?.name || t('common.supplier') }) || `${t('common.welcome')}, ${user?.name || t('common.supplier')}`;
  const heroDescription = t("hero.company.subtitle") || t('hero.company.description');

  // Örnek veri - gerçek uygulamada API'den gelecek
  const dashboardData = {
    newTenders: tenderData.newTenders.length,
    pendingTenders: tenderData.pendingTenders.length,
    rejectedTenders: tenderData.rejectedTenders.length,
    approvedTenders: tenderData.approvedTenders.length,
    contracts: 2,
    vehicles: 47
  };

  useEffect(() => {
    const fetchTenders = async () => {
      try {
        setIsLoading(true);
        // Şirketin ID'sini kullanarak ihale verilerini getir
        const companyId = user?.id || 1; // Kullanıcı yoksa varsayılan değer
        const response = await getTendersByCompany(companyId);
        
        if (response && response.success) {
          setTenderData({
            newTenders: response.data.newTenders || [],
            pendingTenders: response.data.pendingTenders || [],
            rejectedTenders: response.data.rejectedTenders || [],
            approvedTenders: response.data.approvedTenders || []
          });
        } else {
          throw new Error(response?.message || 'İhale verileri alınamadı');
        }
      } catch (err) {
        console.error('İhale verileri yüklenirken hata oluştu:', err);
        setError(err.message || 'İhaleler yüklenirken bir hata oluştu');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenders();
  }, [user]);

  return (
    <Fragment>
      <Header />
      
      {/* CompanyHero Bölümü */}
      <CompanyHero
        title={heroTitle}
        description={heroDescription}
      />

      {/* Dashboard İstatistikleri */}
      <DashboardStats stats={dashboardData} />

      {/* Ana Sekme Bölümü */}
      <section className="dashboard-main-section section_70">
        <Container>
          {isLoading ? (
            <div className="text-center p-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">{t('common.loading')}</span>
              </div>
              <p className="mt-3">{t('tender.loading_tenders')}</p>
            </div>
          ) : error ? (
            <div className="alert alert-danger" role="alert">
              <h4 className="alert-heading">{t('common.error')}</h4>
              <p>{error}</p>
              <hr />
              <p className="mb-0">{t('common.try_again_later')}</p>
            </div>
          ) : (
            <TenderManagement data={tenderData} />
          )}
        </Container>
      </section>
      
      {/* Teklif Ücretlendirmesi Bölümü */}
      {/* <PricingInfo /> */}

      <Footer />
    </Fragment>
  );
};

export default RentalCompanyDashboard; 