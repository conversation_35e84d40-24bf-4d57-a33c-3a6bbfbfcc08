import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { Row, Col, Form } from "react-bootstrap";
import { 
  FaSearch, 
  FaCar, 
  FaGasPump, 
  FaCogs, 
  FaClipboardCheck, 
  FaCalendarDay
} from "react-icons/fa";
import { 
  fetchVehicles,
  fetchVehicleBrands,
  fetchVehicleModelsByBrand,
  fetchVehicleCategories,
  setFilters
} from "../../redux/slices/vehiclesSlice";
import { setTenderParams } from "../../redux/slices/tenderSlice";

import "./style.css";

const FindTender = ({buttonText}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  // Redux store'dan verileri al
  const { 
    brands, 
    categories,
    modelsByBrand,
    filters,
    loading 
  } = useSelector(state => state.vehicles);
  
  // Form state'i
  const [formData, setFormData] = useState({
    brand: '',
    model: '',
    type: '',
    fuelType: '',
    transmission: '',
    minPrice: '',
    maxPrice: '',
    sort: 'price_asc'
  });
  
  // Sabit değerler (sonradan backend'den gelebilir)
  const fuelTypes = ['Benzin', 'Dizel', 'Elektrik', 'Hibrit', 'LPG'];
  const transmissionTypes = ['Otomatik', 'Manuel', 'Yarı Otomatik'];
  const rentalPeriods = [3, 6, 12, 18, 24, 36];
  
  // Sayfa yüklendiğinde temel verileri çek
  useEffect(() => {
    dispatch(fetchVehicleBrands());
    dispatch(fetchVehicleCategories());
  }, [dispatch]);
  
  // Marka değiştiğinde ilgili modelleri getir
  useEffect(() => {
    if (formData.brand) {
      dispatch(fetchVehicleModelsByBrand(formData.brand));
    }
  }, [dispatch, formData.brand]);
  
  // Form alanları değiştiğinde 
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Eğer marka değiştiyse, model seçimini sıfırla
    if (name === 'brand') {
      setFormData(prev => ({
        ...prev,
        model: ''
      }));
    }
  };
  
  // Form gönderildiğinde
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Eğer buton metni "İhale Başlat" ise ihale sayfasına yönlendir
    if (buttonText === t("hero.customer.buttonText") || buttonText === "İhale Başlat") {
      // Boş olmayan ve 'sort' olmayan form alanlarını içeren nesne oluştur
      const tenderParams = {};
      
      Object.entries(formData).forEach(([key, value]) => {
        if (value && key !== 'sort') {
          tenderParams[key] = value;
        }
      });
      
      // Redux store'a kaydet
      dispatch(setTenderParams(tenderParams));
      
      // TenderStartPage'e yönlendir (artık URL parametreleri olmadan)
      navigate('/tender-start?tab=new-tender');
    } else {
      // Normal arama işlemi
      dispatch(setFilters({
        ...formData,
        page: 1 // Yeni arama için sayfa numarasını sıfırla
      }));
      dispatch(fetchVehicles(formData));
    }
  };
  
  return (
    <div className="findcar-area">
      <div>
        <Form onSubmit={handleSubmit}>
          <div>
            <Row className="g-3">
              <Col lg={2} md={4} sm={6} xs={12}>
                <div className="form-field-wrapper">
                  <div className="field-icon">
                    <FaCar className="text-danger" />
                  </div>
                  <Form.Select 
                    name="brand"
                    value={formData.brand}
                    onChange={handleChange}
                    className="form-select-with-icon"
                  >
                    <option value="">{t("findcar.brand")}</option>
                    {brands.map((brand, index) => (
                      <option key={index} value={brand.name}>{brand.name}</option>
                    ))}
                  </Form.Select>
                </div>
              </Col>
              
              <Col lg={2} md={4} sm={6} xs={12}>
                <div className="form-field-wrapper">
                  <div className="field-icon">
                    <FaCar className="text-danger" />
                  </div>
                  <Form.Select
                    name="model"
                    value={formData.model}
                    onChange={handleChange}
                    disabled={!formData.brand}
                    className="form-select-with-icon"
                  >
                    <option value="">{t("findcar.model")}</option>
                    {formData.brand && modelsByBrand[formData.brand]?.map((model, index) => (
                      <option key={index} value={model.name}>{model.name}</option>
                    ))}
                  </Form.Select>
                </div>
              </Col>
              
              <Col lg={2} md={4} sm={6} xs={12}>
                <div className="form-field-wrapper">
                  <div className="field-icon">
                    <FaGasPump className="text-danger" />
                  </div>
                  <Form.Select
                    name="fuelType"
                    value={formData.fuelType}
                    onChange={handleChange}
                    className="form-select-with-icon"
                  >
                    <option value="">{t("findcar.fuel_type")}</option>
                    {fuelTypes.map((fuel, index) => (
                      <option key={index} value={fuel}>{fuel}</option>
                    ))}
                  </Form.Select>
                </div>
              </Col>
              
              <Col lg={2} md={4} sm={6} xs={12}>
                <div className="form-field-wrapper">
                  <div className="field-icon">
                    <FaCogs className="text-danger" />
                  </div>
                  <Form.Select
                    name="transmission"
                    value={formData.transmission}
                    onChange={handleChange}
                    className="form-select-with-icon"
                  >
                    <option value="">{t("findcar.transmission")}</option>
                    {transmissionTypes.map((transmission, index) => (
                      <option key={index} value={transmission}>{transmission}</option>
                    ))}
                  </Form.Select>
                </div>
              </Col>
              
              <Col lg={2} md={4} sm={6} xs={12}>
                <div className="form-field-wrapper">
                  <div className="field-icon">
                    <FaClipboardCheck className="text-danger" />
                  </div>
                  <Form.Select
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    className="form-select-with-icon"
                  >
                    <option value="">{t("findcar.type")}</option>
                    {categories.map((category, index) => (
                      <option key={index} value={category.name}>{category.name}</option>
                    ))}
                  </Form.Select>
                </div>
              </Col>
              
              <Col lg={2} md={4} sm={6} xs={12}>
                <div className="form-field-wrapper">
                  <div className="field-icon">
                    <FaCalendarDay className="text-danger" />
                  </div>
                  <Form.Select
                    name="rentalPeriod"
                    value={formData.rentalPeriod}
                    onChange={handleChange}
                    className="form-select-with-icon"
                  >
                    <option value="">{t("findcar.rental_period")}</option>
                    {rentalPeriods.map((period, index) => (
                      <option key={index} value={period}>{period} {t("findcar.month")}</option>
                    ))}
                  </Form.Select>
                </div>
              </Col>
              
              <Col lg={12} md={12} sm={12} className="d-flex justify-content-end mt-3">
                <div className="search-btn-container">
                  <button
                    type="submit"
                    className="gauto-btn"
                    disabled={loading}>
                    <FaSearch className="me-2" /> 
                    {loading ? t('findcar.searching') : buttonText || t('findcar.find_car')}
                  </button>
                </div>
              </Col>
            </Row>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default FindTender;
