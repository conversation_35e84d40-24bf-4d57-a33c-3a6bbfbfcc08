import React from 'react';
import { Link } from 'react-router-dom';
import PropTypes from 'prop-types';
import { 
  FaFacebook, 
  FaTwitter, 
  FaLinkedin, 
  FaPinterest, 
  FaInstagram,
  FaYoutube,
  FaSkype,
  FaVimeo
} from 'react-icons/fa';
import './style.css';

/**
 * SocialLinks - Projedeki tüm sosyal medya linkleri için kullanılacak standart bileşen
 * 
 * @param {Object} props
 * @param {Array} props.platforms - Gösterilecek sosyal medya platformları
 * @param {string} props.size - İkon boyutu: "sm", "md", "lg"
 * @param {string} props.variant - Görünüm varyantı: "default", "rounded", "filled"
 * @param {string} props.className - Ek CSS sınıfları
 * @param {function} props.onLinkClick - Link tıklama olayı işleyicisi
 * @returns {React.ReactElement}
 */
const SocialLinks = ({
  platforms = ['facebook', 'twitter', 'linkedin', 'instagram'],
  size = 'md',
  variant = 'default',
  className = '',
  onLinkClick = (e) => e.preventDefault(),
}) => {
  // İkon haritası
  const iconMap = {
    facebook: <FaFacebook />,
    twitter: <FaTwitter />,
    linkedin: <FaLinkedin />,
    pinterest: <FaPinterest />,
    instagram: <FaInstagram />,
    youtube: <FaYoutube />,
    skype: <FaSkype />,
    vimeo: <FaVimeo />
  };

  // CSS sınıflarını oluştur
  const linkClasses = [
    'social-link',
    `social-${variant}`,
    `size-${size}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <ul className="social-links">
      {platforms.map((platform, index) => (
        <li key={index}>
          <Link 
            to="/" 
            className={`${linkClasses} ${platform}`}
            onClick={onLinkClick}
            aria-label={`Visit our ${platform} page`}
          >
            {iconMap[platform] || null}
          </Link>
        </li>
      ))}
    </ul>
  );
};

SocialLinks.propTypes = {
  platforms: PropTypes.arrayOf(PropTypes.string),
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  variant: PropTypes.oneOf(['default', 'rounded', 'filled']),
  className: PropTypes.string,
  onLinkClick: PropTypes.func
};

export default SocialLinks; 