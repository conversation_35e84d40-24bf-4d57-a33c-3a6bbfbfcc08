# Components Klasör<PERSON>, projede kullan<PERSON>lan yeniden kullanılabilir, atomik bileşenleri içerir.

## Kurallar ve Standartlar

1. Her bileşen kendi klasöründe olmalıdır:
   ```
   ComponentName/
   ├── index.js
   ├── style.css
   └── ComponentName.test.js (opsiyonel)
   ```

2. <PERSON><PERSON><PERSON><PERSON><PERSON>, mümkün olduğunca küçük ve tek bir sorumluluk prensibine uygun olmalıdır

3. Bileşenler aşağıdaki kategorilere ayrılır:
   - **UI Bileşenleri**: Button, Card, Input vb. temel UI elementleri
   - **Layout Bileşenleri**: Header, Footer, Sidebar vb.
   - **Feature Bileşenleri**: Belirli özelliklere odaklanan bileşenler

## Mevcut Sorunlar ve Çözümler

Projede bazı bileşenler hem `components/` hem de `main-component/` klasörlerinde tekrarlanıyor. Özellikle About, Blog gibi bileşenler için bu durum söz konusu.

### Öneriler:

1. `AboutPage` bileşeni `components/AboutPage` konumundan kaldırılmalı, sadece `main-component/AboutPage` içinde tutulmalıdır.

2. Benzer şekilde, diğer tekrarlanan bileşenler (Blog, Service vb.) için roller net şekilde ayrılmalıdır:
   - `components/`: Yeniden kullanılabilir parçalar (örn. `Card`, `Button`)
   - `main-component/`: Sayfa bileşenleri (örn. `AboutPage`, `BlogPage`) 