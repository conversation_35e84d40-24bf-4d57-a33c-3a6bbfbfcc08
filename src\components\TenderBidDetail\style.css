.tender-bid-detail {
  background: var(--gray-50);
  min-height: 600px;
  padding: 50px 0;
}

.tender-bid-detail .container {
  max-width: 1200px;
}

.tender-bid-detail .compact-container {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 20px var(--primary-shadow);
  margin: 0 auto;
  padding: 25px 30px;
  margin-bottom: 30px;
}

.tender-header {
  padding: 10px 12px;
  margin-bottom: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tender-header-main {
  margin-bottom: 0;
}

.tender-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.tender-subtitle, .tender-header p {
  font-size: 0.85rem;
  margin-bottom: 5px;
  line-height: 1.3;
}

.tender-body {
  padding: 15px;
  margin-bottom: 15px;
}

.tender-tabs {
  margin-top: 0;
  padding: 0;
  border-radius: 0;
}

.tender-tabs .nav-link {
  padding: 6px 10px;
  font-size: 0.8rem;
}

.tender-tab-content {
  padding: 25px;
}

.tender-status {
  padding: 6px 10px;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  border-radius: 20px;
}

.tender-status-active {
  background-color: rgba(25, 135, 84, 0.15) !important;
  color: #198754 !important;
}

.tender-status-pending {
  background-color: rgba(255, 193, 7, 0.15) !important;
  color: #cc9a06 !important;
}

.tender-status-completed {
  background-color: var(--primary-transparent) !important;
  color: var(--primary-color) !important;
}

.tender-status-cancelled {
  background-color: rgba(220, 53, 69, 0.15) !important;
  color: #dc3545 !important;
}

.tender-status-draft {
  background-color: rgba(108, 117, 125, 0.15) !important;
  color: #6c757d !important;
}

.tender-status-expired {
  background-color: rgba(102, 16, 242, 0.15) !important;
  color: #6610f2 !important;
}

.tender-card {
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.tender-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
  border-color: var(--primary-shadow);
}

.tender-card .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
}

.tender-card .card-body {
  padding: 20px;
}

.tender-info-grid {
  gap: 10px;
  margin-bottom: 15px;
}

.tender-info-item {
  padding: 8px;
}

.tender-info-label {
  font-size: 0.8rem;
  margin-bottom: 3px;
}

.tender-info-value {
  font-size: 0.9rem;
}

.remaining-time {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.25s ease;
  margin-bottom: 0;
  position: relative;
}

.remaining-time:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.remaining-time-header {
  position: relative;
  background-color: transparent;
  color: #334155;
  text-align: left;
  padding: 12px 12px 6px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: auto;
  border-bottom: none;
}

.remaining-time-header h6 {
  font-size: 0.8rem;
  margin: 0;
  font-weight: 500;
  color: #64748b;
  letter-spacing: 0.5px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  text-transform: none;
}

.remaining-time-header h6 svg {
  margin-right: 6px;
  font-size: 0.9rem;
  color: #dc2626;
}

.remaining-time-body {
  padding: 0 12px 12px;
  text-align: center;
}

.remaining-time-counter {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 0;
  flex-wrap: wrap;
}

.time-unit {
  background-color: #fef2f2;
  border-radius: 6px;
  padding: 8px 6px;
  min-width: 52px;
  text-align: center;
  position: relative;
  border: 1px solid rgba(220, 38, 38, 0.1);
}

.time-unit:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #dc2626;
}

.time-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: #dc2626;
  line-height: 1;
  margin-bottom: 4px;
}

.time-label {
  font-size: 0.65rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.remaining-time-text {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 8px;
  font-style: italic;
}

.tender-footer {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-right: 4px solid #1a73e8;
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
  .compact-container {
    padding: 10px;
    margin: 10px auto;
  }

  .tender-header {
    padding: 10px;
    margin-bottom: 10px;
  }

  .tender-title {
    font-size: 1.1rem;
  }

  .tender-compact-body {
    padding: 10px;
  }
}

/* TenderBidForm component için ek stiller */
.bid-form-section {
  margin-bottom: 20px;
}

.bid-form-section-title {
  font-size: 1rem;
  margin-bottom: 10px;
  padding-bottom: 8px;
}

.vehicle-item {
  padding: 10px;
  margin-bottom: 10px;
}

.document-upload-area {
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  padding: 30px 20px;
  text-align: center;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.document-upload-area:hover {
  border-color: var(--primary-color);
  background-color: #f0f7ff;
}

.bid-summary {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.bid-summary-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.bid-summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.bid-summary-total {
  border-top: 1px solid #dee2e6;
  margin-top: 15px;
  padding-top: 15px;
  font-weight: 600;
}

/* TenderVehicleList component için ek stiller */
.vehicle-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.02);
}

.vehicle-card:hover {
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.vehicle-header {
  background-color: #f8f9fa;
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
}

.vehicle-image {
  height: 180px;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vehicle-image img {
  max-height: 100%;
  object-fit: contain;
}

.vehicle-details {
  padding: 15px;
}

.vehicle-info-item {
  display: flex;
  margin-bottom: 8px;
}

.vehicle-info-label {
  width: 40%;
  font-size: 0.85rem;
  color: #6c757d;
}

.vehicle-info-value {
  width: 60%;
  font-weight: 500;
}

.vehicle-info-item:hover {
  background-color: #f8f9fa;
  border-color: var(--primary-color);
}

.customer-info-card {
  display: flex;
  align-items: center;
  background-color: #f9fafd;
  border: 1px solid #e9edf4;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.customer-logo {
  width: 70px;
  height: 70px;
  object-fit: contain;
  margin-right: 20px;
  border-radius: 8px;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #e9edf4;
}

.customer-details {
  flex: 1;
}

.customer-name {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
}

.customer-meta {
  color: #6c757d;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
}

.customer-meta svg {
  margin-right: 5px;
  color: #ff4d37;
}

.contact-info {
  margin-top: 15px;
}

.contact-info a {
  color: #ff4d37;
  text-decoration: none;
  transition: color 0.2s;
}

.contact-info a:hover {
  color: #e0331e;
  text-decoration: underline;
}

/* Tab styling to match site design */
.nav-tabs {
  border-bottom: 1px solid #e9edf4;
  margin-bottom: 25px;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  color: #5d6778;
  font-weight: 500;
  padding: 12px 20px;
  transition: all 0.2s;
  position: relative;
}

.nav-tabs .nav-link:hover {
  border-color: transparent;
  color: var(--primary-color);
}

.nav-tabs .nav-link.active {
  color: var(--primary-color);
  background-color: #fff;
  border-color: #e9edf4 #e9edf4 #fff;
  font-weight: 600;
}

.nav-tabs .nav-link.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--primary-color);
}

/* Button styling */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 3px 10px var(--primary-shadow);
  transition: all 0.3s;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  box-shadow: 0 5px 15px var(--primary-shadow);
  transform: translateY(-2px);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Form elements to match site design */
.form-control {
  border: 1px solid #e9edf4;
  border-radius: 8px;
  padding: 10px 15px;
  height: auto;
  box-shadow: none;
  transition: all 0.2s;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem var(--primary-shadow);
}

.input-group-text {
  background-color: #f9fafd;
  border-color: #e9edf4;
}

/* Custom checkbox styling */
.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Alert styling */
.alert-success {
  background-color: #e8f5e9;
  border-color: #c8e6c9;
  color: #388e3c;
}

.alert-danger {
  background-color: #ffebee;
  border-color: #ffcdd2;
  color: #d32f2f;
}

.alert-warning {
  background-color: #fff8e1;
  border-color: #ffecb3;
  color: #ffa000;
}

.alert-info {
  background-color: var(--primary-transparent);
  border-color: var(--primary-shadow);
  color: #333333;
  border-radius: 8px;
  padding: 12px 15px;
  margin-bottom: 20px;
}

/* Table styling */
.table-responsive {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.table {
  margin-bottom: 0;
}

.table thead th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #e9ecef;
  padding: 12px 15px;
}

.table tbody td {
  vertical-align: middle;
  padding: 12px 15px;
  border-color: #edf2f7;
}

.table-hover tbody tr:hover {
  background-color: #f8f9fa;
}

/* Customer info bölümü için stil */
.customer-info {
  padding: 5px;
}

.customer-info p {
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  border-bottom: 1px dashed #eaeaea;
  padding-bottom: 0.75rem;
}

.customer-info p:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.customer-info strong {
  font-weight: 600;
  color: #495057;
  display: inline-block;
  width: 100px;
}

/* Tab stilleri */
.tender-tabs {
  margin-top: 15px;
  background-color: #f8f9fa;
  border-radius: 5px 5px 0 0;
  padding: 5px 5px 0 5px;
  border: 1px solid #eaeaea;
  border-bottom: none;
}

.tender-nav {
  border-bottom: none;
}

.tender-nav .nav-link {
  color: #495057;
  font-weight: 500;
  padding: 12px 18px;
  border: none;
  border-radius: 5px 5px 0 0;
  margin-right: 5px;
  transition: all 0.2s ease;
}

.tender-nav .nav-link:hover {
  background-color: var(--primary-transparent);
  color: var(--primary-color);
}

.tender-nav .nav-link.active {
  background-color: white;
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  font-weight: 600;
}

.tender-nav .badge {
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
}

/* Tender bileşeni geliştirmeleri */
.tender-description {
  color: #555;
  line-height: 1.6;
  margin-bottom: 20px;
}

.tender-meta {
  background: #f8f9fa;
  padding: 0;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  box-shadow: none;
}

.tender-meta-dates {
  margin-top: 5px;
  gap: 8px;
}

.tender-meta-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
}

.tender-meta-date-item {
  margin-bottom: 0;
}

/* Ortak kart stili */
.tender-meta-card {
  padding: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: white;
  transition: all 0.3s ease;
  border-left: 2px solid var(--primary-color);
}

.tender-meta-card:hover,
.tender-meta-date-item:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.tender-meta-date-header {
  background-color: #4f46e5;
  color: white;
  text-align: center;
  padding: 8px 12px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tender-meta-date-header.end-date {
  background-color: #ea580c;
}

.tender-meta-date-header h6 {
  font-size: 0.8rem;
  margin: 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.tender-meta-date-body {
  padding: 10px;
  text-align: center;
}

.tender-meta-date-day {
  font-size: 1.6rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
  margin-bottom: 2px;
}

.tender-meta-date-month {
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 2px;
}

.tender-meta-date-year {
  font-size: 0.75rem;
  font-weight: 500;
  color: #94a3b8;
}

.budget-range {
  margin-top: 5px;
  padding: 8px;
}

.budget-range:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.budget-range-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #eef2ff;
  border-radius: 50%;
  margin-right: 10px;
}

.budget-range-icon svg {
  color: #4f46e5;
  font-size: 0.9rem;
}

.budget-range-content {
  flex: 1;
}

.budget-range-label {
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
}

.budget-range-value {
  font-weight: 600;
  font-size: 0.95rem;
  color: #334155;
}

/* Mobil görünüm düzenlemeleri */
@media (max-width: 768px) {
  .tender-meta-dates {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .tender-meta-date-item {
    width: 100%;
    margin-bottom: 0;
  }

  .time-unit {
    min-width: 45px;
    padding: 5px;
  }

  .time-value {
    font-size: 1rem;
  }

  .time-label {
    font-size: 0.6rem;
  }

  .tender-header {
    padding: 10px;
    gap: 10px;
  }
}

.tender-submit-btn {
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  transition: all 0.3s ease;
  padding: 12px;
  margin-top: 10px;
  width: 100%;
}

.tender-submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
}

.tender-submit-btn:active {
  transform: translateY(0);
}

@media (max-width: 991px) {
  .tender-meta-grid {
    grid-template-columns: 1fr;
  }
}

:root {
  /* Global değişkenleri kullanmak için yerel tanımlamalar kaldırıldı */
  /* --primary-color ve --primary-rgb tanımları src/styles/colors.css dosyasından alınacak */
}

/* Kompakt panel ve container stilleri - Taşma sorununu gidermek için */
.compact-container {
  max-width: 1140px;
  margin: 10px auto;
  padding: 12px;
}

.tender-compact-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
  gap: 10px;
}

.tender-simple-tabs {
  border-radius: 6px;
  margin-bottom: 0;
  overflow: hidden;
}

.tender-compact-body {
  padding: 10px;
}

/* Ana container düzenlemesi */
.compact-container {
  max-width: 1140px;
  margin: 10px auto;
  padding: 12px;
}

.tender-compact-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
  gap: 10px;
}

/* Sayfa başlığı düzenlemesi */
.tender-header {
  padding: 10px 12px;
  margin-bottom: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid var(--primary-color);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tender-title {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.tender-subtitle, .tender-header p {
  font-size: 0.85rem;
  margin-bottom: 5px;
  line-height: 1.3;
}

/* Tender meta (başlık altındaki bilgiler) düzenlemesi */
.tender-meta-dates {
  margin-top: 5px;
  gap: 8px;
}

.tender-meta-date-item {
  margin-bottom: 0;
}

.budget-range {
  margin-top: 5px;
  padding: 8px;
}

/* Detay içeriği düzenlemesi */
.compact-detail-content {
  gap: 10px;
}

.detail-compact-row {
  margin-bottom: 0;
  gap: 10px;
}

.compact-card {
  margin-bottom: 0;
}

/* Responsive düzenlemeler */
@media (max-width: 576px) {
  .compact-container {
    margin: 5px auto;
    padding: 8px;
  }

  .tender-header {
    padding: 8px;
    margin-bottom: 5px;
  }

  .tender-title {
    font-size: 1.1rem;
  }

  .tender-compact-body {
    padding: 8px;
  }

  .compact-detail-content {
    gap: 8px;
  }
}

/* Tüm tabların daha kompakt görüntülenmesi için */
.tab-pane {
  padding: 5px 0;
}

/* İçerik alanlarını daha kompakt hale getirmek için */
.tender-detail-content,
.tender-vehicles-content,
.tender-documents-content {
  margin-bottom: 15px;
  font-size: 0.9rem;
}

/* Tablolar ve listeler için kompakt stil */
.table {
  font-size: 0.85rem;
}

.table thead th {
  padding: 8px 12px;
  font-size: 0.8rem;
}

.table tbody td {
  padding: 8px 12px;
}

/* Kart ve panel öğeleri için kompakt stiller */
.card, .card-body {
  padding: 10px;
}

.card-header {
  padding: 8px 10px;
  font-size: 0.95rem;
}

/* Araç kartları için kompakt stiller */
.vehicle-card {
  margin-bottom: 15px;
}

.vehicle-header {
  padding: 10px;
}

.vehicle-image {
  height: 150px;
}

.vehicle-details {
  padding: 10px;
}

/* Doküman listesi için kompakt stiller */
.document-item {
  padding: 8px;
  margin-bottom: 8px;
}

/* Müşteri bilgileri için kompakt stiller */
.customer-info-card {
  padding: 12px;
  margin-bottom: 15px;
  gap: 10px;
}

.customer-logo {
  width: 50px;
  height: 50px;
  margin-right: 15px;
  padding: 5px;
}

.customer-name {
  font-size: 1rem;
  margin-bottom: 5px;
}

/* Tab içeriğindeki boşlukları azaltma */
.tender-compact-body p {
  margin-bottom: 8px;
}

.tender-compact-body h1,
.tender-compact-body h2,
.tender-compact-body h3,
.tender-compact-body h4,
.tender-compact-body h5 {
  margin-bottom: 10px;
}

/* Ek kompakt stiller */
.compact-margins {
  margin-top: 8px;
  margin-bottom: 8px;
}

.compact-paddings {
  padding: 8px;
}

@media (max-width: 576px) {
  .tab-pane {
    padding: 0;
  }

  .tender-compact-body {
    padding: 8px;
  }

  .tender-header {
    padding: 8px;
    margin-bottom: 8px;
  }

  .tender-title {
    font-size: 1rem;
  }

  .table thead th,
  .table tbody td {
    padding: 6px 8px;
  }

  .form-control {
    padding: 5px 8px;
    font-size: 0.8rem;
  }
}

/* Tab içeriklerinin genel kompakt stili */
.tab-content {
  font-size: 0.9rem;
}

/* Form elemanları için kompakt stiller */
.form-group {
  margin-bottom: 10px;
}

.form-control {
  padding: 6px 10px;
  font-size: 0.85rem;
}

.form-label {
  font-size: 0.85rem;
  margin-bottom: 3px;
}

.btn {
  padding: 5px 10px;
  font-size: 0.85rem;
}

.btn-sm {
  font-size: 0.8rem;
  padding: 3px 6px;
}

/* Tüm ikonları ve etiketleri küçültmek için */
.badge {
  font-size: 0.7rem;
  padding: 3px 6px;
}

.bi, .fa, .fas {
  font-size: 0.9em;
}

/* Alert bileşenleri için */
.alert {
  padding: 10px;
  margin-bottom: 15px;
  font-size: 0.85rem;
}

.alert-heading {
  font-size: 1rem;
  margin-bottom: 8px;
}

/* Özel kompakt container düzenlemesi */
.tender-compact-body .tab-pane > div {
  margin-bottom: 10px;
}

/* Kayan elemanlar için kompakt düzen */
.tender-meta-grid {
  gap: 8px;
}

.tender-meta-date-item {
  padding: 0;
}

.tender-meta-date-header {
  height: 30px;
}

.tender-meta-date-header h6 {
  font-size: 0.75rem;
}

.tender-meta-date-day {
  font-size: 1.2rem;
  margin-bottom: 0;
}

.tender-meta-date-month,
.tender-meta-date-year {
  font-size: 0.7rem;
  margin-bottom: 0;
}

.tender-meta-date-body {
  padding: 5px;
}

/* Kalan süre sayacı için kompakt düzen */
.remaining-time {
  margin-bottom: 10px;
}

.remaining-time-header {
  padding: 8px 8px 4px;
}

.remaining-time-body {
  padding: 0 8px 8px;
}

.time-unit {
  min-width: 40px;
  padding: 5px;
}

.time-value {
  font-size: 1rem;
  margin-bottom: 2px;
}

.time-label {
  font-size: 0.6rem;
}

/* Detaylar sekmesi için kompakt tasarım */
.compact-detail-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-compact-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 8px;
}

.detail-compact-col {
  flex: 1;
  min-width: 230px;
}

/* Mini kart stilleri */
.mini-card {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e9ecef;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.mini-card-header {
  background-color: #f8f9fa;
  padding: 6px 10px;
  border-bottom: 1px solid #e9ecef;
}

.mini-card-body {
  padding: 10px;
}

.mini-header-title {
  font-size: 0.85rem;
  font-weight: 600;
}

.mini-label {
  font-size: 0.8rem;
  margin-bottom: 2px;
}

.mini-value {
  font-size: 0.85rem;
}

.small-icon {
  font-size: 0.8rem;
}

/* Kompakt kart stilleri */
.compact-card {
  border-radius: 6px;
  border: 1px solid #e9ecef;
  margin-bottom: 8px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.03);
}

.compact-card-header {
  background-color: #f8f9fa;
  padding: 8px 10px;
  border-bottom: 1px solid #e9ecef;
}

.compact-card-header h6 {
  font-size: 0.9rem;
  font-weight: 600;
}

.compact-card-body {
  padding: 10px;
}

.compact-description {
  font-size: 0.85rem;
  margin-bottom: 10px;
  line-height: 1.4;
}

.compact-customer-info .customer-info-card {
  margin-bottom: 8px;
  padding: 10px;
}

.compact-btn {
  font-size: 0.8rem;
  padding: 5px 10px;
  height: auto;
  min-height: 30px;
}

/* Bilgi grid stilleri */
.compact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 10px;
}

.compact-info-item {
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 2px solid var(--primary-color);
}

.compact-info-label {
  font-size: 0.75rem;
  color: #666;
  font-weight: 600;
  margin-bottom: 2px;
}

.compact-info-value {
  font-size: 0.8rem;
  color: #333;
}

/* Mobil Uyumluluk */
@media (max-width: 576px) {
  .detail-compact-row {
    flex-direction: column;
  }

  .detail-compact-col {
    width: 100%;
  }

  .compact-info-grid {
    grid-template-columns: 1fr;
  }

  .compact-card-body {
    padding: 8px;
  }
}

/* Müşteri bilgisi kartı için kompakt stiller */
.compact-customer-data {
  font-size: 0.85rem;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.customer-name-compact {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 15px;
  margin-bottom: 5px;
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 5px;
  display: inline-block;
}

.customer-detail-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
}

.customer-icon {
  font-size: 0.7rem;
  color: #666;
  flex-shrink: 0;
  width: 14px;
}

.customer-text, .customer-link {
  font-size: 0.8rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.customer-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

.customer-link:hover {
  text-decoration: underline;
}

/* Mini Header için ince ayarlar */
.mini-card {
  margin-bottom: 8px;
}

.mini-card-header {
  background-color: #f8f9fa;
}

.mini-header-title {
  font-size: 0.8rem;
  font-weight: 600;
}

.mini-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.mini-value {
  font-size: 0.8rem;
}

/* Alert ince ayarları */
.alert.small {
  font-size: 0.75rem;
  padding: 5px 8px;
}

/* Tüm card başlıkları için tutarlı stiller */
.compact-card-header, .mini-card-header {
  display: flex;
  align-items: center;
}

/* Bölümler arası boşluklar için son ayarlar */
.compact-detail-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-compact-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.detail-compact-col {
  flex: 1;
  min-width: 230px;
}

/* Mobil kompakt görünüm için ayarlar */
@media (max-width: 576px) {
  .customer-name-compact {
    font-size: 0.85rem;
  }

  .customer-text, .customer-link {
    font-size: 0.75rem;
  }

  .customer-detail-item {
    font-size: 0.75rem;
  }
}

/* Yeni başlangıç-bitiş tarihi kartları için stiller */
.date-range-container {
  display: flex;
  align-items: center;
  margin: 10px 0 5px;
  flex-wrap: wrap;
  gap: 0;
}

.date-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  min-width: 180px;
  flex: 1;
  position: relative;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.date-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.date-card.start-date {
  border-left: 3px solid #4f46e5;
}

.date-card.end-date {
  border-left: 3px solid #ea580c;
}

.date-icon-wrapper {
  background-color: #f0f4f8;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  flex-shrink: 0;
}

.date-icon {
  font-size: 0.85rem;
}

.date-icon.start {
  color: #4f46e5;
}

.date-icon.end {
  color: #ea580c;
}

.date-content {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.date-label {
  font-size: 0.7rem;
  color: #64748b;
  margin-bottom: 2px;
  font-weight: 500;
}

.date-value {
  font-size: 0.85rem;
  font-weight: 600;
  color: #111827;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.date-connector {
  width: 40px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
  flex-shrink: 0;
  position: relative;
}

.connector-line {
  height: 2px;
  background: linear-gradient(to right, #4f46e5, #ea580c);
  width: 100%;
  position: absolute;
  z-index: 1;
}

.connector-icon {
  color: #64748b;
  font-size: 0.8rem;
  background-color: white;
  border-radius: 50%;
  padding: 2px;
  position: relative;
  z-index: 2;
}

/* Mobil görünüm */
@media (max-width: 576px) {
  .date-range-container {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .date-connector {
    display: none;
  }

  .date-card {
    width: 100%;
  }
}

/* Meta kartları genel düzeni */
.meta-cards-container {
  display: flex;
  align-items: center;
  margin: 8px 0;
  flex-wrap: wrap;
  gap: 0;
  width: 100%;
}

/* Date card stilleri - güncellendi */
.date-card {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  min-width: 180px;
  flex: 1;
  position: relative;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.date-card:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* Kartlar için renkli kenarlar */
.date-card.start-date {
  border-left: 3px solid #4f46e5;
}

.date-card.end-date {
  border-left: 3px solid #ea580c;
}

.date-card.budget-card {
  border-left: 3px solid #047857;
}

.date-card.time-card {
  border-left: 3px solid #b91c1c;
}

/* İkon stili düzenlemeleri */
.budget-icon-wrapper {
  background-color: rgba(4, 120, 87, 0.1);
}

.time-icon-wrapper {
  background-color: rgba(185, 28, 28, 0.1);
}

.date-icon.budget {
  color: #047857;
}

.date-icon.time {
  color: #b91c1c;
}

/* Sayaç container'ı */
.countdown-container {
  width: 100%;
}

.countdown-container .remaining-time {
  margin: 0;
  box-shadow: none;
  border: none;
  background: transparent;
}

.countdown-container .remaining-time-header {
  display: none;
}

.countdown-container .remaining-time-body {
  padding: 0;
}

.countdown-container .remaining-time-counter {
  justify-content: flex-start;
}

.countdown-container .time-unit {
  min-width: 32px;
  padding: 2px 4px;
  margin: 0 2px 0 0;
}

.countdown-container .time-value {
  font-size: 0.9rem;
  margin-bottom: 0;
}

.countdown-container .time-label {
  font-size: 0.5rem;
}

.countdown-container .remaining-time-text {
  display: none;
}

.invisible-connector {
  visibility: hidden;
}

/* Responsive düzenlemeler */
@media (max-width: 768px) {
  .meta-cards-container {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .date-card {
    width: 100%;
    margin-bottom: 0;
  }

  .date-connector {
    display: none;
  }

  .countdown-container .remaining-time-counter {
    justify-content: flex-start;
  }
}

/* Status Badge Override Bootstrap */
.status-badge.bg-success {
  background-color: rgba(40, 167, 69, 0.1) !important;
  color: #28a745 !important;
  border-color: rgba(40, 167, 69, 0.2) !important;
}

.status-badge.bg-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
  color: #856404 !important;
  border-color: rgba(255, 193, 7, 0.2) !important;
}

.status-badge.bg-secondary {
  background-color: rgba(108, 117, 125, 0.1) !important;
  color: #6c757d !important;
  border-color: rgba(108, 117, 125, 0.2) !important;
}

.status-badge.bg-danger {
  background-color: var(--primary-transparent) !important;
  color: var(--primary-color) !important;
  border-color: var(--primary-shadow) !important;
}

/* ===== ÇOKLU TEKLİF YÖNETİMİ STİLLERİ ===== */

/* Ana konteyner */
.multiple-bid-manager {
  background: var(--gray-50);
  min-height: 400px;
  padding: 0;
}

/* Teklif satırları */
.bid-row {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--white);
  border-left: 4px solid var(--gray-300);
}

.bid-row:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.bid-row.editing {
  border-left-color: var(--primary-color);
  box-shadow: 0 4px 20px var(--primary-shadow);
}

.bid-row.draft {
  border-left-color: var(--warning);
}

.bid-row.submitted {
  border-left-color: var(--success);
}

/* Teklif başlığı */
.bid-row .card-header {
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  padding: 15px 20px;
  font-weight: 500;
}

.bid-row.editing .card-header {
  background-color: var(--primary-transparent);
  border-bottom-color: var(--primary-color);
}

.bid-index {
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 20px;
  font-weight: 600;
}

/* Teklif içeriği */
.bid-row .card-body {
  padding: 20px;
}

/* Düzenleme formu */
.bid-edit-form {
  background: var(--gray-50);
  border-radius: 8px;
  padding: 20px;
  margin: -5px;
}

.bid-edit-form .form-label {
  color: var(--gray-700);
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 8px;
}

.bid-edit-form .form-control {
  border: 1px solid var(--gray-300);
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.bid-edit-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem var(--primary-transparent);
}

.bid-edit-form .input-group-text {
  background-color: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--gray-600);
  font-weight: 500;
}

/* Görüntüleme modu */
.bid-view {
  padding: 10px 0;
}

.bid-summary h5 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
}

.vehicle-prices-summary {
  background: var(--gray-50);
  border-radius: 6px;
  padding: 12px;
  border-left: 3px solid var(--primary-color);
}

.bid-details {
  background: var(--gray-50);
  border-radius: 6px;
  padding: 15px;
  margin-top: 15px;
}

.bid-details p {
  color: var(--gray-700);
  line-height: 1.5;
  margin-bottom: 8px;
}

/* Butonlar */
.bid-row .btn {
  font-size: 13px;
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.bid-row .btn-success {
  background-color: var(--success);
  border-color: var(--success);
}

.bid-row .btn-success:hover {
  background-color: var(--success);
  border-color: var(--success);
  transform: translateY(-1px);
}

.bid-row .btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.bid-row .btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.bid-row .btn-outline-danger {
  color: var(--danger);
  border-color: var(--danger);
}

.bid-row .btn-outline-danger:hover {
  background-color: var(--danger);
  border-color: var(--danger);
  color: var(--white);
}

.bid-row .btn-outline-secondary {
  color: var(--gray-600);
  border-color: var(--gray-400);
}

.bid-row .btn-outline-secondary:hover {
  background-color: var(--gray-600);
  border-color: var(--gray-600);
  color: var(--white);
}

/* Badge'ler */
.bid-row .badge {
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.badge.bg-warning {
  background-color: var(--warning) !important;
  color: var(--white);
}

.badge.bg-success {
  background-color: var(--success) !important;
  color: var(--white);
}

.badge.bg-secondary {
  background-color: var(--gray-500) !important;
  color: var(--white);
}

/* Boş durum */
.bids-container .card.text-center {
  border: 2px dashed var(--gray-300);
  background: var(--white);
  border-radius: 12px;
}

.bids-container .card.text-center:hover {
  border-color: var(--primary-color);
  background: var(--primary-transparent);
}

/* Gönderim özeti */
.multiple-bid-manager .card:last-child {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
}

.multiple-bid-manager .card:last-child .card-body {
  padding: 25px;
}

/* Ana konteyner kartı */
.multiple-bid-manager > .card:first-child {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 25px;
  background: var(--white);
}

.multiple-bid-manager > .card:first-child .card-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
  border-bottom: 1px solid var(--gray-200);
  padding: 20px 25px;
  border-radius: 12px 12px 0 0;
}

.multiple-bid-manager > .card:first-child h5 {
  color: var(--gray-900);
  font-weight: 600;
  margin-bottom: 5px;
}

.multiple-bid-manager > .card:first-child small {
  color: var(--gray-600);
  font-size: 13px;
}

/* Responsive tasarım */
@media (max-width: 768px) {
  .bid-row .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }

  .bid-row .card-header .d-flex:last-child {
    width: 100%;
    justify-content: space-between;
  }

  .bid-edit-form {
    padding: 15px;
    margin: -10px;
  }

  .bid-summary h5 {
    font-size: 20px;
  }

  .vehicle-prices-summary,
  .bid-details {
    padding: 12px;
  }

  .multiple-bid-manager > .card:first-child .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
  }

  .multiple-bid-manager > .card:first-child .d-flex:last-child {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 10px;
  }
}

@media (max-width: 576px) {
  .bid-row {
    margin-bottom: 15px;
  }

  .bid-row .card-header,
  .bid-row .card-body {
    padding: 12px;
  }

  .bid-edit-form {
    padding: 12px;
    margin: -8px;
  }

  .bid-row .btn {
    font-size: 12px;
    padding: 5px 10px;
  }

  .bid-summary h5 {
    font-size: 18px;
  }

  .multiple-bid-manager .card:last-child .card-body {
    padding: 20px;
  }

  .multiple-bid-manager > .card:first-child .card-header {
    padding: 15px;
  }
}

/* TenderDetailsPage tarzı modern kartlar */
.multiple-bid-manager .card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 5px 20px var(--primary-shadow);
  overflow: hidden;
}

.multiple-bid-manager .card .card-header {
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  font-weight: 600;
  color: var(--gray-900);
  font-family: "Poppins", sans-serif;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Animasyonlar */
.bid-row {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bid-row.editing {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* Loading durumu */
.multiple-bid-manager .spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Alert stilleri */
.multiple-bid-manager .alert {
  border: none;
  border-radius: 8px;
  font-size: 14px;
  padding: 12px 16px;
}

.multiple-bid-manager .alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.multiple-bid-manager .alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  color: #155724;
  border-left: 4px solid #28a745;
}

/* ===== RESPONSIVE TASARIM İYİLEŞTİRMELERİ ===== */

/* Tablet ve küçük ekranlar */
@media (max-width: 991px) {
  .tender-bid-detail {
    padding: 30px 0;
  }

  .tender-bid-detail .compact-container {
    padding: 20px;
    margin-bottom: 20px;
  }

  .multiple-bid-manager > .card:first-child .card-header {
    padding: 15px 20px;
  }

  .bid-row .card-header {
    padding: 12px 15px;
  }

  .bid-row .card-body {
    padding: 15px;
  }
}

/* Mobil ekranlar */
@media (max-width: 767px) {
  .tender-bid-detail {
    padding: 20px 0;
  }

  .tender-bid-detail .compact-container {
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
  }

  .multiple-bid-manager > .card:first-child .card-header {
    padding: 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .multiple-bid-manager > .card:first-child .d-flex:last-child {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 8px;
  }

  .bid-row .card-header {
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .bid-row .card-header .d-flex:last-child {
    width: 100%;
    justify-content: space-between;
  }

  .bid-row .card-body {
    padding: 12px;
  }

  .bid-edit-form {
    padding: 12px;
    margin: -8px;
  }

  .bid-summary h5 {
    font-size: 18px;
  }

  .vehicle-prices-summary,
  .bid-details {
    padding: 10px;
  }
}

/* Küçük mobil ekranlar */
@media (max-width: 576px) {
  .tender-bid-detail {
    padding: 15px 0;
  }

  .tender-bid-detail .compact-container {
    padding: 12px;
    margin-bottom: 12px;
  }

  .multiple-bid-manager > .card:first-child h5 {
    font-size: 16px;
  }

  .multiple-bid-manager > .card:first-child small {
    font-size: 12px;
  }

  .bid-row {
    margin-bottom: 12px;
  }

  .bid-row .btn {
    font-size: 11px;
    padding: 4px 8px;
  }

  .bid-index {
    font-size: 10px;
    padding: 4px 8px;
  }

  .bid-summary h5 {
    font-size: 16px;
  }

  .bid-edit-form .form-label {
    font-size: 13px;
  }

  .bid-edit-form .form-control {
    font-size: 13px;
    padding: 8px 10px;
  }

  .multiple-bid-manager .card:last-child .card-body {
    padding: 15px;
  }

  .multiple-bid-manager .alert {
    font-size: 13px;
    padding: 10px 12px;
  }
}

/* Çok küçük ekranlar */
@media (max-width: 480px) {
  .tender-bid-detail .compact-container {
    padding: 10px;
  }

  .bid-row .card-header,
  .bid-row .card-body {
    padding: 10px;
  }

  .bid-edit-form {
    padding: 10px;
    margin: -6px;
  }

  .multiple-bid-manager > .card:first-child .card-header {
    padding: 12px;
  }

  .bid-row .card-header .d-flex:first-child {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .bid-row .card-header .d-flex:last-child {
    flex-direction: column;
    gap: 8px;
    width: 100%;
  }

  .bid-row .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Landscape mobil orientasyon */
@media (max-width: 767px) and (orientation: landscape) {
  .tender-bid-detail {
    padding: 15px 0;
  }

  .bid-row .card-header {
    flex-direction: row;
    align-items: center;
    gap: 15px;
  }

  .bid-row .card-header .d-flex:last-child {
    width: auto;
    flex-direction: row;
  }
}

/* Print stilleri */
@media print {
  .tender-bid-detail {
    background: white !important;
    padding: 0 !important;
  }

  .bid-row .btn,
  .multiple-bid-manager > .card:first-child .card-header .d-flex:last-child {
    display: none !important;
  }

  .bid-row {
    break-inside: avoid;
    margin-bottom: 20px;
  }

  .multiple-bid-manager .alert {
    display: none !important;
  }
}