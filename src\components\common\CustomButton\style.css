@import url('../../styles/colors.css');

/* ===================== CUSTOM BUTTON ======================*/
.custom-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  text-align: center;
  vertical-align: middle;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 15px;
  line-height: 1.5;
  position: relative;
  overflow: hidden;
}

.custom-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.25);
}

/* Varyasyonlar */
.btn-primary {
  background-color: var(--primary-color);
  color: #fff;
}

.btn-primary:hover, 
.btn-primary:active {
  background-color: var(--primary-dark);
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px var(--primary-shadow);
}

.btn-secondary {
  background-color: #333;
  color: #fff;
}

.btn-secondary:hover, 
.btn-secondary:active {
  background-color: #222;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover, 
.btn-outline:active {
  background-color: var(--primary-color);
  color: #fff;
  transform: translateY(-2px);
}

.btn-white {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2px solid #fff;
}

.btn-white:hover, 
.btn-white:active {
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  transform: translateY(-2px);
}

.btn-white.active {
  background-color: #fff;
  color: var(--primary-color);
}

.btn-white.active:hover {
  background-color: #f8f9fa;
  color: var(--primary-color);
}

.btn-success {
  background-color: #28a745;
  color: #fff;
}

.btn-success:hover, 
.btn-success:active {
  background-color: #218838;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
}

.btn-danger {
  background-color: #dc3545;
  color: #fff;
}

.btn-danger:hover, 
.btn-danger:active {
  background-color: #c82333;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
}

.btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.btn-warning:hover, 
.btn-warning:active {
  background-color: #e0a800;
  color: #212529;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(255, 193, 7, 0.3);
}

/* Boyutlar */
.btn-sm {
  padding: 8px 16px;
  font-size: 13px;
}

.btn-md {
  padding: 12px 24px;
  font-size: 15px;
}

.btn-lg {
  padding: 14px 30px;
  font-size: 17px;
}

/* Genişlik */
.btn-full-width {
  width: 100%;
  display: block;
}

/* İkon özellikleri */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-left {
  margin-right: 8px;
}

.icon-right {
  margin-left: 8px;
}

/* Devre dışı buton */
.custom-btn:disabled {
  opacity: 0.65;
  pointer-events: none;
  background-color: #f8f9fa !important;
  color: #6c757d !important;
  border: 1px solid #dee2e6 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Responsive */
@media (max-width: 767px) {
  .custom-btn {
    padding: 10px 20px;
    font-size: 14px;
  }

  .btn-sm {
    padding: 7px 14px;
    font-size: 12px;
  }

  .btn-lg {
    padding: 12px 24px;
    font-size: 16px;
  }
} 