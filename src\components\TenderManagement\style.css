@import url('../../styles/colors.css');

/* Tender Management bileşeni stilleri */
.tender-nav {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 20px;
}

.tender-nav .nav-link {
  border-radius: 6px;
  padding: 0.5rem 1rem;
  color: #495057;
  margin-right: 0.25rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.tender-nav .nav-link:hover {
  background-color: var(--brand-color-light);
  color: var(--brand-color);
}

.tender-nav .nav-link.active {
  background-color: var(--primary-transparent);
  border-color: var(--primary-color);
}

.alert-info {
  background-color: var(--primary-transparent);
  border-color: rgba(var(--primary-color-rgb), 0.2);
  color: #333333;
  border-radius: 6px;
  padding: 12px 15px;
  margin-bottom: 20px;
}

/* <PERSON><PERSON><PERSON> stilleri */
.tender-table {
  width: 100%;
  margin-bottom: 0;
  border-collapse: separate;
  border-spacing: 0;
}

.tender-table thead th {
  font-weight: 600;
  padding: 1rem 0.75rem;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tender-table tbody td {
  padding: 1rem 0.75rem;
  border-top: none;
  border-bottom: 1px solid #f1f1f1;
  vertical-align: middle;
}

.tender-table tbody tr:last-child td {
  border-bottom: none;
}

.tender-table tbody tr {
  transition: all 0.2s ease;
}

.tender-table tbody tr:hover {
  background-color: var(--brand-color-lighter);
}

/* Badge stilleri */
.status-badge {
  background-color: var(--primary-transparent);
  border-color: rgba(var(--primary-color-rgb), 0.2);
  color: #333333;
  border-radius: 6px;
  padding: 12px 15px;
  margin-bottom: 20px;
}

/* Marka rengi badge - tamamen ortadan kaldırıldı */
.brand-badge {
  display: contents; /* Sadece içeriği gösterir, elementin kendisi görünmez */
  font-weight: 700;
  font-size: 14px;
  color: #333;
}

/* Tüm segmentler için aynı stil */
.brand-badge.economy,
.brand-badge.standard,
.brand-badge.premium,
.brand-badge.luxury {
  display: contents;
}

/* Kalan süre badge'leri */
.badge.bg-danger {
  background-color: var(--primary-color) !important;
  color: white;
  font-weight: 600;
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  box-shadow: 0 2px 4px var(--primary-shadow);
  border: 1px solid var(--primary-transparent);
  transition: all 0.2s ease;
}

.badge.bg-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px var(--primary-shadow);
}

.badge.bg-warning {
  background-color: var(--time-warning) !important;
  color: white;
  font-weight: 600;
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(255, 152, 0, 0.25);
  border: 1px solid rgba(255, 152, 0, 0.15);
  transition: all 0.2s ease;
}

.badge.bg-warning:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(255, 152, 0, 0.3);
}

.badge.bg-info {
  background-color: var(--time-info) !important;
  color: white;
  font-weight: 600;
  padding: 0.5rem 0.8rem;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(76, 175, 80, 0.25);
  border: 1px solid rgba(76, 175, 80, 0.15);
  transition: all 0.2s ease;
}

.badge.bg-info:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(76, 175, 80, 0.3);
}

/* Buton stilleri */
.tender-table .btn {
  transition: all 0.2s ease;
  box-shadow: none;
}

.tender-table .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Marka butonları */
.btn-brand {
  background-color: var(--brand-color);
  border-color: var(--brand-color);
  color: white;
}

.btn-brand:hover, .btn-brand:focus, .btn-brand:active {
  background-color: var(--brand-color-hover) !important;
  border-color: var(--brand-color-hover) !important;
  color: white !important;
  box-shadow: 0 3px 8px var(--brand-color-shadow) !important;
}

.btn-outline-brand {
  color: var(--brand-color);
  border-color: var(--brand-color);
  background-color: transparent;
}

.btn-outline-brand:hover, .btn-outline-brand:focus, .btn-outline-brand:active {
  color: white !important;
  background-color: var(--brand-color) !important;
  border-color: var(--brand-color) !important;
  box-shadow: 0 3px 8px var(--brand-color-shadow) !important;
}

/* Eski Bootstrap stilleri (alternatif olarak bırakıldı) */
.tender-table .btn-success {
  background-color: #333333;
  border-color: #333333;
}

.tender-table .btn-success:hover {
  background-color: #222222;
  border-color: #222222;
}

.tender-table .btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
  transition: all 0.3s ease;
}

.tender-table .btn-outline-primary:hover {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.tender-table .btn-primary {
  color: #fff;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.tender-table .btn-primary:hover {
  color: #fff;
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.tender-table .table-hover tbody tr:hover {
  background-color: var(--primary-transparent);
}

/* İçerik yok mesajları */
.tender-table .text-muted {
  color: #6c757d !important;
  font-size: 0.9rem;
}

/* Responsive tasarım için ek stiller */
@media (max-width: 992px) {
  .tender-table {
    min-width: 900px;
  }
  
  .tender-nav {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding: 0.5rem;
  }
  
  .tender-nav .nav-link {
    white-space: nowrap;
  }
}

/* Ana renk tanımları - Global değişkenler kullanılarak güncellendi */
:root {
  /* Zaten colors.css'den import edildiği için kaldırıldı */
  /* --brand-color: var(--primary-color); */
  /* --brand-color-hover: var(--primary-dark); */
  /* --brand-color-light: var(--primary-transparent); */
  /* --brand-color-lighter: rgba(255, 98, 0, 0.05); */
  /* --brand-color-shadow: var(--primary-shadow); */
  
  /* Kalan süre için renk tanımları */
  --time-danger: var(--primary-color);
  --time-warning: #ff9800;
  --time-info: #4caf50;
  
  /* Kalan süre için opaklık değerleri */
  --time-danger-light: var(--primary-transparent);
  --time-warning-light: rgba(255, 152, 0, 0.1);
  --time-info-light: rgba(76, 175, 80, 0.1);
}

/* Tablo stilleri RentalCompanyDashboard.css dosyasından gelecek */

.tender-management-action .btn-outline-danger {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.tender-management-action .btn-danger {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.active-tender-card {
  background-color: var(--primary-transparent);
  border-color: var(--primary-color);
} 