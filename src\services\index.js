import authService from './authService';
import vehiclesService from './vehiclesService';
import bookingsService from './bookingsService';
import tendersService from './tendersService';
import contractService from './companyServices/contractService';
import userService from './userService';
import tenderService from './tenderService';
import { useMockAPI } from './api';
import dashboardService from './dashboardService';

// Tüm servisleri tek bir noktadan export ediyoruz
export {
  authService,
  vehiclesService,
  bookingsService,
  tendersService,
  contractService,
  userService,
  useMockAPI,
  tenderService,
  dashboardService
};

// Varsayılan olarak tüm servisleri içeren bir obje export ediyoruz
export default {
  auth: authService,
  vehicles: vehiclesService,
  bookings: bookingsService,
  tenders: tendersService,
  contracts: contractService,
  user: userService,
  tender: tenderService,
  useMockAPI,
  dashboard: dashboardService
};