.form-field {
  position: relative;
  margin-bottom: 15px;
  width: 100%;
}

.form-field input {
  width: 100%;
  height: 45px;
  border: 1px solid #eaeaea;
  padding: 0 15px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.form-field input:focus {
  border-color: #00d664;
  outline: none;
  box-shadow: 0 0 0 3px rgba(0, 214, 100, 0.1);
}

.form-field.has-icon input {
  padding-right: 45px;
}

.form-field .field-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 16px;
}

.form-field.has-error input {
  border-color: #dc3545;
}

.form-field.has-error .field-icon {
  color: #dc3545;
}

.form-field .error-message {
  font-size: 12px;
  color: #dc3545;
  margin-top: 5px;
  font-weight: 400;
} 