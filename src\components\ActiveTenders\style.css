@import url('../../styles/colors.css');

.active-tenders-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title h3 {
  font-size: 1.3rem;
  margin: 0;
  font-weight: 600;
  color: var(--secondary-color);
}

.section-actions {
  display: flex;
  gap: 10px;
}

.sort-button, .refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 10px;
  border-radius: 6px;
  background-color: var(--white);
}

.refresh-button {
  width: 36px;
  height: 36px;
  padding: 0;
}

/* Main tenders card */
.tenders-card {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 0;
  margin-bottom: 0;
  overflow: hidden;
}

.tenders-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--medium-gray);
}

.tenders-card-header h3 {
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
  color: var(--secondary-color);
}

.tenders-card-body {
  padding: 0;
}

.tenders-card-body.scrollable {
  max-height: 600px;
  overflow-y: auto;
}

/* Individual tender items */
.tender-item {
  padding: 20px;
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

.tender-item:last-child {
  border-bottom: none;
}

/* Tender Title Row */
.tender-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
}

.tender-title {
  font-size: 1.05rem;
  margin: 0;
  font-weight: 600;
  color: var(--gray-700);
}

/* Tender No Row */
.tender-no-row {
  font-size: 0.85rem;
  color: var(--gray-600);
  margin-bottom: 16px;
}

/* Tender Info Row */
.tender-info-row {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 12px;
}

.tender-info-section {
  flex: 0 0 auto;
  min-width: 120px;
  margin-right: 60px;
}

.tender-info-section:last-child {
  margin-right: 0;
}

.tender-info-label {
  font-size: 0.85rem;
  color: var(--gray-600);
  margin-bottom: 4px;
}

.tender-info-value {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--gray-800);
}

.price-value {
  color: var(--primary-color);
}

/* Kalan süre değeri kırmızı renkli olsun */
.tender-info-section:nth-child(2) .tender-info-value {
  color: var(--danger);
}

/* Details Link */
.tender-details-link {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: var(--primary-color);
  font-weight: 500;
  font-size: 0.85rem;
  cursor: pointer;
  right: unset;
  bottom: unset;
  margin-top: 8px;
  width: 100%;
  transition: color 0.2s;
}

.tender-details-link.flex-right {
  justify-content: flex-end;
}

.tender-details-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.tender-details-link svg {
  margin-left: 6px;
  font-size: 0.9rem;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: var(--soft-primary);
  color: var(--primary-color);
}

.status-new-offers {
  background-color: var(--soft-success);
  color: var(--success);
}

.status-last-24-hours {
  background-color: var(--soft-warning);
  color: var(--warning);
}

/* View All Button */
.active-tenders-section .view-all-button {
  display: block;
  width: 100%;
  text-align: center;
  padding: 15px;
  color: var(--gray-800);
  font-weight: 500;
  font-size: 0.95rem;
  background-color: var(--gray-200);
  border: none;
  cursor: pointer;
  border-radius: 8px;
  margin-top: 10px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
}

.active-tenders-section .view-all-button:hover {
  background-color: var(--gray-300);
}

.loading-indicator, 
.error-message,
.no-items-message {
  padding: 30px;
  text-align: center;
  color: var(--gray-500);
}

.error-message {
  color: var(--danger);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .tender-info-row {
    flex-wrap: wrap;
  }
  
  .tender-info-section {
    width: 50%;
    margin-right: 0;
    margin-bottom: 15px;
    padding-right: 20px;
  }
  
  .tender-details-link {
    position: relative;
    right: auto;
    bottom: auto;
    margin-top: 15px;
    justify-content: flex-end;
  }
}

@media (max-width: 576px) {
  .tender-info-section {
    width: 100%;
    padding-right: 0;
  }
  
  .tender-title-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .status-badge {
    margin-top: 8px;
    margin-left: 0;
  }
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 8px;
}
.pagination-wrapper > span {
  min-width: 90px;
  text-align: left;
  font-size: 15px;
  display: inline-block;
}
.pagination-wrapper .page-size-filter {
  min-width: 130px;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.pagination-wrapper nav {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  padding: 0 16px;
}
@media (max-width: 600px) {
  .pagination-wrapper {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  .pagination-wrapper nav {
    padding: 0;
  }
  .pagination-wrapper .page-size-filter,
  .pagination-wrapper > span {
    text-align: left;
    min-width: 0;
  }
}

/* 2x2 Grid düzeni */
.tender-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px 32px;
  width: 100%;
}

.tender-info-grid .tender-info-section {
  margin-right: 0;
}

@media (max-width: 768px) {
  .tender-info-grid {
    grid-template-columns: 1fr;
    gap: 10px 0;
  }
}

/* Kompakt bilgi satırı */
.tender-info-row.compact {
  display: flex;
  align-items: center;
  gap: 0;
  padding: 0;
  margin-bottom: 8px;
  flex-wrap: wrap;
}
.tender-info-row.compact .tender-info-section {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  font-size: 0.92rem;
  min-width: unset;
  margin: 0;
}
.tender-info-row.compact .tender-info-label {
  font-size: 0.82rem;
  color: var(--gray-500);
  font-weight: 400;
  margin: 0 2px 0 0;
}
.tender-info-row.compact .tender-info-value {
  font-size: 0.98rem;
  font-weight: 600;
  color: var(--gray-800);
  margin-left: 2px;
}
.tender-info-row.compact .tender-info-icon {
  font-size: 1rem;
  color: var(--primary-color);
  margin-right: 3px;
}
.tender-info-row.compact .tender-info-separator {
  color: var(--gray-300);
  font-size: 1.1rem;
  margin: 0 2px;
  user-select: none;
}
@media (max-width: 700px) {
  .tender-info-row.compact {
    flex-wrap: wrap;
    row-gap: 4px;
    column-gap: 0;
  }
  .tender-info-row.compact .tender-info-section {
    min-width: 48%;
    padding: 2px 0;
  }
  .tender-info-row.compact .tender-info-separator {
    display: none;
  }
  .tender-details-link {
    justify-content: center;
    width: 100%;
    margin-top: 10px;
  }
} 