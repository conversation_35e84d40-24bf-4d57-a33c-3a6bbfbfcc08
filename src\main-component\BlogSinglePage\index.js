import React, { Fragment } from "react";
import { useTranslation } from "react-i18next";

import PageTitle from "../../components/PageTitle";
import BlogDetails from "../../components/BlogDetails";

const BlogSinglePage = () => {
  const { t } = useTranslation();

  return (
    <Fragment>
      <PageTitle
        pageTitle={t("header-navigation.blog_single")}
        pagesub={t("header-navigation.blog_single")}
      />
      <BlogDetails />
    </Fragment>
  );
};
export default BlogSinglePage;
