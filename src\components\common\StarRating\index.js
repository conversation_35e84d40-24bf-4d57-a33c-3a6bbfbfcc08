import React from 'react';
import PropTypes from 'prop-types';
import { FaStar, FaStarHalfAlt, FaRegStar } from 'react-icons/fa';
import './style.css';

/**
 * StarRating - Projedeki tüm derecelendirme yıldızları için kullanılacak ortak bileşen
 * 
 * @param {Object} props
 * @param {number} props.rating - Derecelendirme puanı (0-5 arası)
 * @param {number} props.maxRating - Maksimum yıldız sayısı (varsayılan: 5)
 * @param {string} props.size - Yıldız boyutu: "sm", "md", "lg"
 * @param {boolean} props.showCount - Derecelendirme sayısını gösterme
 * @param {number} props.count - Derecelendirme sayısı
 * @param {string} props.className - Ek CSS sınıfları
 * @returns {React.ReactElement}
 */
const StarRating = ({
  rating = 0,
  maxRating = 5,
  size = 'md',
  showCount = false,
  count = 0,
  className = '',
}) => {
  // Yıldızları oluşturmak için array
  const stars = [];
  
  // Tam dolu yıldızların sayısı
  const fullStars = Math.floor(rating);
  
  // Yarım yıldız var mı?
  const hasHalfStar = rating % 1 !== 0;
  
  // Boş yıldızların sayısı
  const emptyStars = maxRating - fullStars - (hasHalfStar ? 1 : 0);
  
  // CSS sınıflarını oluştur
  const containerClasses = [
    'star-rating',
    `size-${size}`,
    className
  ].filter(Boolean).join(' ');
  
  // Tam yıldızları ekle
  for (let i = 0; i < fullStars; i++) {
    stars.push(<FaStar key={`full-${i}`} className="full-star" />);
  }
  
  // Yarım yıldız ekle (eğer varsa)
  if (hasHalfStar) {
    stars.push(<FaStarHalfAlt key="half" className="half-star" />);
  }
  
  // Boş yıldızları ekle
  for (let i = 0; i < emptyStars; i++) {
    stars.push(<FaRegStar key={`empty-${i}`} className="empty-star" />);
  }
  
  return (
    <div className={containerClasses}>
      <ul className="stars">
        {stars.map((star, index) => (
          <li key={index}>{star}</li>
        ))}
      </ul>
      
      {showCount && count > 0 && (
        <span className="rating-count">({count})</span>
      )}
    </div>
  );
};

StarRating.propTypes = {
  rating: PropTypes.number,
  maxRating: PropTypes.number,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  showCount: PropTypes.bool,
  count: PropTypes.number,
  className: PropTypes.string,
};

export default StarRating; 