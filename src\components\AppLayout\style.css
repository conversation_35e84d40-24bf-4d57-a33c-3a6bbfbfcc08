/* AppLayout i<PERSON>in temel stiller */
html, 
body, 
#root {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* Fragment doğrudan stil alamadığından içindeki elementlerin düzenini sağlıyoruz */
#root > div {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* Viewport yüksekliğinin en az %100'ü kadar */
}

/* Ana içerik alanı */
.app-main {
  flex: 1 0 auto; /* Flex-grow ile mevcut boşluğu doldurmasını sağlıyoruz */
  display: flex;
  flex-direction: column;
  min-height: 400px; /* Minimum yükseklik belirleyelim */
}

/* Footer'ın her zaman altta kalması için */
footer.gauto-footer-area {
  flex-shrink: 0; /* Footer'ın boyutunun sabit kalmasını sağlar */
} 