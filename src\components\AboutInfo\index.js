import React from "react";
import { useTranslation } from "react-i18next";
import { Container, Row, Col } from "react-bootstrap";
import { FaPhoneAlt, FaCheck } from "react-icons/fa";

import img1 from "../../img/new-images/AboutInfo/about-ihaleden-kirala-kurumsal-filo-kiralama-550x420.png";

import "./style.css";

const AboutInfo = () => {
  const { t } = useTranslation();

  return (
    <section className="about-info-area section_70">
      <Container>
        <Row>
          <Col lg={6}>
            <div className="about-info-left">
              <h4>{t("about_page.subtitle")}</h4>
              <h3>{t("about_page.title")}</h3>
              <p>{t("about_text")}</p>
              <ul className="about-list">
                <li><FaCheck className="fa-check" /> {t("trusted_name")}</li>
                <li><FaCheck className="fa-check" /> {t("deal_brands")}</li>
                <li><FaCheck className="fa-check" /> {t("larger_stocks")}</li>
                <li><FaCheck className="fa-check" /> {t("worldwide_location")}</li>
              </ul>
              <div className="about-page-call">
                <div className="page-call-icon">
                  <FaPhoneAlt />
                </div>
                <div className="call-info">
                  <p>{t("need_any_help")}</p>
                  <h4>
                    <a href="tel:12435424">(*************</a>
                  </h4>
                </div>
              </div>
            </div>
          </Col>
          <Col lg={6}>
            <div className="about-info-right">
              <img src={img1} alt="about page" />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default AboutInfo; 