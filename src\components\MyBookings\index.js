import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Container, Row, Col, Card, Table, Badge, Button } from "react-bootstrap";
import { FaCar, FaCalendarAlt, FaMapMarkerAlt, FaMoneyBillWave, FaEye, FaTimesCircle } from "react-icons/fa";
import "./style.css";

const MyBookings = () => {
  // Redux'tan kullanıcı verilerini al
  const { user } = useSelector(state => state.auth);
  const { t, i18n } = useTranslation();
  
  // Örnek rezervasyon verileri (gerçek uygulamada API'den alınacak)
  const [bookings, setBookings] = useState([
    {
      id: 1,
      vehicleName: "Toyota Alphard",
      startDate: "2025-04-10",
      endDate: "2025-04-15",
      fromAddress: "İstanbul",
      toAddress: "Ankara",
      amount: 250,
      status: "active"
    },
    {
      id: 2,
      vehicleName: "Audi Q3",
      startDate: "2025-05-20",
      endDate: "2025-05-22",
      fromAddress: "İzmir",
      toAddress: "Bodrum",
      amount: 180,
      status: "completed"
    },
    {
      id: 3,
      vehicleName: "Nissan 370Z",
      startDate: "2025-06-05",
      endDate: "2025-06-10",
      fromAddress: "Antalya",
      toAddress: "Alanya",
      amount: 320,
      status: "cancelled"
    }
  ]);
  
  const getBadgeVariant = (status) => {
    switch(status) {
      case 'active':
        return 'success';
      case 'completed':
        return 'primary';
      case 'cancelled':
        return 'danger';
      default:
        return 'secondary';
    }
  };
  
  const getStatusText = (status) => {
    switch(status) {
      case 'active':
        return t("my_bookings.status_active");
      case 'completed':
        return t("my_bookings.status_completed");
      case 'cancelled':
        return t("my_bookings.status_cancelled");
      default:
        return status;
    }
  };
  
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    const language = i18n.language || 'tr';
    return new Date(dateString).toLocaleDateString(language === 'tr' ? 'tr-TR' : 'en-US', options);
  };
  
  const cancelBooking = (id) => {
    // Gerçek uygulamada API çağrısı yapılacak
    setBookings(bookings.map(booking => 
      booking.id === id ? {...booking, status: 'cancelled'} : booking
    ));
  };
  
  return (
    <section className="my-bookings-area section_70">
      <Container>
        <Row>
          <Col lg={12}>
            <div className="site-heading">
              <h4>{t("my_bookings.title")}</h4>
              <h2>{t("my_bookings.subtitle")}</h2>
            </div>
          </Col>
        </Row>
        
        <Row>
          <Col lg={12}>
            <Card className="booking-card">
              <Card.Body>
                {bookings.length === 0 ? (
                  <div className="no-bookings">
                    <h3>{t("my_bookings.empty_title")}</h3>
                    <p>{t("my_bookings.empty_message")}</p>
                    <Button variant="primary" href="/car-listing">{t("my_bookings.view_cars")}</Button>
                  </div>
                ) : (
                  <Table responsive className="booking-table">
                    <thead>
                      <tr>
                        <th>{t("my_bookings.vehicle")}</th>
                        <th>{t("my_bookings.date_range")}</th>
                        <th>{t("my_bookings.route")}</th>
                        <th>{t("my_bookings.amount")}</th>
                        <th>{t("my_bookings.status")}</th>
                        <th>{t("my_bookings.actions")}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {bookings.map(booking => (
                        <tr key={booking.id} className={`booking-row ${booking.status}`}>
                          <td>
                            <div className="car-name">
                              <FaCar />
                              <span>{booking.vehicleName}</span>
                            </div>
                          </td>
                          <td>
                            <div className="booking-date">
                              <FaCalendarAlt />
                              <span>{formatDate(booking.startDate)} - {formatDate(booking.endDate)}</span>
                            </div>
                          </td>
                          <td>
                            <div className="booking-route">
                              <FaMapMarkerAlt />
                              <span>{booking.fromAddress} - {booking.toAddress}</span>
                            </div>
                          </td>
                          <td>
                            <div className="booking-amount">
                              <FaMoneyBillWave />
                              <span>{booking.amount} ₺</span>
                            </div>
                          </td>
                          <td>
                            <Badge bg={getBadgeVariant(booking.status)}>
                              {getStatusText(booking.status)}
                            </Badge>
                          </td>
                          <td>
                            <div className="booking-actions">
                              <Button variant="outline-primary" size="sm" title={t("my_bookings.view_details")}>
                                <FaEye />
                              </Button>
                              
                              {booking.status === 'active' && (
                                <Button 
                                  variant="outline-danger" 
                                  size="sm" 
                                  title={t("my_bookings.cancel_booking")}
                                  onClick={() => cancelBooking(booking.id)}
                                >
                                  <FaTimesCircle />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                )}
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default MyBookings; 