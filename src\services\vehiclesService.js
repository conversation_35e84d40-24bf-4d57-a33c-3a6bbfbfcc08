import api, { useMockAPI } from './api';

// ----- Gerçek API Çağrıları ----- //

// Tüm araçları getir (filtreleme dahil)
const getVehicles = (filters = {}) => api.get('/vehicles', { params: filters });

// Belirli bir aracın detaylarını getir
const getVehicleById = (id) => api.get(`/vehicles/${id}`);

// <PERSON><PERSON> kategorilerini getir
const getVehicleCategories = () => api.get('/vehicles/categories');

// Araç markalarını getir
const getVehicleBrands = () => api.get('/vehicles/brands');

// Araç modellerini markaya göre getir
const getVehicleModelsByBrand = (brand) => api.get(`/vehicles/models?brand=${brand}`);

// ----- Mock API Çağrıları ----- //

// Mock araç verileri
const mockVehicles = [
  {
    id: 1,
    brand: 'Toyota',
    model: 'Alphard',
    year: 2022,
    price: 50,
    priceUnit: 'TL',
    image: '/assets/img/offer-toyota.png',
    features: ['Otomatik', '20kmpl', 'GPS Navigasyon', 'Klima'],
    type: 'SUV',
    isAvailable: true,
    fuelType: 'Dizel',
    transmission: 'Otomatik',
    condition: 'Yeni',
    rentalPeriod: 12,
    description: 'Son model Toyota Alphard, geniş iç hacmi ve lüks özellikleriyle uzun dönem kiralamalar için ideal bir seçimdir.',
    specifications: {
      motor: '2.5L',
      beygirGucu: '180HP',
      cekis: 'Önden',
      yakitTuketimi: '8.5L/100km',
      koltukSayisi: 7,
      bagajHacmi: '500L',
      renk: 'Beyaz'
    }
  },
  {
    id: 2,
    brand: 'Nissan',
    model: '370Z',
    year: 2021,
    price: 75,
    priceUnit: 'TL',
    image: '/assets/img/nissan-offer.png',
    features: ['Otomatik', '15kmpl', 'GPS Navigasyon', 'Klima'],
    type: 'Sports',
    isAvailable: true,
    fuelType: 'Benzin',
    transmission: 'Manuel',
    condition: '2. El',
    rentalPeriod: 6,
    description: 'Spor otomobil deneyimi arayanlar için Nissan 370Z, yüksek performans ve şık tasarımı bir arada sunuyor.',
    specifications: {
      motor: '3.7L V6',
      beygirGucu: '328HP',
      cekis: 'Arkadan',
      yakitTuketimi: '12.5L/100km',
      koltukSayisi: 2,
      bagajHacmi: '235L',
      renk: 'Kırmızı'
    }
  },
  {
    id: 3,
    brand: 'Audi',
    model: 'Q3',
    year: 2022,
    price: 45,
    priceUnit: 'TL',
    image: '/assets/img/audi-offer.png',
    features: ['Otomatik', '18kmpl', 'GPS Navigasyon', 'Klima'],
    type: 'SUV',
    isAvailable: true,
    fuelType: 'Dizel',
    transmission: 'Otomatik',
    condition: 'Yeni',
    rentalPeriod: 12,
    description: 'Kompakt SUV segmentinin en iyilerinden Audi Q3, lüks donanımları ve yakıt tasarrufu sağlayan motoruyla öne çıkıyor.',
    specifications: {
      motor: '2.0L TDI',
      beygirGucu: '150HP',
      cekis: '4x4',
      yakitTuketimi: '7.2L/100km',
      koltukSayisi: 5,
      bagajHacmi: '530L',
      renk: 'Gri'
    }
  },
  {
    id: 4,
    brand: 'BMW',
    model: 'X5',
    year: 2023,
    price: 85,
    priceUnit: 'TL',
    image: '/assets/img/bmw-offer.png',
    features: ['Otomatik', '16kmpl', 'GPS Navigasyon', 'Klima', 'Panoramik Tavan'],
    type: 'SUV',
    isAvailable: true,
    fuelType: 'Benzin',
    transmission: 'Otomatik',
    condition: 'Yeni',
    rentalPeriod: 12,
    description: 'Premium SUV segmentinin lider modellerinden BMW X5, yüksek konfor ve performansı bir arada sunuyor.',
    specifications: {
      motor: '3.0L',
      beygirGucu: '265HP',
      cekis: '4x4',
      yakitTuketimi: '9.5L/100km',
      koltukSayisi: 5,
      bagajHacmi: '650L',
      renk: 'Siyah'
    }
  },
  {
    id: 5,
    brand: 'Mercedes',
    model: 'E200',
    year: 2022,
    price: 65,
    priceUnit: 'TL',
    image: '/assets/img/marcedes-offer.png',
    features: ['Otomatik', '19kmpl', 'GPS Navigasyon', 'Klima', 'Deri Koltuk'],
    type: 'Sedan',
    isAvailable: true,
    fuelType: 'Benzin',
    transmission: 'Otomatik',
    condition: 'Yeni',
    rentalPeriod: 12,
    description: 'İş dünyasının tercihi Mercedes E200, konfor ve prestiji bir arada sunuyor.',
    specifications: {
      motor: '2.0L',
      beygirGucu: '184HP',
      cekis: 'Arkadan',
      yakitTuketimi: '8.0L/100km',
      koltukSayisi: 5,
      bagajHacmi: '540L',
      renk: 'Gümüş'
    }
  }
];

// Araç kategorileri
const mockCategories = [
  { id: 1, name: 'SUV', count: 3 },
  { id: 2, name: 'Sedan', count: 1 },
  { id: 3, name: 'Sports', count: 1 },
  { id: 4, name: 'Hatchback', count: 0 },
  { id: 5, name: 'Minivan', count: 0 }
];

// Araç markaları
const mockBrands = [
  { id: 1, name: 'Toyota', count: 1 },
  { id: 2, name: 'Nissan', count: 1 },
  { id: 3, name: 'Audi', count: 1 },
  { id: 4, name: 'BMW', count: 1 },
  { id: 5, name: 'Mercedes', count: 1 }
];

// Mock araçları getirme ve filtreleme
const mockGetVehicles = (filters = {}) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      let filteredVehicles = [...mockVehicles];
      
      // Filtreleme işlemleri
      if (filters.brand) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.brand.toLowerCase() === filters.brand.toLowerCase()
        );
      }
      
      if (filters.type) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.type.toLowerCase() === filters.type.toLowerCase()
        );
      }
      
      if (filters.minPrice) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.price >= parseInt(filters.minPrice)
        );
      }
      
      if (filters.maxPrice) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.price <= parseInt(filters.maxPrice)
        );
      }
      
      if (filters.fuelType) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.fuelType.toLowerCase() === filters.fuelType.toLowerCase()
        );
      }
      
      if (filters.transmission) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.transmission.toLowerCase() === filters.transmission.toLowerCase()
        );
      }
      
      if (filters.rentalPeriod) {
        filteredVehicles = filteredVehicles.filter(
          vehicle => vehicle.rentalPeriod <= parseInt(filters.rentalPeriod)
        );
      }
      
      // Araçları sıralama (varsayılan: fiyat artan)
      if (filters.sort) {
        switch (filters.sort) {
          case 'price_asc':
            filteredVehicles.sort((a, b) => a.price - b.price);
            break;
          case 'price_desc':
            filteredVehicles.sort((a, b) => b.price - a.price);
            break;
          case 'year_desc':
            filteredVehicles.sort((a, b) => b.year - a.year);
            break;
          default:
            filteredVehicles.sort((a, b) => a.price - b.price);
        }
      }
      
      resolve({
        data: {
          vehicles: filteredVehicles,
          total: filteredVehicles.length,
          page: parseInt(filters.page || 1),
          limit: parseInt(filters.limit || 10)
        }
      });
    }, 800);
  });
};

// Mock belirli bir aracın detaylarını getirme
const mockGetVehicleById = (id) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const vehicle = mockVehicles.find(v => v.id === parseInt(id));
      
      if (!vehicle) {
        return reject({ 
          response: { data: { message: 'Araç bulunamadı' } } 
        });
      }
      
      resolve({ data: vehicle });
    }, 600);
  });
};

// Mock araç kategorilerini getirme
const mockGetVehicleCategories = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ data: mockCategories });
    }, 500);
  });
};

// Mock araç markalarını getirme
const mockGetVehicleBrands = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ data: mockBrands });
    }, 500);
  });
};

// Mock araç modellerini markaya göre getirme
const mockGetVehicleModelsByBrand = (brand) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const models = mockVehicles
        .filter(v => v.brand.toLowerCase() === brand.toLowerCase())
        .map(v => ({ id: v.id, name: v.model }));
        
      resolve({ data: models });
    }, 500);
  });
};

// Kullanılacak servisleri belirleme (mock mu gerçek mi)
const vehiclesService = {
  getVehicles: useMockAPI ? mockGetVehicles : getVehicles,
  getVehicleById: useMockAPI ? mockGetVehicleById : getVehicleById,
  getVehicleCategories: useMockAPI ? mockGetVehicleCategories : getVehicleCategories,
  getVehicleBrands: useMockAPI ? mockGetVehicleBrands : getVehicleBrands,
  getVehicleModelsByBrand: useMockAPI ? mockGetVehicleModelsByBrand : getVehicleModelsByBrand,
};

export default vehiclesService; 