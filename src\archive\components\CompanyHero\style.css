@import url('../../styles/colors.css');

/* ===================== COMPANY HERO ======================*/
.company-hero-area {
  position: relative;
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
  padding: 60px 0;
  overflow: hidden;
  z-index: 1;
}

/* Hero içerik stilleri */
.company-hero-area .hero-content {
  padding: 20px 0;
  position: relative;
  z-index: 5;
  text-align: left;
  max-width: 800px;
}

.company-hero-area .hero-title {
  color: #fff;
  font-size: 36px;
  font-weight: 600;
  margin-bottom: 15px;
  font-family: "Poppins", sans-serif;
  letter-spacing: -0.5px;
  line-height: 1.2;
}

.company-hero-area .hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-bottom: 0;
  line-height: 1.6;
}

/* Minimal arka plan efekti */
.company-hero-area::before {
  content: "";
  position: absolute;
  top: -5%;
  right: -10%;
  width: 45%;
  height: 200%;
  background: rgba(255, 255, 255, 0.05);
  transform: rotate(30deg);
  z-index: 2;
}

.company-hero-area::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 100%);
  z-index: 2;
}

/* Responsive */
@media (max-width: 991px) {
  .company-hero-area .hero-title {
    font-size: 32px;
  }
  .company-hero-area .hero-subtitle {
    font-size: 15px;
  }
  .company-hero-area {
    padding: 50px 0;
  }
  .company-hero-area .hero-content {
    max-width: 100%;
    text-align: center;
    padding-bottom: 10px;
  }
}

@media (max-width: 767px) {
  .company-hero-area .hero-title {
    font-size: 28px;
  }
  .company-hero-area .hero-subtitle {
    font-size: 14px;
    margin-bottom: 5px;
  }
  .company-hero-area {
    padding: 40px 0;
  }
  .company-hero-area .hero-content {
    padding-bottom: 5px;
  }
}

@media (max-width: 576px) {
  .company-hero-area {
    padding: 30px 0;
  }
  .company-hero-area .hero-title {
    font-size: 24px;
  }
} 