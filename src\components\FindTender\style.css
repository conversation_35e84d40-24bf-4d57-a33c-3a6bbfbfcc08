@import url('../../styles/colors.css');

/* ===================== FIND CAR ======================*/



.find-box-wrapper {
  position: relative;
  margin-top: -80px;
  z-index: 99;
}

.find-box {
  background: #fff;
  padding: 15px;
  border-radius: 10px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.find-box::before {
  position: absolute;
  background: var(--primary-color) none repeat scroll 0 0;
  content: "";
  top: 0;
  left: 0;
  width: 30%;
  height: 100%;
  z-index: -1;
}

.find-box .row {
  margin-left: 0;
  margin-right: 0;
}

.find-text {
  color: #fff;
  padding: 20px;
}

.find-text h3 {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 0;
  margin-bottom: 10px;
  padding-bottom: 10px;
  position: relative;
  text-transform: capitalize;
  font-family: "Poppins", sans-serif;
}

.find-text p {
  margin-bottom: 0;
  font-size: 16px;
  line-height: 1.6;
}

.find-form {
  background: #fff;
  padding: 20px 15px;
  position: relative;
}

.find-form .form-group {
  margin-bottom: 15px;
}

.find-form .form-label {
  color: #555;
  font-weight: 500;
  margin-bottom: 5px;
  font-size: 14px;
}

.find-form .form-control {
  height: 45px;
  border-radius: 5px;
  border: 1px solid #e0e0e0;
  box-shadow: none;
  font-size: 15px;
  color: #333;
  transition: all 0.3s ease;
}

.find-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

.find-form .form-control:disabled {
  background-color: #f8f9fa;
  opacity: 0.7;
}

/* Responsive Adjustments */
@media (min-width: 992px) and (max-width: 1169px) {
  .find-text {
    padding: 15px;
  }
  
  .find-text h3 {
    font-size: 22px;
  }
  
  .find-form {
    padding: 15px 10px;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .find-box::before {
    width: 40%;
  }
  
  .find-text {
    padding: 15px 10px;
  }
  
  .find-text h3 {
    font-size: 20px;
  }
  
  .find-form {
    padding: 15px 10px;
  }
}

@media (max-width: 767px) {
  .find-box {
    margin-bottom: 100px;
  }
  
  .find-box::before {
    width: 100%;
    height: 200px;
  }
  
  .find-text {
    text-align: center;
    margin-bottom: 20px;
  }
  
  .find-text h3 {
    font-size: 22px;
    display: inline-block;
  }
  
  .find-text p {
    margin-bottom: 0;
  }
  
  .find-form {
    padding: 15px 0;
  }
  
  .find-form .form-control {
    height: 40px;
  }

}

@media only screen and (min-width: 480px) and (max-width: 767px) {
  .find-box {
    margin-bottom: 30px;
  }
  
  .find-text {
    padding-top: 40px;
  }
  
  .find-text h3 {
    font-size: 24px;
  }
}

/* ===================== FIND CAR FORM ======================*/
.find-form-wrapper {
  width: 100%;
  position: relative;
}

.location-input input,
.date-input input,
.time-input input {
  height: 50px;
  font-size: 16px;
  border: none;
  border-radius: 6px;
  padding-left: 15px;
}

.input-icon {
  background-color: #ffffff;
  border: none;
  color: var(--primary-color) !important;
  font-size: 18px;
}

.form-control-lg {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.form-control:focus {
  box-shadow: none;
  border-color: var(--primary-color);
}

.different-location-check {
  color: #fff;
  font-size: 14px;
}

.different-location-check .form-check-input {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  border-color: rgba(255, 255, 255, 0.5);
}

.different-location-check .form-check-input:checked {
  background-color: #ffc107;
  border-color: #ffc107;
}

.search-button {
  padding: 12px 25px;
  font-weight: 600;
  font-size: 16px;
  text-transform: uppercase;
  border-radius: 6px;
  background-color: #ffc107;
  border-color: #ffc107;
  color: #333;
  transition: all 0.3s ease;
}

.search-button:hover {
  background-color: #e0a800;
  border-color: #e0a800;
  transform: translateY(-2px);
}

.search-button:focus,
.search-button:active {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
  background-color: #e0a800;
  border-color: #e0a800;
}

/* Responsive */
@media (max-width: 991px) {
  .search-button {
    width: 100%;
    margin-top: 15px;
  }
}

@media (max-width: 767px) {
  .form-control-lg {
    font-size: 14px;
  }
  
  .location-input input,
  .date-input input,
  .time-input input {
    height: 45px;
  }
}

/* ===================== FIND CAR ======================*/
.search-container {
  position: relative;
  margin-top: -80px;
  z-index: 10;
  margin-bottom: 50px;
}

.search-box {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 30px;
  position: relative;
}

.form-control {
  height: 50px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  padding: 0 15px;
  font-size: 15px;
  color: #333;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

.form-control:disabled {
  background-color: #f8f9fa;
  opacity: 0.7;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}


/* Responsive Styles */
@media (max-width: 991px) {
  .search-container {
    margin-top: -60px;
  }
  
  .search-box {
    padding: 20px;
  }
  

}

@media (max-width: 767px) {
  .search-container {
    margin-top: -40px;
  }
  
  .search-box {
    padding: 15px;
  }
  
  .form-control {
    height: 45px;
    font-size: 14px;
  }
  
  .form-label {
    font-size: 13px;
  }
}

/*===================== FINDCAR AREA ======================*/
.findcar-area {
  margin-top: -80px;
  position: relative;
  z-index: 10;
}



.form-title {
  margin-bottom: 25px;
  font-size: 22px;
  font-weight: 600;
  color: #001238;
  text-align: center;
}

.form-row {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #001238;
}

.form-control {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  font-size: 14px;
  height: 45px;
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* Arama butonu container */
.search-btn-group {
  display: flex;
  justify-content: center;
  width: 100%;
}

.gauto-btn{
  border-color: #fff !important;
}
/* Responsive */
@media (max-width: 991px) {
  .findcar-area {
    margin-top: -60px;
  }
  
  .findcar-form {
    padding: 10px;
  }
}

@media (max-width: 767px) {
  .findcar-form {
    padding: 10px 10px;
  }
  
  .form-title {
    font-size: 18px;
    margin-bottom: 15px;
  }
  

  .findcar-area {
    margin-top: -40px;
  }
}

/* Yolcu360 stilindeki form düzeni */
.search-container {
  position: relative;
  margin-top: -100px;
  z-index: 20;
}

.search-box {
  background: #fff;
  border-radius: 5px;
  padding: 25px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* Form alanları düzeni */
.location-input {
  position: relative;
  margin-bottom: 15px;
}

.location-input input, 
.date-input input,
.time-input select {
  height: 50px;
  padding-left: 40px;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  font-size: 14px;
  width: 100%;
}

/* Icon konumlandırma */
.input-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
}

/* Tarih ve zaman girişi */
.date-picker-container,
.time-picker-container {
  position: relative;
  margin-bottom: 15px;
}

/* Arama butonu container */
.search-action {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

/* Responsive */
@media (max-width: 767px) {
  .findcar-form {
    padding: 10px;
  }
  
  .form-title {
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  
  .search-container {
    margin-top: -60px;
  }
  
  .search-box {
    padding: 15px;
  }
  
  .location-input input, 
  .date-input input,
  .time-input select {
    height: 45px;
    font-size: 13px;
  }
}

/* Yeni Arama Kutusu Stilleri */
.search-box-wrapper {
  position: relative;
  z-index: 10;
}

.location-field .form-label {
  font-weight: 500;
}

.advanced-search {
  border-top: 1px solid #eee;
  padding-top: 15px;
  margin-top: 15px;
}

/* Icon Stilleri */
.form-label svg {
  color: var(--primary-color);
  vertical-align: middle;
  margin-right: 5px;
}

/* Form Field Wrapper ve Icon Stilleri */
.form-field-wrapper {
  position: relative;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
  display: flex;
  align-items: center;
}

.form-field-wrapper:hover {
  box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.15);
  border-color: var(--primary-color);
}

.field-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
  font-size: 16px;
}

.form-select-with-icon {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  height: 45px;
  font-size: 14px;
  color: #555;
  background-color: #fff;
  transition: all 0.3s ease;
  padding-left: 35px;
  width: 100%;
}

.form-select-with-icon:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.1);
}

.form-select-with-icon:disabled {
  background-color: #f5f5f5;
  opacity: 0.7;
  cursor: not-allowed;
}

/* Arama Buton Container */
.search-btn-container {
  border-color: #fff !important;
  display: flex;
  justify-content: flex-end;
  width: auto;
}

/* Mobil Responsive Stilleri */
@media (max-width: 991px) {
  .search-box-wrapper {
    padding: 15px;
  }
  
  .form-field-wrapper {
    margin-bottom: 10px;
  }
}

@media (max-width: 767px) {
  .search-box-wrapper {
    padding: 10px;
    margin-top: -30px;
  }
  
  .form-select-with-icon {
    height: 40px;
    font-size: 13px;
  }
}

.ipo-area:before {
  position: absolute;
  content: "";
  top: 0;
  width: 50%;
  height: 100%;
  right: -65px;
  z-index: -1;
  -webkit-transform: skewX(40deg);
  transform: skewX(40deg);
  -webkit-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  background: var(--primary-color) none repeat scroll 0 0;
}

.ipo-select-form select:focus,
.ipo-date-form input:focus,
.ipo-location-form input:focus {
  border-color: var(--primary-color);
}

.react-datepicker__day--selected,
.react-datepicker__day--keyboard-selected {
  color: var(--primary-color) !important;
  background-color: transparent !important;
  font-weight: 700 !important;
}

.react-datepicker__day:hover,
.react-datepicker__month-text:hover,
.react-datepicker__quarter-text:hover,
.react-datepicker__year-text:hover {
  border-color: var(--primary-color) !important;
}

.pac-item:hover {
  background-color: #f5f5f5;
  border-color: var(--primary-color);
}

.Toastify__progress-bar--info {
  border-color: var(--primary-color);
}

.find-form .error-text {
  color: var(--primary-color);
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.ipo-form-btn:hover {
  border-color: var(--primary-color);
}
