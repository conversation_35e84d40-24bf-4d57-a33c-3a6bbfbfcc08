import React, { Fragment, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { Container, Row, Col, Card, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner } from "react-bootstrap";
import { FaUpload, FaFileAlt, FaCheck, FaExclamationTriangle, FaShieldAlt } from "react-icons/fa";
import { useNavigate } from "react-router-dom";
import PageTitle from "../../components/PageTitle";
import { verifyUserAccount } from "../../redux/slices/authSlice";
import "./style.css";

const VerificationPage = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { user, loading, error, verificationStatus } = useSelector((state) => state.auth);
  
  const [verificationData, setVerificationData] = useState({
    taxDocument: null,
    findexChecked: false,
  });
  const [taxDocumentName, setTaxDocumentName] = useState('');
  const [uploadError, setUploadError] = useState('');
  
  // Doğrulama durumunu kontrol et
  if (user?.isVerified) {
    return (
      <Fragment>
        <PageTitle
          pageTitle={t("verification_page.title")}
          pagesub={t("verification_page.subtitle")}
        />
        <section className="verification-area section_70">
          <Container>
            <Row className="justify-content-center">
              <Col lg={8}>
                <Card className="verification-card already-verified">
                  <Card.Body className="text-center p-5">
                    <div className="verified-icon">
                      <FaCheck />
                    </div>
                    <h2>{t("verification_page.already_verified")}</h2>
                    <p>{t("verification_page.verified_account_description")}</p>
                    <Button 
                      variant="primary" 
                      className="mt-4"
                      onClick={() => navigate('/profile')}
                    >
                      {t("verification_page.back_to_profile")}
                    </Button>
                  </Card.Body>
                </Card>
              </Col>
            </Row>
          </Container>
        </section>
      </Fragment>
    );
  }
  
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    
    if (file) {
      if (file.type !== 'application/pdf') {
        setUploadError(t("verification_page.pdf_only"));
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) { // 5MB
        setUploadError(t("verification_page.file_too_large"));
        return;
      }
      
      setVerificationData({
        ...verificationData,
        taxDocument: file
      });
      setTaxDocumentName(file.name);
      setUploadError('');
    }
  };
  
  const handleFindexCheck = (e) => {
    setVerificationData({
      ...verificationData,
      findexChecked: e.target.checked
    });
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validasyon
    if (!verificationData.taxDocument) {
      setUploadError(t("verification_page.tax_document_required"));
      return;
    }
    
    if (!verificationData.findexChecked) {
      setUploadError(t("verification_page.findex_check_required"));
      return;
    }
    
    // Vergi levhası için dosya nesnesi yerine base64 dönüşümü yapılabilir
    // Gerçek bir API'da FormData kullanılacaktır
    // Burada demo için mock veri gönderiyoruz
    const formData = {
      taxDocument: taxDocumentName, // Gerçek bir API için dosya yüklenecek
      findexConsent: true
    };
    
    dispatch(verifyUserAccount(formData));
  };
  
  return (
    <Fragment>
      <PageTitle
        pageTitle={t("verification_page.title")}
        pagesub={t("verification_page.subtitle")}
      />
      <section className="verification-area section_70">
        <Container>
          <Row className="justify-content-center">
            <Col lg={10}>
              {error && (
                <Alert variant="danger" dismissible>
                  {error}
                </Alert>
              )}
              
              {verificationStatus === 'success' && (
                <Alert variant="success" dismissible>
                  {t("verification_page.success_message")}
                </Alert>
              )}
              
              <Card className="verification-card">
                <Card.Header className="d-flex align-items-center">
                  <h3><FaShieldAlt className="me-2" /> {t("verification_page.verification_process")}</h3>
                </Card.Header>
                
                <Card.Body>
                  <div className="verification-info mb-4">
                    <Alert variant="info">
                      <h5>{t("verification_page.why_verify")}</h5>
                      <p>{t("verification_page.verification_description")}</p>
                    </Alert>
                  </div>
                  
                  <Form onSubmit={handleSubmit}>
                    <Row>
                      <Col md={12}>
                        <div className="document-upload-section mb-4">
                          <h4>{t("verification_page.tax_document")}</h4>
                          <p>{t("verification_page.tax_document_description")}</p>
                          
                          <div className="upload-box">
                            <input
                              type="file"
                              id="taxDocument"
                              accept=".pdf"
                              onChange={handleFileChange}
                              className="file-input"
                            />
                            <label htmlFor="taxDocument" className="upload-label">
                              <FaUpload className="upload-icon" />
                              <span>{t("verification_page.select_tax_document")}</span>
                            </label>
                            
                            {taxDocumentName && (
                              <div className="selected-file">
                                <FaFileAlt className="me-2" />
                                {taxDocumentName}
                              </div>
                            )}
                            
                            {uploadError && (
                              <div className="text-danger mt-2">
                                <FaExclamationTriangle className="me-2" />
                                {uploadError}
                              </div>
                            )}
                          </div>
                        </div>
                      </Col>
                      
                      <Col md={12}>
                        <div className="findex-section mb-4">
                          <h4>{t("verification_page.findex_report")}</h4>
                          <p>{t("verification_page.findex_description")}</p>
                          
                          <Alert variant="warning" className="d-flex align-items-center">
                            <FaExclamationTriangle className="me-3 warning-icon" />
                            <div>
                              {t("verification_page.findex_warning")}
                            </div>
                          </Alert>
                          
                          <Form.Check 
                            type="checkbox"
                            id="findexConsent"
                            className="mt-3 findex-checkbox"
                            label={t("verification_page.findex_consent")}
                            checked={verificationData.findexChecked}
                            onChange={handleFindexCheck}
                          />
                        </div>
                      </Col>
                    </Row>
                    
                    <div className="text-center mt-4">
                      <Button 
                        variant="primary" 
                        type="submit"
                        className="verification-submit-btn"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <Spinner as="span" animation="border" size="sm" className="me-2" />
                            {t("verification_page.processing")}
                          </>
                        ) : (
                          <>
                            <FaShieldAlt className="me-2" /> {t("verification_page.submit_verification")}
                          </>
                        )}
                      </Button>
                    </div>
                    
                    <div className="text-center mt-3">
                      <Button 
                        variant="outline-secondary" 
                        onClick={() => navigate('/profile')}
                      >
                        {t("verification_page.back_to_profile")}
                      </Button>
                    </div>
                  </Form>
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </Container>
      </section>
    </Fragment>
  );
};

export default VerificationPage; 