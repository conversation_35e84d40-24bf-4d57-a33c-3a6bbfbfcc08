# Long Term Rental Frontend

Bu proje uzun dönem araç kiralama hizmeti sunan bir platformun frontend uygulamasıdır.

## Teknoloji Stack'i

- **Framework**: React JS (18.2.0)
- **State Yönetimi**: Redux Toolkit
- **Routing**: React Router v6.3.0
- **Stil**: Bootstrap 5.2.0 ve özel CSS
- **Çoklu Dil Desteği**: i18next

## Proje Yapısı

Proje, standart bir React uygulaması olarak organize edilmiştir:

```
LongTermRental-Frontend/
│
├── public/               # Statik dosyalar
│   ├── assets/           # Dil dosyaları ve diğer varlıklar
│   │   └── locales/      # Çoklu dil JSON dosyaları
│   ├── index.html        # Ana HTML dosyası
│   └── favicon.ico       # Site favicon'u
│
├── src/                  # Kaynak kod
│   ├── components/       # Atomik bileşenler
│   ├── main-component/   # Sayfa bileşenleri
│   ├── redux/            # Redux state yönetimi
│   ├── services/         # API servisleri
│   ├── constants/        # Sabit değerler
│   ├── img/              # Görsel dosyalar
│   ├── index.js          # Ana giriş noktası
│   └── index.css         # Global stiller
│
├── package.json          # Bağımlılıklar ve scriptler
├── CHANGELOG.md          # Değişiklik günlüğü
└── README.md             # Bu dosya
```

## Bileşen Organizasyonu

1. **components/**: Yeniden kullanılabilir atomik bileşenler
2. **main-component/**: Sayfa düzenindeki bileşenler

## Çalıştırma

Projeyi çalıştırmak için:

```bash
# Bağımlılıkları yükle
npm install

# Geliştirme sunucusunu başlat
npm start

# Üretim için build oluştur
npm run build
```

## Son Değişiklikler

Proje yapısı yakın zamanda düzenlendi:
- Package.json dosyaları tek ve merkezi hale getirildi
- React uygulaması kök dizinden çalışacak şekilde yapılandırıldı

Daha fazla detay için `CHANGELOG.md` dosyasını inceleyebilirsiniz.

## CSS Renk Sistemi

Proje, merkezi bir renk yönetim sistemi kullanmaktadır. Tüm renk tanımlamaları `src/styles/colors.css` dosyasında yapılır ve CSS değişkenleri kullanılarak uygulanır.

### Renk Değişikliği Yapma

Eğer müşteri veya proje yönetimi renk değişikliği talep ederse, sadece `src/styles/colors.css` dosyasındaki ilgili değişkenleri güncellemek yeterlidir. Diğer dosyalarda değişiklik yapılmasına gerek yoktur.

Detaylı bilgi için [CONTRIBUTING.md](./CONTRIBUTING.md) dosyasına bakınız. 