import React, { Fragment } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Header from "../Header";
import Footer from "../Footer";
import { Helmet } from 'react-helmet-async';
import { PAGE_SEO_META, LANGUAGES } from "../common/SEOMeta/defaultMetaTags";
import "./style.css";

const AppLayout = () => {
  const location = useLocation();
  const path = location.pathname;
  
  // Mevcut dilin tespiti
  const getCurrentLanguage = () => {
    // Path'den dil kodunu çıkar (örn: "/en/about" -> "en")
    const pathSegments = path.split('/').filter(segment => segment);
    const langInPath = pathSegments[0];
    
    // Geçerli bir dil kodu mu kontrol et
    const isValidLang = LANGUAGES.some(lang => lang.code === langInPath);
    if (isValidLang) return langInPath;
    
    // Varsayılan olarak Türkçe
    return "tr";
  };
  
  const currentLang = getCurrentLanguage();
  
  // Basit bir yol-SEO eşleştirme fonksiyonu
  const getSeoForPath = (path) => {
    if (path === '/' || path === '/home') return PAGE_SEO_META.home;
    if (path.includes('/about')) return PAGE_SEO_META.about;
    if (path.includes('/service')) return PAGE_SEO_META.service;
    if (path.includes('/contact')) return PAGE_SEO_META.contact;
    if (path.includes('/blog')) return PAGE_SEO_META.blog;
    if (path.includes('/car-listing')) return PAGE_SEO_META.cars;
    if (path.includes('/tender')) return PAGE_SEO_META.tender;
    if (path.includes('/dashboard')) return PAGE_SEO_META.dashboard;
    if (path.includes('/profile')) return PAGE_SEO_META.profile;
    if (path.includes('/login')) return PAGE_SEO_META.login;
    if (path.includes('/register')) return PAGE_SEO_META.register;
    // Diğer sayfalar için özel SEO bilgileri eklenebilir
    
    // Varsayılan SEO bilgileri
    return {
      title: "İhaleden Kirala - İhale ile Araç Kiralama",
      description: "İhale sistemi ile uzun dönem araç kiralama, filo kiralama ve kurumsal araç kiralama çözümleri.",
      keywords: "ihale ile araç kiralama, ihaleden araba kiralama, uzun dönem filo kiralama, kurumsal araç kiralama"
    };
  };
  
  const seo = getSeoForPath(path);
  const siteUrl = "https://www.ihaledenkirala.com";
  const canonicalUrl = siteUrl + path;
  
  // Alt sayfalar için alternatif dil URL'leri oluştur
  const getAlternateUrls = () => {
    // Path'i parçalara ayır
    const pathSegments = path.split('/').filter(segment => segment);
    let pathWithoutLang = path;
    
    // İlk segment dil kodu ise, bunu kaldır
    const firstSegment = pathSegments[0];
    const isLangCode = LANGUAGES.some(lang => lang.code === firstSegment);
    if (isLangCode) {
      pathWithoutLang = '/' + pathSegments.slice(1).join('/');
    }
    
    // Tüm diller için alternatif URL'ler oluştur
    return LANGUAGES.map(lang => {
      const langPrefix = lang.code === 'tr' ? '' : `/${lang.code}`;
      return {
        hrefLang: lang.locale,
        href: `${siteUrl}${langPrefix}${pathWithoutLang}`
      };
    });
  };
  
  const alternateUrls = getAlternateUrls();
  
  // Sayfa türüne göre uygun Schema.org yapısal verisi
  const getStructuredData = () => {
    // Temel web sitesi bilgileri
    const websiteSchema = {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "url": siteUrl,
      "name": "İhaleden Kirala",
      "description": "İhale sistemi ile uzun dönem araç kiralama platformu. En uygun fiyatları rekabetçi tekliflerle alın.",
      "potentialAction": {
        "@type": "SearchAction",
        "target": `${siteUrl}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      }
    };
    
    // Ana sayfa için
    if (path === '/' || path === '/home') {
      return [
        websiteSchema,
        {
          "@context": "https://schema.org",
          "@type": "Organization",
          "url": siteUrl,
          "logo": `${siteUrl}/assets/images/logo.png`,
          "name": "İhaleden Kirala",
          "slogan": "Türkiye'nin İlk İhale Bazlı Araç Kiralama Platformu",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+90-************",
            "contactType": "customer service",
            "availableLanguage": ["Turkish", "English"]
          },
          "sameAs": [
            "https://www.facebook.com/ihaledenkirala",
            "https://www.twitter.com/ihaledenkirala",
            "https://www.linkedin.com/company/ihaledenkirala",
            "https://www.instagram.com/ihaledenkirala"
          ]
        }
      ];
    }
    
    // Hakkımızda sayfası için
    if (path.includes('/about')) {
      return [
        websiteSchema,
        {
          "@context": "https://schema.org",
          "@type": "AboutPage",
          "mainEntity": {
            "@type": "Organization",
            "name": "İhaleden Kirala",
            "description": seo.description,
            "foundingDate": "2023",
            "founder": {
              "@type": "Person",
              "name": "İhale Bazlı Araç Kiralama Uzmanları"
            }
          }
        }
      ];
    }
    
    // İletişim sayfası için
    if (path.includes('/contact')) {
      return [
        websiteSchema,
        {
          "@context": "https://schema.org",
          "@type": "ContactPage",
          "mainEntity": {
            "@type": "Organization",
            "name": "İhaleden Kirala",
            "telephone": "+90-************",
            "email": "<EMAIL>",
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "Atatürk Bulvarı No: 123",
              "addressLocality": "İstanbul",
              "postalCode": "34000",
              "addressCountry": "TR"
            }
          }
        }
      ];
    }
    
    // Blog sayfası için
    if (path.includes('/blog') && !path.includes('/blog-single')) {
      return [
        websiteSchema,
        {
          "@context": "https://schema.org",
          "@type": "Blog",
          "headline": seo.title,
          "description": seo.description,
          "publisher": {
            "@type": "Organization",
            "name": "İhaleden Kirala",
            "logo": {
              "@type": "ImageObject",
              "url": `${siteUrl}/assets/images/logo.png`
            }
          }
        }
      ];
    }
    
    // Araç listesi sayfası için
    if (path.includes('/car-listing')) {
      return [
        websiteSchema,
        {
          "@context": "https://schema.org",
          "@type": "ItemList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "item": {
                "@type": "Product",
                "name": "İhale ile Araç Kiralama Hizmeti",
                "description": "Kurumsal müşteriler için özel filo kiralama ve ihale bazlı araç kiralama hizmetleri",
                "offers": {
                  "@type": "AggregateOffer",
                  "lowPrice": "1000",
                  "highPrice": "5000",
                  "priceCurrency": "TRY",
                  "offerCount": "10"
                }
              }
            }
          ]
        }
      ];
    }
    
    // İhale sayfası için özel schema
    if (path.includes('/tender')) {
      return [
        websiteSchema,
        {
          "@context": "https://schema.org",
          "@type": "Service",
          "serviceType": "Araç Kiralama İhalesi",
          "provider": {
            "@type": "Organization",
            "name": "İhaleden Kirala",
            "logo": `${siteUrl}/assets/images/logo.png`
          },
          "description": "İhale bazlı araç kiralama sistemi ile rekabetçi fiyatlarla uzun dönem araç kiralama hizmeti",
          "offers": {
            "@type": "Offer",
            "priceCurrency": "TRY",
            "availability": "https://schema.org/InStock",
            "url": `${siteUrl}/tender`
          }
        }
      ];
    }
    
    // Varsayılan olarak temel web sitesi bilgilerini döndür
    return [websiteSchema];
  };

  return (
    <Fragment>
      <Helmet>
        <meta charSet="utf-8" />
        <title>{seo.title}</title>
        <meta name="description" content={seo.description} />
        <meta name="keywords" content={seo.keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="canonical" href={canonicalUrl} />
        
        {/* Alternatif dil URL'leri */}
        {alternateUrls.map((alt, index) => (
          <link key={index} rel="alternate" hrefLang={alt.hrefLang} href={alt.href} />
        ))}
        <link rel="alternate" hrefLang="x-default" href={`${siteUrl}${path}`} />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:url" content={canonicalUrl} />
        <meta property="og:site_name" content="İhaleden Kirala" />
        <meta property="og:locale" content={currentLang === 'tr' ? 'tr_TR' : 'en_US'} />
        <meta property="og:title" content={seo.ogTitle || seo.title} />
        <meta property="og:description" content={seo.ogDescription || seo.description} />
        {seo.ogImage && <meta property="og:image" content={siteUrl + seo.ogImage} />}
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:url" content={canonicalUrl} />
        <meta name="twitter:title" content={seo.ogTitle || seo.title} />
        <meta name="twitter:description" content={seo.ogDescription || seo.description} />
        {seo.ogImage && <meta name="twitter:image" content={siteUrl + seo.ogImage} />}
        
        {/* Temel meta etiketleri */}
        <meta name="robots" content={seo.robots || "index, follow"} />
        <meta name="author" content="İhaleden Kirala" />
        <meta name="language" content={currentLang === 'tr' ? 'Turkish' : 'English'} />
        
        {/* Mobil uyumluluk */}
        <meta name="format-detection" content="telephone=no" />
        <meta name="theme-color" content="#FF6200" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        
        {/* JSON-LD Yapısal Veri */}
        <script type="application/ld+json">
          {JSON.stringify(getStructuredData())}
        </script>
      </Helmet>
      <Header />
      <main className="app-main">
        <Outlet />
      </main>
      <Footer />
    </Fragment>
  );
};

export default AppLayout; 